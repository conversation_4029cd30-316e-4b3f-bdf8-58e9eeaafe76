using Dxf.Core.Extensions;
using IxMilia.Dxf.Entities;

namespace Dxf.Core.Tests.Extensions.DxfEntityCollectionsExtensions;

[Trait("DxfEntityCollectionsExtensions", "GetMaterialSearchIdentifiersTests")]
public class GetMaterialSearchIdentifiersTests
{
    [Fact(DisplayName = "When entities are empty, should return empty list")]
    public void WhenEntitiesAreEmpty_ShouldReturnEmptyList()
    {
        var entities = new List<DxfEntity>();

        var result = entities.GetMaterialSearchIdentifiers();

        result.Should().BeEmpty();
    }

    [Fact(DisplayName = "When no valid layers exist, should return empty list")]
    public void WhenNoValidLayersExist_ShouldReturnEmptyList()
    {
        var entities = new List<DxfEntity>
        {
            new DxfLine { Layer = "0" },
            new DxfLine { Layer = "InvalidLayer" }
        };

        var result = entities.GetMaterialSearchIdentifiers();

        result.Should().BeEmpty();
    }

    [Fact(DisplayName =
        "When valid layers exist, should return unique identifiers")]
    public void WhenValidLayersExist_ShouldReturnUniqueIdentifiers()
    {
        var entities = new List<DxfEntity>
        {
            new DxfLine { Layer = "1" },
            new DxfLine { Layer = "2" },
            new DxfLine { Layer = "1" } // Duplicate layer
        };

        var result = entities.GetMaterialSearchIdentifiers();

        result.Should().BeEquivalentTo(new List<int> { 1, 2 });
    }

    [Fact(DisplayName = "When mixed layers exist, should ignore invalid layers")]
    public void WhenMixedLayersExist_ShouldIgnoreInvalidLayers()
    {
        var entities = new List<DxfEntity>
        {
            new DxfLine { Layer = "1" },
            new DxfLine { Layer = "InvalidLayer" },
            new DxfLine { Layer = "2" }
        };

        var result = entities.GetMaterialSearchIdentifiers();

        result.Should().BeEquivalentTo(new List<int> { 1, 2 });
    }
}