using Coordinate.Core.Classes;
using FluentValidation;

namespace Coordinate.Core.Model._Shared
{
    public class UtmRequestValidator : AbstractValidator<Utm>
    {
        public UtmRequestValidator()
        {
            RuleLevelCascadeMode = CascadeMode.Stop;

            RuleFor(x => x.ZoneNumber)
                .NotEmpty()
                .InclusiveBetween(1, 60)
                .WithMessage("The zone number must be between 1 and 60.");

            RuleFor(x => x.ZoneLetter.ToString())
                .NotEmpty()
                .Matches(@"^[c-xC-X]$")
                .WithMessage("The zone letter must be a character between C and X.");
        }
    }
}
