using Coordinate.Core.Classes;
using FluentValidation;

namespace Coordinate.Core.Model._Shared
{
    public class CoordinateSettingRequestValidator : AbstractValidator<CoordinateSetting>
    {
        private readonly SystemsRequestValidator _systemsValidator = new();

        public CoordinateSettingRequestValidator()
        {
            RuleLevelCascadeMode = CascadeMode.Stop;

            RuleFor(x => x.Format).IsInEnum();

            RuleFor(x => x.Datum).IsInEnum();

            RuleFor(x => x.Systems)
              .NotNull()
              .SetValidator(_systemsValidator);

            RuleFor(x => x)
                .Must(c => c.Systems.SystemsAreEquivalent(c.Datum))
                .WithMessage("Coordinates are not equivalent.");
        }
    }
}
