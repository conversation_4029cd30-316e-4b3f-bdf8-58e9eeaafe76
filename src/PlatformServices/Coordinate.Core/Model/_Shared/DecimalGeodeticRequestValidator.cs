using Coordinate.Core.Classes;
using FluentValidation;

namespace Coordinate.Core.Model._Shared
{
    public class DecimalGeodeticRequestValidator : AbstractValidator<DecimalGeodetic>
    {
        public DecimalGeodeticRequestValidator()
        {
            RuleLevelCascadeMode = CascadeMode.Stop;

            RuleFor(x => x.Latitude)
                .InclusiveBetween(-90, 90)
                .WithMessage("The latitude must be between -90 and 90.");

            RuleFor(x => x.Longitude)
                .InclusiveBetween(-180, 180)
                .WithMessage("The longitude must be between -180 and 180.");
        }
    }
}
