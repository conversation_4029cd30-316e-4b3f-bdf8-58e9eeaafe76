using Coordinate.Core.Classes;
using FluentValidation;

namespace Coordinate.Core.Model._Shared
{
    public class SystemsRequestValidator : AbstractValidator<Systems>
    {
        private readonly UtmRequestValidator _utmValidator = new();
        private readonly DecimalGeodeticRequestValidator _decimalValidator = new();


        public SystemsRequestValidator()
        {
            RuleLevelCascadeMode = CascadeMode.Stop;

            RuleFor(x => x.Utm)
                .NotNull()
                .SetValidator(_utmValidator);

            RuleFor(x => x.DecimalGeodetic)
                .NotNull()
                .SetValidator(_decimalValidator);
        }
    }
}
