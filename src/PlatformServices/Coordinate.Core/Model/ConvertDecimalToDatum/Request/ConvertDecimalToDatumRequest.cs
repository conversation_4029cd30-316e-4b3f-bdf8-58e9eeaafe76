using Coordinate.Core.Classes;
using Coordinate.Core.Enums;
using System.Text.Json.Serialization;

namespace Coordinate.Core.Model.ConvertDecimalToDatum.Request
{
    public sealed record ConvertDecimalToDatumRequest
    {
        [JsonPropertyName("decimal_geodetic")]
        public DecimalGeodetic DecimalGeodetic { get; set; } = new();

        [JsonPropertyName("input_datum")]
        public Datum InputDatum { get; set; }

        [JsonPropertyName("output_datum")]
        public Datum OutputDatum { get; set; }
    }
}
