using Coordinate.Core.Model._Shared;
using FluentValidation;

namespace Coordinate.Core.Model.ConvertDecimalToDatum.Request
{
    public class ConvertDecimalToDatumRequestValidator : AbstractValidator<ConvertDecimalToDatumRequest>
    {
        private readonly DecimalGeodeticRequestValidator _geodeticValidator = new();

        public ConvertDecimalToDatumRequestValidator()
        {
            RuleLevelCascadeMode = CascadeMode.Stop;

            RuleFor(x => x.InputDatum).IsInEnum();

            RuleFor(x => x.OutputDatum).IsInEnum();

            RuleFor(x => x.DecimalGeodetic)
                .NotNull()
                .SetValidator(_geodeticValidator);
        }
    }
}
