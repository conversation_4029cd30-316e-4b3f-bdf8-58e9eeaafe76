using Coordinate.Core.Model._Shared;
using FluentValidation;

namespace Coordinate.Core.Model.ConvertUtmToDecimal.Request
{
    public class ConvertUtmToDecimalRequestValidator : AbstractValidator<ConvertUtmToDecimalRequest>
    {
        private readonly UtmRequestValidator _utmValidator = new();

        public ConvertUtmToDecimalRequestValidator()
        {
            RuleLevelCascadeMode = CascadeMode.Stop;

            RuleFor(x => x.Datum).IsInEnum();

            RuleFor(x => x.Utm)
                .NotNull()
                .SetValidator(_utmValidator);
        }
    }
}
