using Coordinate.Core.Model._Shared;
using FluentValidation;

namespace Coordinate.Core.Model.ConvertDecimalToUtm.Request
{
    public class ConvertDecimalToUtmRequestValidator : AbstractValidator<ConvertDecimalToUtmRequest>
    {
        private readonly DecimalGeodeticRequestValidator _geodeticValidator = new();


        public ConvertDecimalToUtmRequestValidator()
        {
            RuleLevelCascadeMode = CascadeMode.Stop;

            RuleFor(x => x.Datum).IsInEnum();

            RuleFor(x => x.DecimalGeodetic)
                .NotNull()
                .SetValidator(_geodeticValidator);
        }
    }
}
