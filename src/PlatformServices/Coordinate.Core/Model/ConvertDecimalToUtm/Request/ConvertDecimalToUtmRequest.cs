using Coordinate.Core.Classes;
using Coordinate.Core.Enums;
using System.Text.Json.Serialization;

namespace Coordinate.Core.Model.ConvertDecimalToUtm.Request
{
    public sealed record ConvertDecimalToUtmRequest
    {
        [JsonPropertyName("decimal_geodetic")]
        public DecimalGeodetic DecimalGeodetic { get; set; } = new();

        [JsonPropertyName("datum")]
        public Datum Datum { get; set; }
    }
}
