using Coordinate.Core.Enums;
using System.ComponentModel;
using System.Text.Json.Serialization;

namespace Coordinate.Core.Classes
{
    public sealed record CoordinateSetting
    {
        [JsonPropertyName("datum")]
        public Datum Datum { get; set; }

        [DisplayName("Formato")]
        [JsonPropertyName("coordinate_format")]
        public Format Format { get; set; }

        [DisplayName("Sistemas")]
        [JsonPropertyName("coordinate_systems")]
        public Systems Systems { get; set; } = new();
    }
}
