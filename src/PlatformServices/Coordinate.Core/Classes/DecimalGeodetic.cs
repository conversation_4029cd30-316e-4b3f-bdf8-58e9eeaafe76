using System.Text.Json.Serialization;

namespace Coordinate.Core.Classes
{
    public sealed record DecimalGeodetic
    {
        private double _latitude;

        [JsonPropertyName("latitude")]
        public double Latitude
        {
            get
            {
                return _latitude;
            }
            set
            {
                _latitude = Math.Round(value, 10);
            }
        }

        private double _longitude;

        [JsonPropertyName("longitude")]
        public double Longitude
        {
            get
            {
                return _longitude;
            }
            set
            {
                _longitude = Math.Round(value, 10);
            }
        }
    }
}
