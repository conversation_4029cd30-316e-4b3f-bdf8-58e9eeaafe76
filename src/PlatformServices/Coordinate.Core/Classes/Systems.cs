using Coordinate.Core.Enums;
using System.ComponentModel;
using System.Text.Json.Serialization;

namespace Coordinate.Core.Classes
{
    public sealed record Systems
    {
        [JsonPropertyName("utm")]
        [DisplayName("UTM")]
        public Utm Utm { get; set; } = new();

        [JsonPropertyName("decimal_geodetic")]
        [DisplayName("Decimal Geodésica")]
        public DecimalGeodetic DecimalGeodetic { get; set; } = new();

        public void ConvertSystems(Datum inputDatum, Datum outputDatum)
        {
            Utm = Helper.ToDatum(inputDatum, outputDatum, Utm);
            DecimalGeodetic = Helper.ToDatum(inputDatum, outputDatum, DecimalGeodetic);
        }

        public bool SystemsAreEquivalent(Datum datum)
        {
            if (Utm == null || DecimalGeodetic == null)
            {
                throw new ArgumentNullException("It is not possible to check the equivalence of coordinates when one of the systems is null.");
            }

            var utmConvertedToDecimal = Helper.ConvertUTMToDecimalGeodetic(Utm, datum);
            var decimalConvertedToUtm = Helper.ConvertDecimalGeodeticToUTM(DecimalGeodetic, datum);

            if ((utmConvertedToDecimal.Latitude == DecimalGeodetic.Latitude
                && utmConvertedToDecimal.Longitude == DecimalGeodetic.Longitude)
                || (
                    decimalConvertedToUtm.Easting == Utm.Easting
                    && decimalConvertedToUtm.Northing == Utm.Northing
                    && decimalConvertedToUtm.ZoneLetter == Utm.ZoneLetter
                    && decimalConvertedToUtm.ZoneNumber == Utm.ZoneNumber
                ))
            {
                return true;
            }

            return false;
        }
    }
}
