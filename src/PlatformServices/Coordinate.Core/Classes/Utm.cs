using Geometry.Core;
using System.ComponentModel;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;

namespace Coordinate.Core.Classes
{
    public sealed record Utm
    {
        private int _zoneNumber;

        [Description("Número da Zona")]
        [JsonPropertyName("zone_number")]
        public int ZoneNumber
        {
            get
            {
                return _zoneNumber;
            }
            set
            {
                _zoneNumber = value;
            }
        }

        private char _zoneLetter;

        [Description("Letra da Zona")]
        [JsonPropertyName("zone_letter")]
        public char ZoneLetter
        {
            get
            {
                return _zoneLetter;
            }
            set
            {
                if (!Regex.IsMatch(value.ToString(), @"^[a-zA-Z]$"))
                {
                    throw new ArgumentOutOfRangeException("ZoneLetter", "The zone letter must be a character between A and Z.");
                }

                _zoneLetter = value;
            }
        }

        [Description("Norte")]
        [JsonPropertyName("northing")]
        public double Northing { get; set; }

        [Description("Leste")]
        [JsonPropertyName("easting")]
        public double Easting { get; set; }
        public PointD ToPointD()
        {
            return new PointD(Easting, Northing);
        }

        public string GetFormatedZone()
        {
            return $"{ZoneNumber}{ZoneLetter}";
        }
    }
}