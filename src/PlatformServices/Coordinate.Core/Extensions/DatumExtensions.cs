using Coordinate.Core.Enums;

namespace Coordinate.Core.Extensions
{
    /// <summary>
    /// Extension methods to get terrestrial ellipsoid information at different data
    /// Ref.: SNYDER, <PERSON>.Map projections--A working manual. US Government Printing Office, 1987.
    /// </summary>
    public static class DatumExtensions
    {
        public static double EarthEllipsoidRadius(this Datum datum)
        {
            return datum switch
            {
                Datum.CorregoAlegre => 6.3783880e+06,
                Datum.SIRGAS2000 => 6.3781370e+06,
                Datum.WGS84 => 6.3781370e+06,
                Datum.SAD69 => 6.3781600e+06,
                _ => throw new NotImplementedException("There are no radius values for this datum."),
            };
        }

        public static double EarthEllipsoidFlattening(this Datum datum)
        {
            return datum switch
            {
                Datum.CorregoAlegre => 3.36700337e-03,
                Datum.SIRGAS2000 => 3.35281068e-03,
                Datum.WGS84 => 3.35281066e-03,
                Datum.SAD69 => 3.35289187e-03,
                _ => throw new NotImplementedException("There are no flattening values for this datum."),
            };
        }

        public static double DeltaX(this Datum datum)
        {
            return datum switch
            {
                Datum.CorregoAlegre => -2.060500e+02,
                Datum.SIRGAS2000 => 0.000000e+00,
                Datum.WGS84 => 0.000000e+00,
                Datum.SAD69 => -6.735000e+01,
                _ => throw new NotImplementedException("There are no delta x values for this datum."),
            };
        }

        public static double DeltaY(this Datum datum)
        {
            return datum switch
            {
                Datum.CorregoAlegre => 1.682800e+02,
                Datum.SIRGAS2000 => 0.000000e+00,
                Datum.WGS84 => 0.000000e+00,
                Datum.SAD69 => 3.880000e+00,
                _ => throw new NotImplementedException("There are no delta y values for this datum."),
            };
        }

        public static double DeltaZ(this Datum datum)
        {
            return datum switch
            {
                Datum.CorregoAlegre => -3.820000e+00,
                Datum.SIRGAS2000 => 0.000000e+00,
                Datum.WGS84 => 0.000000e+00,
                Datum.SAD69 => -3.822000e+01,
                _ => throw new NotImplementedException("There are no delta z values for this datum."),
            };
        }
    }
}
