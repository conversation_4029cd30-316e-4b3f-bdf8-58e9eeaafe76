using Coordinate.Core.Classes;
using Coordinate.Core.Enums;
using Coordinate.Core.Extensions;
using System.Text.RegularExpressions;

namespace Coordinate.Core
{
    public static class Helper
    {
        public static DecimalGeodetic ToDatum(Datum inputDatum, Datum outputDatum, DecimalGeodetic coordinate)
        {
            return TransformDatum(inputDatum, outputDatum, coordinate);
        }

        public static Utm ToDatum(Datum inputDatum, Datum outputDatum, Utm coordinate)
        {
            var geoIn = ConvertUTMToDecimalGeodetic(coordinate, inputDatum);
            var transformed = TransformDatum(inputDatum, outputDatum, geoIn);
            return ConvertDecimalGeodeticToUTM(transformed, outputDatum);
        }

        public static Utm ConvertDecimalGeodeticToUTM(DecimalGeodetic decimalGeodetic, Datum inputDatum)
        {
            var lat = decimalGeodetic.Latitude;
            var longi = decimalGeodetic.Longitude;

            // Switch to UPS
            if (lat < -80 || lat > 84)
            {
                return GeodeticToUPS(lat, longi, inputDatum);
            }

            //Within UTM BOUNDS
            string letter;

            var zone = (int)Math.Floor(longi / 6 + 31);

            if (lat < -72)
                letter = "C";
            else if (lat < -64)
                letter = "D";
            else if (lat < -56)
                letter = "E";
            else if (lat < -48)
                letter = "F";
            else if (lat < -40)
                letter = "G";
            else if (lat < -32)
                letter = "H";
            else if (lat < -24)
                letter = "J";
            else if (lat < -16)
                letter = "K";
            else if (lat < -8)
                letter = "L";
            else if (lat < 0)
                letter = "M";
            else if (lat < 8)
                letter = "N";
            else if (lat < 16)
                letter = "P";
            else if (lat < 24)
                letter = "Q";
            else if (lat < 32)
                letter = "R";
            else if (lat < 40)
                letter = "S";
            else if (lat < 48)
                letter = "T";
            else if (lat < 56)
                letter = "U";
            else if (lat < 64)
                letter = "V";
            else if (lat < 72)
                letter = "W";
            else
                letter = "X";

            var a = inputDatum.EarthEllipsoidRadius();
            var f = inputDatum.EarthEllipsoidFlattening();
            // polar radius
            var b = a * (1 - f);

            var e = Math.Sqrt(1 - Math.Pow(b, 2) / Math.Pow(a, 2));

            var drad = Math.PI / 180;
            var k0 = 0.9996;

            // convert latitude to radians
            var phi = lat * drad;

            var utmz = 1 + Math.Floor((longi + 180) / 6.0);

            // longitude to utm zone
            // central meridian of a zone
            var zcm = 3 + 6.0 * (utmz - 1) - 180;

            // this gives us zone A-B for below 80S
            var esq = (1 - (b / a) * (b / a));
            var e0sq = e * e / (1 - Math.Pow(e, 2));

            var N = a / Math.Sqrt(1 - Math.Pow(e * Math.Sin(phi), 2));
            var T = Math.Pow(Math.Tan(phi), 2);
            var C = e0sq * Math.Pow(Math.Cos(phi), 2);
            var A = (longi - zcm) * drad * Math.Cos(phi);

            // calculate M (USGS style)
            var M = phi * (1 - esq * (1.0 / 4.0 + esq * (3.0 / 64.0 + 5.0 * esq / 256.0)));
            M -= Math.Sin(2.0 * phi) * (esq * (3.0 / 8.0 + esq * (3.0 / 32.0 + 45.0 * esq / 1024.0)));
            M += Math.Sin(4.0 * phi) * (esq * esq * (15.0 / 256.0 + esq * 45.0 / 1024.0));
            M -= Math.Sin(6.0 * phi) * (esq * esq * esq * (35.0 / 3072.0));
            //Arc length along standard meridian
            M *= a;

            // if another point of origin is used than the equator
            var M0 = 0;

            // Calculate the UTM values

            // first the easting
            // Easting relative to CM
            var x = k0 * N * A * (1 + A * A * ((1 - T + C) / 6 + A * A * (5 - 18 * T + T * T + 72.0 * C - 58 * e0sq) / 120.0));
            // standard easting
            x += 500000;

            // Northing

            var y = k0 * (M - M0 + N * Math.Tan(phi) * (A * A * (1 / 2.0 + A * A * ((5 - T + 9 * C + 4 * C * C) / 24.0 + A * A * (61 - 58 * T + T * T + 600 * C - 330 * e0sq) / 720.0))));    // first from the equator

            if (y < 0)
            {
                // add in false northing if south of the equator
                y = 10000000 + y;
            }

            //Last zone is 60, but will hit 61 at 180 degrees exactly. Reset to 1.
            if (zone == 61)
            {
                zone = 1;
            }

            var easting = x;
            var northing = y;

            return new()
            {
                Easting = easting,
                Northing = northing,
                ZoneNumber = zone,
                ZoneLetter = char.Parse(letter)
            };
        }

        public static DecimalGeodetic ConvertUTMToDecimalGeodetic(Utm utm, Datum inputDatum)
        {
            var southhemi = false;
            var upsCheck = new Regex("[AaBbYyZz]");
            if (upsCheck.IsMatch(utm.ZoneLetter.ToString()))
            {
                return UPSToGeodetic(utm, inputDatum.EarthEllipsoidFlattening(), inputDatum.EarthEllipsoidRadius());
            }

            var regex = new Regex("[C-Hc-hJ-Mj-m]");
            if (regex.IsMatch(utm.ZoneLetter.ToString())) { southhemi = true; }

            double cmeridian;

            double x = utm.Easting - 500000.0;
            double UTMScaleFactor = 0.9996;
            x /= UTMScaleFactor;

            // If in southern hemisphere, adjust y accordingly.
            var y = utm.Northing;
            if (southhemi)
            {
                y -= 10000000.0;
            }

            y /= UTMScaleFactor;

            cmeridian = UTMCentralMeridian(utm.ZoneNumber);

            return UTMToDecimalGeodetic(x, y, cmeridian, inputDatum.EarthEllipsoidRadius(), inputDatum.EarthEllipsoidFlattening());
        }

        private static DecimalGeodetic TransformDatum(Datum inputDatum, Datum outputDatum, DecimalGeodetic inputGeodetic)
        {
            var latRad = DegToRad(inputGeodetic.Latitude);
            var lonRad = DegToRad(inputGeodetic.Longitude);

            var a1 = inputDatum.EarthEllipsoidRadius();
            var f1 = inputDatum.EarthEllipsoidFlattening();
            var e21 = 2 * f1 - f1 * f1;
            var N1 = a1 / Math.Sqrt(1 - e21 * Math.Pow(Math.Sin(latRad), 2));
            var x1 = N1 * Math.Cos(latRad) * Math.Cos(lonRad);
            var y1 = N1 * Math.Cos(latRad) * Math.Sin(lonRad);
            var z1 = N1 * (1 - e21) * Math.Sin(latRad);

            var dX = inputDatum.DeltaX() - outputDatum.DeltaX();
            var dY = inputDatum.DeltaY() - outputDatum.DeltaY();
            var dZ = inputDatum.DeltaZ() - outputDatum.DeltaZ();
            var x2 = x1 + dX;
            var y2 = y1 + dY;
            var z2 = z1 + dZ;

            var a2 = outputDatum.EarthEllipsoidRadius();
            var f2 = outputDatum.EarthEllipsoidFlattening();
            var e22 = 2 * f2 - f2 * f2;
            var p = Math.Sqrt(x2 * x2 + y2 * y2);
            var latOut = Math.Atan2(z2, p * (1 - e22));
            var latOutPrev = 0.0;
            var tol = 1e-11;
            while (Math.Abs(latOut - latOutPrev) > tol)
            {
                latOutPrev = latOut;
                var N2 = a2 / Math.Sqrt(1 - e22 * Math.Pow(Math.Sin(latOut), 2));
                latOut = Math.Atan2(z2 + e22 * N2 * Math.Sin(latOut), p);
            }
            var lonOut = Math.Atan2(y2, x2);

            return new DecimalGeodetic
            {
                Latitude = Math.Round(RadToDeg(latOut), 10),
                Longitude = Math.Round(RadToDeg(lonOut), 10)
            };
        }

        private static Utm GeodeticToUPS(double latitude, double longitude, Datum datum)
        {
            //RESET FOR CALCS
            if (longitude == -180)
            {
                longitude = 180;
            }

            //LAT LONG TO UPS
            //TEC-SR-7 US ARMY Corps of Engineers CONVERSIONS TEXT PAGE 99 STEP 0
            double latRad = latitude * Math.PI / 180;
            double longRad = longitude * Math.PI / 180;

            //STEP 1
            //E = Eccentricity
            //E^2 = (a^2 - b^2) / a2
            //A and B pulled from Table A-1
            //A-1 are datum value, pull in form Coordinate object

            //ELLIPSOID

            //Inverse flattening
            var f = datum.EarthEllipsoidFlattening();
            //Semi-Major Axis
            var a = datum.EarthEllipsoidRadius();

            //Semi Minor Axis a(1-f)     
            double b = a * (1 - f);

            //Eccentricity Squared  
            double E2 = (2 * f) - Math.Pow(f, 2);

            //Eccentricity 
            double E = Math.Sqrt(E2);

            //STEP 2
            double K = ((2 * (a * a)) / b) * Math.Pow(((1 - E) / (1 + E)), (E / 2));

            //STEP 3
            double KTan = K * Math.Tan((Math.PI / 4) - (Math.Abs(latRad) / 2));
            double r = KTan * Math.Pow((1 + E * Math.Sin(Math.Abs(latRad))) / (1 - E * Math.Sin(Math.Abs(latRad))), (E / 2));

            //STEP 4
            double x = 2000000 + .994 * r * Math.Sin(longRad);

            double y;
            if (latitude > 0) { y = 2000000 - .994 * r * Math.Cos(longRad); }
            else { y = 2000000 + .994 * r * Math.Cos(longRad); }


            //GRAB LETTER.
            string zone;
            if (latitude >= 0)
            {
                if (longitude >= 0 || longitude == -180 || latitude == 90) { zone = "Z"; }
                else { zone = "Y"; }
            }
            else
            {
                if (longitude >= 0 || longitude == -180 || latitude == -90) { zone = "B"; }
                else { zone = "A"; }
            }

            return new()
            {
                Easting = x,
                Northing = y,
                ZoneNumber = 0,
                ZoneLetter = char.Parse(zone)
            };
        }

        private static DecimalGeodetic UPSToGeodetic(Utm utm, double inveseFlattening, double equatorialRadius)
        {
            //INTERNATIONAL ELLIPSOID

            //Inverse flattening
            double f = inveseFlattening;
            //Semi-Major Axis
            double a = equatorialRadius;
            //Semi Minor Axis a(1-f)     
            double b = a * (1 - f);

            //Eccentricity Squared
            double E2 = (2 * f) - Math.Pow(f, 2);
            //Eccentricity 
            double E = Math.Sqrt(E2);

            //CONVERT BACK
            double x = utm.Easting;
            double y = utm.Northing;

            //STEP 1          
            double Xps = (x - 2000000) / .994;

            double Yps = (y - 2000000) / .994;

            //Used for true longi calcs.
            double tPS = Yps;

            if (Yps == 0) { Yps = 1; }

            //STEP 2
            //ATAN = ARCTAN

            bool southernHemi = true;
            if (utm.ZoneLetter.ToString().ToUpper() == "Z" || utm.ZoneLetter.ToString().ToUpper() == "Y") { southernHemi = false; }

            double longRad;
            //USED FOR LAT CALCS. LongRad is will LongRad. This is needed to due exact 90 issues.
            double longRadForCalcs;
            if (southernHemi)
            {
                longRad = Math.PI + Math.Atan(Xps / tPS);
                longRadForCalcs = Math.PI + Math.Atan(Xps / Yps);
            }
            else
            {
                longRad = Math.PI - Math.Atan(Xps / tPS);
                longRadForCalcs = Math.PI - Math.Atan(Xps / Yps);
            }

            //STEP 3
            double K = (2 * Math.Pow(a, 2) / b) * Math.Pow(((1 - E) / (1 + E)), (E / 2));


            //STEP 4
            double absYps = Math.Abs(Yps);
            double kCos = K * Math.Abs(Math.Cos(longRadForCalcs));
            double q = Math.Log(absYps / kCos) / Math.Log(Math.E) * -1;

            //STEP 5
            double estLat = 2 * Math.Atan(Math.Pow(Math.E, q)) - (Math.PI / 2);


            double lat = 0;
            while (Math.Abs(estLat - lat) > .0000001)
            {
                if (double.IsInfinity(estLat)) { break; }
                lat = estLat;
                //STEP 6       
                double bracket = (1 + Math.Sin(estLat)) / (1 - Math.Sin(estLat)) * Math.Pow((1 - E * Math.Sin(estLat)) / (1 + E * Math.Sin(estLat)), E);
                double fLat = -q + 1 / 2.0 * Math.Log(bracket);
                double fLat2 = (1 - Math.Pow(E, 2)) / ((1 - Math.Pow(E, 2) * Math.Pow(Math.Sin(estLat), 2)) * Math.Cos(estLat));

                //STEP 7
                estLat -= fLat / fLat2;

            }
            if (!double.IsInfinity(estLat))
            {
                lat = estLat;
            }

            //NaN signals poles
            double latDeg;
            if (double.IsNaN(lat))
            {
                latDeg = 90;
            }
            else
            {
                //Radians to Degrees        
                latDeg = lat * (180 / Math.PI);
            }

            if (southernHemi)
            {
                latDeg *= -1;
            }


            double longDeg;
            if (double.IsNaN(longRad))
            {
                longDeg = 0;
            }
            else
            {
                longDeg = (longRad) * (180 / Math.PI);
            }
            if (utm.Easting < 2000000)
            {
                //Normalize to 180 degrees
                longDeg = 180 - longDeg % 180;
                //Set Western Hemi
                longDeg *= -1;
            }
            else if (longDeg > 180)
            {
                longDeg -= 180;
            }
            else if (longDeg < -180)
            {
                longDeg += 180;
            }

            // SET TO 0 or it will equate to 180
            if (utm.Northing >= 2000000 && Xps == 0 && southernHemi)
            {
                longDeg = 0;
            }

            // SET TO 0 or it will equate to 180
            if (utm.Northing < 2000000 && Xps == 0 && !southernHemi)
            {
                longDeg = 0;
            }

            return new()
            {
                Latitude = latDeg,
                Longitude = longDeg
            };
        }

        private static double UTMCentralMeridian(double zone)
        {
            return DegToRad(-183.0 + (zone * 6.0));
        }

        private static DecimalGeodetic UTMToDecimalGeodetic(double x, double y, double zone, double equatorialRadius, double flattening)
        {
            double phif, Nf, Nfpow, nuf2, ep2, tf, tf2, tf4, cf;
            double x1frac, x2frac, x3frac, x4frac, x5frac, x6frac, x7frac, x8frac;
            double x2poly, x3poly, x4poly, x5poly, x6poly, x7poly, x8poly;

            double sm_a = equatorialRadius;

            //Polar Radius
            double sm_b = equatorialRadius * (1 - flattening);

            // Get the value of phif, the footpoint latitude.
            phif = FootpointLatitude(y, equatorialRadius, flattening);

            // Precalculate ep2
            ep2 = (Math.Pow(sm_a, 2.0) - Math.Pow(sm_b, 2.0))
                  / Math.Pow(sm_b, 2.0);

            // Precalculate cos (phif)
            cf = Math.Cos(phif);

            // Precalculate nuf2
            nuf2 = ep2 * Math.Pow(cf, 2.0);

            // Precalculate Nf and initialize Nfpow
            Nf = Math.Pow(sm_a, 2.0) / (sm_b * Math.Sqrt(1 + nuf2));
            Nfpow = Nf;

            // Precalculate tf
            tf = Math.Tan(phif);
            tf2 = tf * tf;
            tf4 = tf2 * tf2;

            // Precalculate fractional coefficients for x**n in the equations
            //   below to simplify the expressions for latitude and longitude.
            x1frac = 1.0 / (Nfpow * cf);

            Nfpow *= Nf;   // now equals Nf**2)
            x2frac = tf / (2.0 * Nfpow);

            Nfpow *= Nf;   // now equals Nf**3)
            x3frac = 1.0 / (6.0 * Nfpow * cf);

            Nfpow *= Nf;   // now equals Nf**4)
            x4frac = tf / (24.0 * Nfpow);

            Nfpow *= Nf;   // now equals Nf**5)
            x5frac = 1.0 / (120.0 * Nfpow * cf);

            Nfpow *= Nf;   // now equals Nf**6)
            x6frac = tf / (720.0 * Nfpow);

            Nfpow *= Nf;   // now equals Nf**7)
            x7frac = 1.0 / (5040.0 * Nfpow * cf);

            Nfpow *= Nf;   // now equals Nf**8)
            x8frac = tf / (40320.0 * Nfpow);

            // Precalculate polynomial coefficients for x**n.
            //   -- x**1 does not have a polynomial coefficient. 
            x2poly = -1.0 - nuf2;

            x3poly = -1.0 - 2 * tf2 - nuf2;

            x4poly = 5.0 + 3.0 * tf2 + 6.0 * nuf2 - 6.0 * tf2 * nuf2
                - 3.0 * (nuf2 * nuf2) - 9.0 * tf2 * (nuf2 * nuf2);

            x5poly = 5.0 + 28.0 * tf2 + 24.0 * tf4 + 6.0 * nuf2 + 8.0 * tf2 * nuf2;

            x6poly = -61.0 - 90.0 * tf2 - 45.0 * tf4 - 107.0 * nuf2
                + 162.0 * tf2 * nuf2;

            x7poly = -61.0 - 662.0 * tf2 - 1320.0 * tf4 - 720.0 * (tf4 * tf2);

            x8poly = 1385.0 + 3633.0 * tf2 + 4095.0 * tf4 + 1575 * (tf4 * tf2);

            // Calculate latitude 
            var nLat = phif + x2frac * x2poly * (x * x)
                + x4frac * x4poly * Math.Pow(x, 4.0)
                + x6frac * x6poly * Math.Pow(x, 6.0)
                + x8frac * x8poly * Math.Pow(x, 8.0);

            // Calculate longitude
            var nLong = zone + x1frac * x
                + x3frac * x3poly * Math.Pow(x, 3.0)
                + x5frac * x5poly * Math.Pow(x, 5.0)
                + x7frac * x7poly * Math.Pow(x, 7.0);

            var dLat = RadToDeg(nLat);
            var dLong = RadToDeg(nLong);
            if (dLat > 90) { dLat = 90; }
            if (dLat < -90) { dLat = -90; }
            if (dLong > 180) { dLong = 180; }
            if (dLong < -180) { dLong = -180; }

            return new()
            {
                Latitude = Math.Round(dLat, 6),
                Longitude = Math.Round(dLong, 6)
            };
        }

        private static double DegToRad(double degrees) => degrees * (Math.PI / 180);

        private static double RadToDeg(double radians) => radians * (180 / Math.PI);

        private static double FootpointLatitude(double y, double equatorialRadius, double flattening)
        {
            double y_, alpha_, beta_, gamma_, delta_, epsilon_, n;
            double result;

            // Ellipsoid model constants
            double sm_a = equatorialRadius;
            double sm_b = equatorialRadius * (1 - (flattening));

            // Precalculate n (Eq. 10.18)
            n = (sm_a - sm_b) / (sm_a + sm_b);

            // Precalculate alpha_ (Eq. 10.22)
            // (Same as alpha in Eq. 10.17)
            alpha_ = ((sm_a + sm_b) / 2.0) * (1 + (Math.Pow(n, 2.0) / 4) + (Math.Pow(n, 4.0) / 64));

            // Precalculate y_ (Eq. 10.23)
            y_ = y / alpha_;

            // Precalculate beta_ (Eq. 10.22)
            beta_ = (3.0 * n / 2.0) + (-27.0 * Math.Pow(n, 3.0) / 32.0)
                + (269.0 * Math.Pow(n, 5.0) / 512.0);

            // Precalculate gamma_ (Eq. 10.22)
            gamma_ = (21.0 * Math.Pow(n, 2.0) / 16.0)
                + (-55.0 * Math.Pow(n, 4.0) / 32.0);

            // Precalculate delta_ (Eq. 10.22)
            delta_ = (151.0 * Math.Pow(n, 3.0) / 96.0)
                + (-417.0 * Math.Pow(n, 5.0) / 128.0);

            // Precalculate epsilon_ (Eq. 10.22)
            epsilon_ = (1097.0 * Math.Pow(n, 4.0) / 512.0);

            // Now calculate the sum of the series (Eq. 10.21)
            result = y_ + (beta_ * Math.Sin(2.0 * y_))
                + (gamma_ * Math.Sin(4.0 * y_))
                + (delta_ * Math.Sin(6.0 * y_))
                + (epsilon_ * Math.Sin(8.0 * y_));

            return result;
        }
    }
}
