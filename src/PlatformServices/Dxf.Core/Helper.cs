using Dxf.Core.Classes;
using Dxf.Core.Extensions;
using Geometry.Core;
using IxMilia.Dxf;
using IxMilia.Dxf.Entities;

namespace Dxf.Core
{
    public static class Helper
    {
        public static PointD GetFirstPointOfSectionUpstream(this DxfFile dxfFile)
        {
            var pointDList = new List<PointD>();

            pointDList.AddRange(dxfFile.Entities
                .Where(x => x.EntityType == DxfEntityType.Polyline)
                .Select(x => (DxfPolyline)x)
                .SelectMany(x => x.Vertices)
                .Select(x => new PointD(x.Location.X, x.Location.Y))
                .ToList());

            pointDList.AddRange(dxfFile.Entities
                .Where(x => x.EntityType == DxfEntityType.LwPolyline)
                .Select(x => (DxfLwPolyline)x)
                .SelectMany(x => x.Vertices)
                .Select(x => new PointD(x.X, x.Y)));

            pointDList.AddRange(dxfFile.Entities
                .Where(x => x.EntityType == DxfEntityType.Line)
                .SelectMany(x => new[]
                {
                    new PointD(((DxfLine)x).P1.X, ((DxfLine)x).P1.Y),
                    new PointD(((DxfLine)x).P2.X, ((DxfLine)x).P2.Y)
                })
                .ToList());

            return pointDList.OrderBy(p => p.X)
                    .ThenBy(p => p.Y)
                    .First();
        }

        private static List<PolygonProperties> GetPolygonProperties(List<DxfHatch> hatches)
        {
            var polygons = new List<PolygonProperties>();

            foreach (var hatch in hatches)
            {
                var hatchPolygons = hatch.GetHatchPolygons();

                foreach (var hatchPolygon in hatchPolygons)
                {
                    var polygonCenter = GetPolygonCenter(hatchPolygon);

                    polygons.Add(new PolygonProperties
                    {
                        CenterX = polygonCenter.Item1,
                        CenterY = polygonCenter.Item2,
                        Vertices = hatchPolygon,
                        Layer = hatch.Layer
                    });
                }
            }

            return polygons;
        }

        private static List<PolygonProperties> GetPolygonProperties(List<DxfLine> lines)
        {
            var polygons = new List<PolygonProperties>();

            var groupedLines = lines.GroupBy(x => x.Layer);

            foreach (var groupedLine in groupedLines)
            {
                var linePolygons = groupedLine.ToList().GetLinePolygons();

                foreach (var linePolygon in linePolygons)
                {
                    var polygonCenter = GetPolygonCenter(linePolygon);

                    polygons.Add(new PolygonProperties
                    {
                        CenterX = polygonCenter.Item1,
                        CenterY = polygonCenter.Item2,
                        Vertices = linePolygon,
                        Layer = groupedLine.Key
                    });
                }
            }

            return polygons;
        }

        private static List<PolygonProperties> GetPolygonProperties(List<DxfSolid> solids)
        {
            var polygons = new List<PolygonProperties>();

            foreach (var solid in solids)
            {
                var solidPolygons = solid.GetSolidPolygons();

                foreach (var solidPolygon in solidPolygons)
                {
                    var polygonCenter = GetPolygonCenter(solidPolygon);

                    polygons.Add(new PolygonProperties
                    {
                        CenterX = polygonCenter.Item1,
                        CenterY = polygonCenter.Item2,
                        Vertices = solidPolygon,
                        Layer = solid.Layer
                    });
                }
            }

            return polygons;
        }

        public static List<PolygonProperties> GetPolygons(List<DxfEntity> entities)
        {
            var polygons = new List<PolygonProperties>();

            var hatches = entities
                .Where(x => x.EntityType == DxfEntityType.Hatch)
                .Select(x => (DxfHatch)x)
                .ToList();

            var solids = new List<DxfSolid>();

            solids.AddRange(entities
                .Where(x => x.EntityType == DxfEntityType.Solid)
                .Select(x => (DxfSolid)x)
                .ToList());

            var lines = new List<DxfLine>();

            lines.AddRange(entities
               .Where(x => x.EntityType == DxfEntityType.Line)
               .Select(x => (DxfLine)x)
               .ToList());

            polygons.AddRange(GetPolygonProperties(hatches));
            polygons.AddRange(GetPolygonProperties(solids));
            polygons.AddRange(GetPolygonProperties(lines));

            return polygons;
        }

        public static Tuple<double, double> GetPolygonCenter(List<DxfVertex> vertices)
        {
            var polygonCenterX = vertices.Select(x => x.Location.X).Average();
            var polygonCenterY = vertices.Select(x => x.Location.Y).Average();

            return Tuple.Create(polygonCenterX, polygonCenterY);
        }
    }
}
