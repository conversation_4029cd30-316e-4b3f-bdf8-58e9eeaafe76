using IxMilia.Converters;
using IxMilia.Dxf;

namespace Dxf.Core.Converter
{
    public static class DxfExtensions
    {
        public static Vector ToVector(this DxfPoint point) => new Vector(point.X, point.Y, point.Z);

        public static bool IsCloseTo(this double a, double b)
        {
            return Math.Abs(a - b) < 1.0e-10;
        }

        public static UnitFormat ToUnitFormat(this DxfUnitFormat unitFormat)
        {
            switch (unitFormat)
            {
                case DxfUnitFormat.Architectural:
                case DxfUnitFormat.ArchitecturalStacked:
                    return UnitFormat.Architectural;
                case DxfUnitFormat.Decimal:
                case DxfUnitFormat.Engineering:
                case DxfUnitFormat.Scientific:
                    return UnitFormat.Decimal;
                case DxfUnitFormat.Fractional:
                case DxfUnitFormat.FractionalStacked:
                    return UnitFormat.Fractional;
                default:
                    throw new ArgumentOutOfRangeException(nameof(unitFormat));
            }
        }

        public static DxfPoint ToDxfPoint(this Vector v) => new DxfPoint(v.X, v.Y, v.Z);

        public static DimensionSettings ToDimensionSettings(this DxfDimStyle dimStyle)
        {
            return new DimensionSettings(
                textHeight: dimStyle.DimensioningTextHeight,
                extensionLineOffset: dimStyle.DimensionExtensionLineOffset,
                extensionLineExtension: dimStyle.DimensionExtensionLineExtension,
                dimensionLineGap: dimStyle.DimensionLineGap,
                arrowSize: dimStyle.DimensioningArrowSize,
                tickSize: dimStyle.DimensioningTickSize);
        }

        public static DrawingUnits ToDrawingUnits(this DxfDrawingUnits drawingUnits)
        {
            switch (drawingUnits)
            {
                case DxfDrawingUnits.English:
                    return DrawingUnits.English;
                case DxfDrawingUnits.Metric:
                    return DrawingUnits.Metric;
                default:
                    throw new ArgumentOutOfRangeException(nameof(drawingUnits));
            }
        }
    }
}
