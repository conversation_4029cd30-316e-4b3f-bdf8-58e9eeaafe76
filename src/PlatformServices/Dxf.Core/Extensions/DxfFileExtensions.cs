using Geometry.Core;
using IxMilia.Dxf;
using IxMilia.Dxf.Entities;

namespace Dxf.Core.Extensions
{
    public static class DxfFileExtensions
    {
        public static DxfFile LoadWithBase64(this DxfFile dxfFile, string dxfBase64)
        {
            var dxfBytes = Convert.FromBase64String(dxfBase64);
            using var stream = new MemoryStream(dxfBytes);
            return DxfFile.Load(stream);
        }

        public static DxfColor FromRgb(byte r, byte g, byte b)
        {
            int index = ClosestColorIndex(r, g, b);

            return DxfColor.FromIndex((byte)index);
        }

        public static List<PointD> GetAllPoints(this DxfFile dxfFile, string layer = null)
        {
            var externalEntities = new List<DxfEntity>();

            if (string.IsNullOrEmpty(layer))
            {
                externalEntities = dxfFile.Entities.ToList();
            }
            else
            {
                externalEntities = dxfFile.Entities.Where(x => x.Layer.ToLower() == layer.ToLower()).ToList();
            }

            var externalPoints = externalEntities
                .Where(x => x.EntityType == DxfEntityType.Polyline)
                .Select(x => (DxfPolyline)x)
                .SelectMany(x => x.Vertices)
                .Select(x => new PointD(x.Location.X, x.Location.Y))
                .ToList();

            externalPoints.AddRange(externalEntities
                .Where(x => x.EntityType == DxfEntityType.LwPolyline)
                .Select(x => (DxfLwPolyline)x)
                .SelectMany(x => x.Vertices)
                .Select(x => new PointD(x.X, x.Y)));

            externalPoints.AddRange(externalEntities
                .Where(x => x.EntityType == DxfEntityType.Line)
                .SelectMany(x => new[]
                {
                    new PointD(((DxfLine)x).P1.X, ((DxfLine)x).P1.Y),
                    new PointD(((DxfLine)x).P2.X, ((DxfLine)x).P2.Y)
                })
                .ToList());

            if (!externalPoints.Any())
            {
                externalPoints.Add(new PointD(0, 0));
            }

            return externalPoints;
        }

        private static int ClosestColorIndex(byte r, byte g, byte b)
        {
            int resultIndex = 0;
            double closestDistance = double.MaxValue;

            for (int index = 1; index < DxfColor.DefaultColors.Count; ++index)
            {
                byte colorR = (byte)((DxfColor.DefaultColors[index] >> 16) & 255);
                byte colorG = (byte)((DxfColor.DefaultColors[index] >> 8) & 255);
                byte colorB = (byte)(DxfColor.DefaultColors[index] & 255);

                double distance = Math.Sqrt(Math.Pow(colorR - r, 2) +
                                            Math.Pow(colorG - g, 2) +
                                            Math.Pow(colorB - b, 2));

                if (distance < closestDistance)
                {
                    resultIndex = index;
                    closestDistance = distance;
                }
            }

            return resultIndex;
        }
        
        /// <summary>
        /// Retrieves a list of filling entities from a given DXF file.
        /// Filling entities include hatches, solids, and lines, as well as entities
        /// contained within inserts, with their layers properly assigned.
        /// </summary>
        /// <param name="dxfFile">The DXF file to extract filling entities from.</param>
        /// <returns>
        /// A list of <see cref="DxfEntity"/> objects representing the filling entities
        /// (hatches, solids, and lines) in the DXF file.
        /// </returns>
        public static List<DxfEntity> GetFillingEntities(this DxfFile dxfFile)
        {
            var fillingEntities = new List<DxfEntity>();
    
            var hatches = dxfFile.Entities
                .Where(entity => entity.EntityType == DxfEntityType.Hatch)
                .Select(entity => (DxfHatch)entity)
                .ToList();
    
            fillingEntities.AddRange(hatches);
    
            foreach (var insert in dxfFile.Entities
                         .Where(entity => entity.EntityType == DxfEntityType.Insert)
                         .Cast<DxfInsert>())
            {
                var solids = insert.Entities
                    .Where(entity => entity.EntityType == DxfEntityType.Solid)
                    .Select(entity => (DxfSolid)entity)
                    .ToList();
    
                var lines = insert.Entities
                    .Where(entity => entity.EntityType == DxfEntityType.Line)
                    .Select(entity => (DxfLine)entity)
                    .ToList();
    
                foreach (var solid in solids)
                {
                    solid.Layer = insert.Layer;
                }
    
                foreach (var line in lines)
                {
                    line.Layer = insert.Layer;
                }
    
                fillingEntities.AddRange(solids);
                fillingEntities.AddRange(lines);
            }
    
            fillingEntities.AddRange(dxfFile.Entities
                .Where(entity => entity.EntityType == DxfEntityType.Solid)
                .Select(entity => (DxfSolid)entity)
                .ToList());
    
            return fillingEntities;
        }
        
        /// <summary>
        /// Retrieves a list of external points from a given DXF file.
        /// External points are extracted from entities on the "external" layer, 
        /// including polylines, lightweight polylines, and lines.
        /// </summary>
        /// <param name="dxfFile">The DXF file to extract external points from.</param>
        /// <returns>
        /// A list of <see cref="PointD"/> objects representing the external points
        /// found in the DXF file.
        /// </returns>
        public static List<PointD> GetExternalPoints(this DxfFile dxfFile)
        {
            var externalEntities = dxfFile.Entities
                .Where(entity => entity.Layer.ToLower() == "external")
                .ToList();
    
            var externalPoints = externalEntities
                .Where(entity => entity.EntityType == DxfEntityType.Polyline)
                .Select(entity => (DxfPolyline)entity)
                .SelectMany(polyline => polyline.Vertices)
                .Select(vertex => new PointD(vertex.Location.X, vertex.Location.Y))
                .ToList();
    
            externalPoints.AddRange(externalEntities
                .Where(entity => entity.EntityType == DxfEntityType.LwPolyline)
                .Select(entity => (DxfLwPolyline)entity)
                .SelectMany(polyline => polyline.Vertices)
                .Select(vertex => new PointD(vertex.X, vertex.Y)));
    
            externalPoints.AddRange(externalEntities
                .Where(entity => entity.EntityType == DxfEntityType.Line)
                .SelectMany(entity => new[]
                {
                    new PointD(((DxfLine)entity).P1.X, ((DxfLine)entity).P1.Y),
                    new PointD(((DxfLine)entity).P2.X, ((DxfLine)entity).P2.Y)
                })
                .ToList());
    
            return externalPoints;
        }

        /// <summary>
        /// Retrieves a list of fixed points from a given DXF file.
        /// Fixed points are extracted from entities on layers named "fixed points",
        /// "fixedpoints", or "fixed_points", including polylines, lightweight polylines,
        /// circles, and vertices.
        /// </summary>
        /// <param name="dxfFile">The DXF file to extract fixed points from.</param>
        /// <returns>
        /// A list of <see cref="PointD"/> objects representing the fixed points
        /// found in the DXF file.
        /// </returns>
        public static List<PointD> GetFixedPoints(this DxfFile dxfFile)
        {
            var fixedPoints = dxfFile.Entities
                .Where(x => x.Layer.ToLower() == "fixed points"
                            || x.Layer.ToLower() == "fixedpoints"
                            || x.Layer.ToLower() == "fixed_points")
                .ToList();
    
            var fixedPointsCoordinates = fixedPoints
                .Where(entity => entity.EntityType == DxfEntityType.Polyline)
                .Select(entity => (DxfPolyline)entity)
                .SelectMany(polyline => polyline.Vertices)
                .Select(vertex => new PointD(vertex.Location.X, vertex.Location.Y))
                .ToList();
    
            fixedPointsCoordinates.AddRange(fixedPoints
                .Where(entity => entity.EntityType == DxfEntityType.LwPolyline)
                .Select(entity => (DxfLwPolyline)entity)
                .SelectMany(polyline => polyline.Vertices)
                .Select(vertex => new PointD(vertex.X, vertex.Y)));
    
            fixedPointsCoordinates.AddRange(fixedPoints
                .Where(entity => entity.EntityType == DxfEntityType.Circle)
                .Select(entity => (DxfCircle)entity)
                .Select(circle => circle.Center)
                .Select(point => new PointD(point.X, point.Y)));
    
            fixedPointsCoordinates.AddRange(fixedPoints
                .Where(entity => entity.EntityType == DxfEntityType.Vertex)
                .Select(entity => (DxfVertex)entity)
                .Select(vertex => vertex.Location)
                .Select(point => new PointD(point.X, point.Y)));
    
            return fixedPointsCoordinates;
        }
        
        /// <summary>
        /// Retrieves a list of entities from a DXF file that match specific block search types.
        /// The method searches for entities whose layer names contain variations of the specified type,
        /// such as "blocksearch {type}", "blocksearch{type}", "block_search_{type}", and others.
        /// </summary>
        /// <param name="dxfFile">The DXF file to search for entities.</param>
        /// <param name="type">The type of block search to look for in the layer names.</param>
        /// <returns>
        /// A list of <see cref="DxfEntity"/> objects whose layer names match the specified block search type.
        /// </returns>
        public static List<DxfEntity> GetBlockSearchEntitiesByType(
            this DxfFile dxfFile, 
            string type)
        {
            var entityTypes = new[]
                {
                    $"blocksearch {type}",
                    $"blocksearch{type}",
                    $"block_search_{type}",
                    $"block-search-{type}",
                    $"block search {type}"
                }
                .Select(x => x.ToLower())
                .ToArray();

            return dxfFile.Entities
                .Where(entity => entityTypes
                    .Any(typeName => entity.Layer.ToLower().Contains(typeName)))
                .ToList();
        }
    }
}
