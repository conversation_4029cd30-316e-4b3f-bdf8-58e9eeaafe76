using IxMilia.Dxf;
using IxMilia.Dxf.Entities;
using static IxMilia.Dxf.Entities.DxfHatch;

namespace Dxf.Core.Extensions
{
    public static class BoundaryPathBaseExtensions
    {
        public static List<List<DxfVertex>> GetHatchPolygons(this DxfHatch hatch)
        {
            return hatch
               .BoundaryPaths
               .Where(x => x is PolylineBoundaryPath)
               .Cast<PolylineBoundaryPath>()
               .Select(x => x.Vertices)
               .ToList();
        }

        public static List<List<DxfVertex>> GetSolidPolygons(this DxfSolid solid)
        {
            var mirroredCoordinates = solid.ExtrusionDirection.Z == -1;

            // If the extrusion direction is downwards, the polygon center X should be multiplied by -1
            var firstCorner = new DxfPoint(mirroredCoordinates ? solid.FirstCorner.X * -1 : solid.FirstCorner.X, solid.FirstCorner.Y, 0);
            var secondCorner = new DxfPoint(mirroredCoordinates ? solid.SecondCorner.X * -1 : solid.SecondCorner.X, solid.SecondCorner.Y, 0);
            var thirdCorner = new DxfPoint(mirroredCoordinates ? solid.ThirdCorner.X * -1 : solid.ThirdCorner.X, solid.ThirdCorner.Y, 0);
            var fourthCorner = new DxfPoint(mirroredCoordinates ? solid.FourthCorner.X * -1 : solid.FourthCorner.X, solid.FourthCorner.Y, 0);

            return new List<List<DxfVertex>>()
            {
                new List<DxfVertex>() { new(firstCorner), new(secondCorner), new(thirdCorner), new(fourthCorner) }
            };
        }

        public static List<List<DxfVertex>> GetLinePolygons(this List<DxfLine> line)
        {
            return line
                .Select(x => new List<DxfVertex>() { new(x.P1), new(x.P2) })
                .ToList();
        }
    }
}
