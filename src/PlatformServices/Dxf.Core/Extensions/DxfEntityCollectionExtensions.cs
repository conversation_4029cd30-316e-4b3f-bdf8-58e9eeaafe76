using IxMilia.Dxf.Entities;

namespace Dxf.Core.Extensions;

public static class DxfEntityCollectionExtensions
{
    /// <summary>
    /// Extracts a list of unique material search identifiers from a collection of DXF entities.
    /// </summary>
    /// <param name="entities">The list of <see cref="DxfEntity"/> objects to process.</param>
    /// <returns>
    /// A list of integers representing the unique material search identifiers derived from the 
    /// <see cref="DxfEntity.Layer"/> property of the entities.
    /// </returns>
    public static List<int> GetMaterialSearchIdentifiers(
        this List<DxfEntity> entities)
    {
        var materialSearchIdentifiers = new List<int>();

        materialSearchIdentifiers.AddRange(entities
            .Where(entity => entity.Layer != "0" && int.TryParse(entity.Layer, out _))
            .Select(entity => int.Parse(entity.Layer))
            .Distinct()
            .ToList());

        return materialSearchIdentifiers;
    }
}