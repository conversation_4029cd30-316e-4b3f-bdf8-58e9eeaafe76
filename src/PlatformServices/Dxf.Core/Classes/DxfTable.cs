using IxMilia.Dxf;
using IxMilia.Dxf.Entities;

namespace Dxf.Core.Classes
{
    public sealed class DxfTable
    {
        private DxfFile _file;
        private double _leftStart;
        private double _topStart;
        private double _colWidth = 25;
        private double _rowHeight = 12;
        private double _textXOffset = 0.5;
        private double _textYOffset = 4;
        private double _textHeight = 2.2;
        private int _maxCharsPerLine = 12;
        private int _maxStringLength = 27;
        private double _shrinkAmount = 0.05;
        private string _layerName;

        public DxfTable(DxfFile file, int columns, int rows, string layerName = "Tabela")
        {
            _file = file;
            _layerName = layerName;

            var maxPointBoundingBox = file.Entities
                .Where(e => e.GetBoundingBox().HasValue)
                .Select(e => e.GetBoundingBox().Value.MaximumPoint)
                .OrderByDescending(p => p.X + p.Y)
                .FirstOrDefault();

            var maxX = maxPointBoundingBox.X;
            var maxY = maxPointBoundingBox.Y;

            var scaleFactor = Math.Min(maxX, maxY) / 100.0;
            if (scaleFactor <= 0)
            {
                scaleFactor = 1.0;
            }

            _colWidth *= scaleFactor;
            _rowHeight *= scaleFactor;
            _textXOffset *= scaleFactor;
            _textYOffset *= scaleFactor;
            _textHeight *= scaleFactor;

            _leftStart = maxX + (maxX * 0.05);
            _topStart = maxY;

            for (var i = 0; i <= rows; i++)
            {
                var y = _topStart - (i * _rowHeight);
                AddLine(_file, _leftStart, y, _leftStart + (columns * _colWidth), y);
            }

            for (var j = 0; j <= columns; j++)
            {
                var x = _leftStart + (j * _colWidth);
                AddLine(_file, x, _topStart, x, _topStart - (rows * _rowHeight));
            }
        }

        public void Insert(int column, int row, string text)
        {
            double x = _leftStart + column * _colWidth + _textXOffset;
            double y = _topStart - row * _rowHeight - _textYOffset;
            AddText(_file, x, y, text);
        }

        public void Insert(int column, int row, string text, DxfColor color)
        {
            double x = _leftStart + column * _colWidth + _textXOffset;
            double y = _topStart - row * _rowHeight - _textYOffset;

            double left = _leftStart + column * _colWidth;
            double top = _topStart - row * _rowHeight;
            double right = left + _colWidth;
            double bottom = top - _rowHeight;

            AddSolid(_file, left + _shrinkAmount, bottom + _shrinkAmount, right - _shrinkAmount, top - _shrinkAmount, color);
            AddText(_file, x, y, text);
        }

        private void AddLine(DxfFile file, double x1, double y1, double x2, double y2)
        {
            var line = new DxfLine(new DxfPoint(x1, y1, 0.0), new DxfPoint(x2, y2, 0.0)) { Layer = _layerName };
            file.Entities.Add(line);
        }

        private void AddText(DxfFile file, double x, double y, string text)
        {
            if (text.Length > _maxStringLength)
            {
                text = text.Substring(0, _maxStringLength);
            }

            var lines = SplitText(text);

            foreach (var line in lines)
            {
                var txt = new DxfText
                {
                    Location = new DxfPoint(x, y, 0),
                    Value = line,
                    TextHeight = _textHeight,
                    Layer = _layerName,
                    ObliqueAngle = 0,
                };

                file.Entities.Add(txt);

                y -= 1.5 * _textHeight;
            }
        }

        private List<string> SplitText(string text)
        {
            var lines = new List<string>();
            var words = text.Split(' ');

            var line = "";

            foreach (var word in words)
            {
                if ((line + word).Length > _maxCharsPerLine)
                {
                    if (!string.IsNullOrEmpty(line))
                    {
                        lines.Add(line);
                        line = "";
                    }

                    if (word.Length > _maxCharsPerLine)
                    {
                        for (int i = 0; i < word.Length; i += _maxCharsPerLine)
                        {
                            lines.Add(word.Substring(i, Math.Min(_maxCharsPerLine, word.Length - i)));
                        }
                    }
                    else
                    {
                        line = word;
                    }
                }
                else
                {
                    line = string.IsNullOrEmpty(line) ? word : line + " " + word;
                }
            }

            if (!string.IsNullOrEmpty(line))
            {
                lines.Add(line);
            }

            return lines;
        }

        private void AddSolid(DxfFile file, double x1, double y1, double x2, double y2, DxfColor color)
        {
            var solid = new DxfSolid
            {
                FirstCorner = new DxfPoint(x1, y1, 0),
                SecondCorner = new DxfPoint(x1, y2, 0),
                ThirdCorner = new DxfPoint(x2, y1, 0),
                FourthCorner = new DxfPoint(x2, y2, 0),
                Color = color,
                ExtrusionDirection = new DxfVector(0, 0, 1),
                Layer = _layerName
            };

            file.Entities.Add(solid);
        }
    }
}
