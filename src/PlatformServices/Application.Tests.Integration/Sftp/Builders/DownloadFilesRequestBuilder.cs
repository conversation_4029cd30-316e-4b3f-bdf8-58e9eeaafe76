using Application.Sftp.Model._Shared;
using Application.Sftp.Model.Request;

namespace Application.Tests.Integration.Sftp.Builders
{
    public static class DownloadFilesRequestBuilder
    {
        public static DownloadFilesRequest GetValid(
            string remoteFileFullPath)
        {
            var filename = remoteFileFullPath
                .Split('/')
                .Last();

            return new(new List<FileInformation>()
            {
                new(
                    name: filename,
                    fullPath: remoteFileFullPath)
            });
        }
    }
}
