using Application.Sftp.Model._Shared;
using Application.Sftp.Model.Request;

namespace Application.Tests.Integration.Sftp.Builders
{
    public static class MoveFilesRequestBuilder
    {
        public static MoveFilesRequest GetValid(
            string sourcePath,
            string destinationPath,
            string remoteFileFullPath)
        {
            var filename = remoteFileFullPath
                .Split('/')
                .Last();

            var files = new List<FileInformation>
            {
                new(
                    name: filename,
                    fullPath: remoteFileFullPath)
            };

            return new(
                FilesLastFetchDate: DateTime.UtcNow,
                SourcePaths: new List<string> { sourcePath },
                DestinationPath: destinationPath,
                Files: files);
        }
    }
}
