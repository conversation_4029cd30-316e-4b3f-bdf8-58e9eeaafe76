using Renci.SshNet;
using System.Text;

namespace Application.Tests.Integration.Sftp.Builders
{
    public static class SftpClientBuilder
    {
        private const string _username = "logisoil-workflows";
        private const string _password = "YDu^Af9b6DpH^>7!Gu>H";
        private const string _host = "*************";
        private const int _port = 2022;
        public const string NonExistentFolder = "nonExistentFolder";
        public const string TestIntegrationFolder = "test_integration";
        public const string TestIntegrationProcessedPath = "test_integration/processed";

        public static SftpClient CreateClient()
        {
            return new SftpClient(
                host: _host,
                port: _port,
                username: _username,
                password: _password);
        }

        public static async Task<string> UploadAsync(
            SftpClient sftpClient)
        {
            sftpClient.Connect();

            var existsTestDirectory = sftpClient
                .Exists(path: TestIntegrationFolder);
            if (!existsTestDirectory)
            {
                sftpClient
                    .CreateDirectory(path: TestIntegrationFolder);
            }

            var fileContent = Guid.NewGuid().ToString();
            var remoteFileFullPath = $"{TestIntegrationFolder}/{fileContent}.txt";

            await using var memoryStream = new MemoryStream(
                Encoding.UTF8.GetBytes(fileContent));

            sftpClient.UploadFile(
                input: memoryStream,
                path: remoteFileFullPath);

            sftpClient.Disconnect();

            return remoteFileFullPath;
        }

        public static async Task CleanFilesAsync(
            SftpClient sftpClient,
            string remoteFileFullPath)
        {
            sftpClient.Connect();

            await sftpClient.DeleteFileAsync(
                path: remoteFileFullPath,
                cancellationToken: new CancellationTokenSource().Token);

            sftpClient.Disconnect();
        }
    }
}
