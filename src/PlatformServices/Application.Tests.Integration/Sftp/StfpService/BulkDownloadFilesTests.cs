using Application.Sftp;
using Application.Sftp.Model.Request;
using Application.Tests.Integration.Sftp.Builders;
using FluentAssertions;
using Renci.SshNet;
using Renci.SshNet.Common;
using Xunit;

namespace Application.Tests.Integration.Sftp.StfpService
{
    [Trait("SftpService", "BulkDownloadFilesAsync")]
    [Collection("Sequential")]
    public class BulkDownloadFilesTests
    {
        private readonly SftpClient _sftpClient;
        private readonly SftpService _sftpService;

        public BulkDownloadFilesTests()
        {
            _sftpClient = SftpClientBuilder.CreateClient();

            _sftpService = new SftpService(_sftpClient);
        }

        [Fact(DisplayName = "When request is null - Returns failure")]
        public async Task WhenRequestIsNull_ReturnsFailure()
        {
            DownloadFilesRequest request = null;

            var result = await _sftpService
                .BulkDownloadFilesAsync(request);

            result
                .IsSuccessful
                .Should()
                .BeFalse();
        }

        [Fact(DisplayName = "When request is null - Returns failure message")]
        public async Task WhenRequestIsNull_ReturnsFailureMessage()
        {
            DownloadFilesRequest request = null;

            var result = await _sftpService
                .BulkDownloadFilesAsync(request);

            result
                .Message
                .Should()
                .Be("Request is null");
        }

        [Fact(DisplayName = "When request is null - Returns null")]
        public async Task WhenRequestIsNull_ReturnsNull()
        {
            DownloadFilesRequest request = null;

            var result = await _sftpService
                .BulkDownloadFilesAsync(request);

            result
                .Data
                .Should()
                .BeNull();
        }

        [Fact(DisplayName = "When files were not found - Returns failure")]
        public async Task WhenFilesWereNotFound_ReturnsFailure()
        {
            var remoteFileFullPath =
                $"{SftpClientBuilder.TestIntegrationFolder}/" +
                $"{Guid.NewGuid()}.txt";

            var request = DownloadFilesRequestBuilder
                .GetValid(remoteFileFullPath);

            var result = await _sftpService
                .BulkDownloadFilesAsync(request);

            result
                .IsSuccessful
                .Should()
                .BeFalse();
        }

        [Fact(DisplayName = "When files were not found - Returns exception")]
        public async Task WhenFilesWereNotFound_ReturnsException()
        {
            var remoteFileFullPath =
                $"{SftpClientBuilder.TestIntegrationFolder}/" +
                $"{Guid.NewGuid()}.txt";

            var request = DownloadFilesRequestBuilder
                .GetValid(remoteFileFullPath);

            var result = await _sftpService
                .BulkDownloadFilesAsync(request);

            result
                .Exception
                .Should()
                .BeOfType<SftpPathNotFoundException>();
        }

        [Fact(DisplayName = "When files were not found - Returns null")]
        public async Task WhenFilesWereNotFound_ReturnsNull()
        {
            var remoteFileFullPath =
                $"{SftpClientBuilder.TestIntegrationFolder}/" +
                $"{Guid.NewGuid()}.txt";

            var request = DownloadFilesRequestBuilder
                .GetValid(remoteFileFullPath);

            var result = await _sftpService
                .BulkDownloadFilesAsync(request);

            result
                .Data
                .Should()
                .BeNull();
        }

        [Fact(DisplayName = "When throws exception - Returns failure")]
        public async Task WhenThrowsException_ReturnsFailure()
        {
            var remoteFileFullPath =
                $"{SftpClientBuilder.NonExistentFolder}/{Guid.NewGuid()}.txt";

            var request = DownloadFilesRequestBuilder
                .GetValid(remoteFileFullPath);

            var result = await _sftpService
                .BulkDownloadFilesAsync(request);

            result
                .IsSuccessful
                .Should()
                .BeFalse();
        }

        [Fact(DisplayName = "When throws exception - Returns exception")]
        public async Task WhenThrowsException_ReturnsException()
        {
            var remoteFileFullPath =
                $"{SftpClientBuilder.NonExistentFolder}/{Guid.NewGuid()}.txt";

            var request = DownloadFilesRequestBuilder
                .GetValid(remoteFileFullPath);

            var result = await _sftpService
                .BulkDownloadFilesAsync(request);

            result
                .Exception
                .Should()
                .BeOfType<SftpPathNotFoundException>();
        }

        [Fact(DisplayName = "When files are found - Returns success")]
        public async Task WhenFilesAreFound_ReturnsSuccess()
        {
            var remoteFileFullPath = await SftpClientBuilder
                .UploadAsync(
                _sftpClient);

            var request = DownloadFilesRequestBuilder
                .GetValid(remoteFileFullPath);

            var result = await _sftpService
                .BulkDownloadFilesAsync(request);

            await SftpClientBuilder.CleanFilesAsync(
                _sftpClient,
                remoteFileFullPath);

            result
                .IsSuccessful
                .Should()
                .BeTrue();
        }

        [Fact(DisplayName = "When files are found - Returns downloaded files")]
        public async Task WhenFilesAreFound_ReturnsDownloadedFiles()
        {
            var remoteFileFullPath = await SftpClientBuilder
                .UploadAsync(
                _sftpClient);

            var request = DownloadFilesRequestBuilder
                .GetValid(remoteFileFullPath);

            var result = await _sftpService
                .BulkDownloadFilesAsync(request);

            await SftpClientBuilder.CleanFilesAsync(
                _sftpClient,
                remoteFileFullPath);

            result
                .Data
                .Files
                .Should()
                .NotBeNullOrEmpty();
        }
    }
}
