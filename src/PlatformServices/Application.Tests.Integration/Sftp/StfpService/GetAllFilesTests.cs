using Application.Sftp;
using Application.Sftp.Model.Request;
using Application.Tests.Integration.Sftp.Builders;
using FluentAssertions;
using Renci.SshNet;
using Renci.SshNet.Common;
using Xunit;

namespace Application.Tests.Integration.Sftp.StfpService
{
    [Trait("SftpService", "GetAllFilesAsync")]
    [Collection("Sequential")]
    public class GetAllFilesTests
    {
        private readonly SftpClient _sftpClient;
        private readonly SftpService _sftpService;

        public GetAllFilesTests()
        {
            _sftpClient = SftpClientBuilder.CreateClient();

            _sftpService = new SftpService(_sftpClient);
        }

        [Fact(DisplayName = "When request is null - Returns failure")]
        public async Task WhenRequestIsNull_ReturnsFailure()
        {
            GetAllFilesRequest request = null;

            var result = await _sftpService
                .GetAllFilesAsync(request);

            result
                .IsSuccessful
                .Should()
                .BeFalse();
        }

        [Fact(DisplayName = "When request is null - Returns failure message")]
        public async Task WhenRequestIsNull_ReturnsFailureMessage()
        {
            GetAllFilesRequest request = null;

            var result = await _sftpService
                .GetAllFilesAsync(request);

            result
                .Message
                .Should()
                .Be("Request is null");
        }

        [Fact(DisplayName = "When request is null - Returns null")]
        public async Task WhenRequestIsNull_ReturnsNull()
        {
            GetAllFilesRequest request = null;

            var result = await _sftpService
                .GetAllFilesAsync(request);

            result
                .Data
                .Should()
                .BeNull();
        }

        [Fact(DisplayName = "When files were not found - Returns failure")]
        public async Task WhenFilesWereNotFound_ReturnsFailure()
        {
            var request = GetAllFilesRequestBuilder
                .GetValid(SftpClientBuilder.TestIntegrationFolder);

            var result = await _sftpService
                .GetAllFilesAsync(request);

            result
                .IsSuccessful
                .Should()
                .BeFalse();
        }

        [Fact(DisplayName = "When files were not found - Returns failure message")]
        public async Task WhenFilesWereNotFound_ReturnsFailureMessage()
        {
            var request = GetAllFilesRequestBuilder
                .GetValid(SftpClientBuilder.TestIntegrationFolder);

            var result = await _sftpService
                .GetAllFilesAsync(request);

            result
                .Message
                .Should()
                .Be("No data found for: GetAllFilesAsync");
        }

        [Fact(DisplayName = "When files were not found - Returns null")]
        public async Task WhenFilesWereNotFound_ReturnsNull()
        {
            var request = GetAllFilesRequestBuilder
                .GetValid(SftpClientBuilder.TestIntegrationFolder);

            var result = await _sftpService
                .GetAllFilesAsync(request);

            result
                .Data
                .Should()
                .BeNull();
        }

        [Fact(DisplayName = "When throws exception - Returns failure")]
        public async Task WhenThrowsException_ReturnsFailure()
        {
            var request = GetAllFilesRequestBuilder
                .GetValid(SftpClientBuilder.NonExistentFolder);

            var result = await _sftpService
                .GetAllFilesAsync(request);

            result
                .IsSuccessful
                .Should()
                .BeFalse();
        }

        [Fact(DisplayName = "When throws exception - Returns exception")]
        public async Task WhenThrowsException_ReturnsException()
        {
            var request = GetAllFilesRequestBuilder
                .GetValid(SftpClientBuilder.NonExistentFolder);

            var result = await _sftpService
                .GetAllFilesAsync(request);

            result
                .Exception
                .Should()
                .BeOfType<SftpPathNotFoundException>();
        }

        [Fact(DisplayName = "When files are found - Returns success")]
        public async Task WhenFilesAreFound_ReturnsSuccess()
        {
            var remoteFileFullPath = await SftpClientBuilder
                .UploadAsync(
                _sftpClient);

            var request = GetAllFilesRequestBuilder
                .GetValid(SftpClientBuilder.TestIntegrationFolder);

            var result = await _sftpService
                .GetAllFilesAsync(request);

            await SftpClientBuilder.CleanFilesAsync(
                _sftpClient,
                remoteFileFullPath);

            result
                .IsSuccessful
                .Should()
                .BeTrue();
        }

        [Fact(DisplayName = "When files are found - Returns the files")]
        public async Task WhenFilesAreFound_ReturnsFiles()
        {
            var remoteFileFullPath = await SftpClientBuilder
                .UploadAsync(
                _sftpClient);

            var request = GetAllFilesRequestBuilder
                .GetValid(SftpClientBuilder.TestIntegrationFolder);

            var result = await _sftpService
                .GetAllFilesAsync(request);

            await SftpClientBuilder.CleanFilesAsync(
                _sftpClient,
                remoteFileFullPath);

            result
                .Data
                .Files
                .Should()
                .NotBeNullOrEmpty();
        }
    }
}
