using Application.Sftp;
using Application.Sftp.Model.Request;
using Application.Tests.Integration.Sftp.Builders;
using FluentAssertions;
using Renci.SshNet;
using Renci.SshNet.Common;
using Xunit;

namespace Application.Tests.Integration.Sftp.StfpService
{
    [Trait("SftpService", "DeleteFilesAsync")]
    [Collection("Sequential")]
    public class DeleteFilesTests
    {
        private readonly SftpClient _sftpClient;
        private readonly SftpService _sftpService;

        public DeleteFilesTests()
        {
            _sftpClient = SftpClientBuilder.CreateClient();

            _sftpService = new SftpService(_sftpClient);
        }

        [Fact(DisplayName = "When request is null - Returns failure")]
        public async Task WhenRequestIsNull_ReturnsFailure()
        {
            DeleteFilesRequest request = null;

            var result = await _sftpService
                .DeleteFilesAsync(request);

            result
                .IsSuccessful
                .Should()
                .BeFalse();
        }

        [Fact(DisplayName = "When request is null - Returns failure message")]
        public async Task WhenRequestIsNull_ReturnsFailureMessage()
        {
            DeleteFilesRequest request = null;

            var result = await _sftpService
                .DeleteFilesAsync(request);

            result
                .Message
                .Should()
                .Be("Request is null");
        }

        [Fact(DisplayName = "When request is null - Returns null")]
        public async Task WhenRequestIsNull_ReturnsNull()
        {
            DeleteFilesRequest request = null;

            var result = await _sftpService
                .DeleteFilesAsync(request);

            result
                .Data
                .Should()
                .BeNull();
        }

        [Fact(DisplayName = "When files were not found - Returns success")]
        public async Task WhenFilesWereNotFound_ReturnsSuccess()
        {
            var request = DeleteFilesRequestBuilder
                .GetValid(SftpClientBuilder.TestIntegrationFolder);

            var result = await _sftpService
                .DeleteFilesAsync(request);

            result
                .IsSuccessful
                .Should()
                .BeTrue();
        }

        [Fact(DisplayName = "When files were not found - Returns empty list")]
        public async Task WhenFilesWereNotFound_ReturnsEmptyList()
        {
            var request = DeleteFilesRequestBuilder
                .GetValid(SftpClientBuilder.TestIntegrationFolder);

            var result = await _sftpService
                .DeleteFilesAsync(request);

            result
                .Data
                .Filenames
                .Should()
                .BeEmpty();
        }

        [Fact(DisplayName = "When throws exception - Returns failure")]
        public async Task WhenThrowsException_ReturnsFailure()
        {
            var request = DeleteFilesRequestBuilder
                .GetValid(SftpClientBuilder.NonExistentFolder);

            var result = await _sftpService
                .DeleteFilesAsync(request);

            result
                .IsSuccessful
                .Should()
                .BeFalse();
        }

        [Fact(DisplayName = "When throws exception - Returns exception")]
        public async Task WhenThrowsException_ReturnsException()
        {
            var request = DeleteFilesRequestBuilder
                .GetValid(SftpClientBuilder.NonExistentFolder);

            var result = await _sftpService
                .DeleteFilesAsync(request);

            result
                .Exception
                .Should()
                .BeOfType<SftpPathNotFoundException>();
        }

        [Fact(DisplayName = "When files are found - Returns success")]
        public async Task WhenFilesAreFound_ReturnsSuccess()
        {
            _ = await SftpClientBuilder
                .UploadAsync(
                _sftpClient);

            var request = DeleteFilesRequestBuilder
                .GetValid(SftpClientBuilder.TestIntegrationFolder);

            var result = await _sftpService
                .DeleteFilesAsync(request);

            result
                .IsSuccessful
                .Should()
                .BeTrue();
        }

        [Fact(DisplayName = "When files are found - Returns the files that were deleted")]
        public async Task WhenFilesAreFound_ReturnsTheFilesThatWereDeleted()
        {
            _ = await SftpClientBuilder
                .UploadAsync(
                _sftpClient);

            var request = DeleteFilesRequestBuilder
                .GetValid(SftpClientBuilder.TestIntegrationFolder);

            var result = await _sftpService
                .DeleteFilesAsync(request);

            result
                .Data
                .Filenames
                .Should()
                .NotBeNullOrEmpty();
        }
    }
}
