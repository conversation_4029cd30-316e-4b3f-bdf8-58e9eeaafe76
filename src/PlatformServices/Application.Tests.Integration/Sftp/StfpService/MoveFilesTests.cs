using Application.Sftp;
using Application.Sftp.Model.Request;
using Application.Tests.Integration.Sftp.Builders;
using FluentAssertions;
using Renci.SshNet;
using Xunit;

namespace Application.Tests.Integration.Sftp.StfpService
{
    [Trait("SftpService", "MoveFilesAsync")]
    [Collection("Sequential")]
    public class MoveFilesTests
    {
        private readonly SftpClient _sftpClient;
        private readonly SftpService _sftpService;

        public MoveFilesTests()
        {
            _sftpClient = SftpClientBuilder.CreateClient();

            _sftpService = new SftpService(_sftpClient);
        }

        [Fact(DisplayName = "When request is null - Returns failure")]
        public async Task WhenRequestIsNull_ReturnsFailure()
        {
            MoveFilesRequest request = null;

            var result = await _sftpService
                .MoveFilesAsync(request);

            result
                .IsSuccessful
                .Should()
                .BeFalse();
        }

        [Fact(DisplayName = "When request is null - Returns failure message")]
        public async Task WhenRequestIsNull_ReturnsFailureMessage()
        {
            MoveFilesRequest request = null;

            var result = await _sftpService
                .MoveFilesAsync(request);

            result
                .Message
                .Should()
                .Be("Request is null");
        }

        [Fact(DisplayName = "When request is null - Returns null")]
        public async Task WhenRequestIsNull_ReturnsNull()
        {
            MoveFilesRequest request = null;

            var result = await _sftpService
                .MoveFilesAsync(request);

            result
                .Data
                .Should()
                .BeNull();
        }

        [Fact(DisplayName = "When files were not found - Returns failure")]
        public async Task WhenFilesWereNotFound_ReturnsFailure()
        {
            var remoteFileFullPath =
                $"{SftpClientBuilder.TestIntegrationFolder}/" +
                $"{Guid.NewGuid()}.txt";

            var request = MoveFilesRequestBuilder.GetValid(
                sourcePath: SftpClientBuilder.TestIntegrationFolder,
                destinationPath: SftpClientBuilder.TestIntegrationProcessedPath,
                remoteFileFullPath: remoteFileFullPath);

            var result = await _sftpService
                .MoveFilesAsync(request);

            result
                .IsSuccessful
                .Should()
                .BeFalse();
        }

        [Fact(DisplayName = "When files were not found - Returns failure message")]
        public async Task WhenFilesWereNotFound_ReturnsFailureMessage()
        {
            var remoteFileFullPath =
                $"{SftpClientBuilder.TestIntegrationFolder}/" +
                $"{Guid.NewGuid()}.txt";

            var request = MoveFilesRequestBuilder.GetValid(
                sourcePath: SftpClientBuilder.TestIntegrationFolder,
                destinationPath: SftpClientBuilder.TestIntegrationProcessedPath,
                remoteFileFullPath: remoteFileFullPath);

            var result = await _sftpService
                .MoveFilesAsync(request);

            result
                .Message
                .Should()
                .Be("No data found for: MoveFilesAsync");
        }

        [Fact(DisplayName = "When files were not found - Returns null")]
        public async Task WhenFilesWereNotFound_ReturnsNull()
        {
            var remoteFileFullPath =
                $"{SftpClientBuilder.TestIntegrationFolder}/" +
                $"{Guid.NewGuid()}.txt";

            var request = MoveFilesRequestBuilder.GetValid(
                sourcePath: SftpClientBuilder.TestIntegrationFolder,
                destinationPath: SftpClientBuilder.TestIntegrationProcessedPath,
                remoteFileFullPath: remoteFileFullPath);

            var result = await _sftpService
                .MoveFilesAsync(request);

            result
                .Data
                .Should()
                .BeNull();
        }

        [Fact(DisplayName = "When throws exception - Returns failure")]
        public async Task WhenThrowsException_ReturnsFailure()
        {
            var remoteFileFullPath =
                $"{SftpClientBuilder.NonExistentFolder}/{Guid.NewGuid()}.txt";

            var request = MoveFilesRequestBuilder.GetValid(
                sourcePath: SftpClientBuilder.NonExistentFolder,
                destinationPath: string.Empty,
                remoteFileFullPath: remoteFileFullPath);

            var result = await _sftpService
                .MoveFilesAsync(request);

            result
                .IsSuccessful
                .Should()
                .BeFalse();
        }

        [Fact(DisplayName = "When throws exception - Returns exception")]
        public async Task WhenThrowsException_ReturnsException()
        {
            var remoteFileFullPath =
                $"{SftpClientBuilder.NonExistentFolder}/{Guid.NewGuid()}.txt";

            var request = MoveFilesRequestBuilder.GetValid(
                sourcePath: SftpClientBuilder.NonExistentFolder,
                destinationPath: string.Empty,
                remoteFileFullPath: remoteFileFullPath);

            var result = await _sftpService
                .MoveFilesAsync(request);

            result
                .Exception
                .Should()
                .BeOfType<ArgumentException>();
        }

        [Fact(DisplayName = "When files are found - Returns success")]
        public async Task WhenFilesAreFound_ReturnsSuccess()
        {
            var remoteFileFullPath = await SftpClientBuilder
                .UploadAsync(_sftpClient);

            var request = MoveFilesRequestBuilder.GetValid(
                sourcePath: SftpClientBuilder.TestIntegrationFolder,
                destinationPath: SftpClientBuilder.TestIntegrationProcessedPath,
                remoteFileFullPath: remoteFileFullPath);

            var result = await _sftpService
                .MoveFilesAsync(request);

            await SftpClientBuilder.CleanFilesAsync(
                _sftpClient,
                $"{SftpClientBuilder.TestIntegrationProcessedPath}/{result.Data.Filenames.First()}");

            result
                .IsSuccessful
                .Should()
                .BeTrue();
        }

        [Fact(DisplayName = "When files are found - Returns moved files")]
        public async Task WhenFilesAreFound_ReturnsMovedFiles()
        {
            var remoteFileFullPath = await SftpClientBuilder
                .UploadAsync(_sftpClient);

            var request = MoveFilesRequestBuilder.GetValid(
                sourcePath: SftpClientBuilder.TestIntegrationFolder,
                destinationPath: SftpClientBuilder.TestIntegrationProcessedPath,
                remoteFileFullPath: remoteFileFullPath);

            var result = await _sftpService
                .MoveFilesAsync(request);

            await SftpClientBuilder.CleanFilesAsync(
                _sftpClient,
                $"{SftpClientBuilder.TestIntegrationProcessedPath}/{result.Data.Filenames.First()}");

            result
                .Data
                .Filenames
                .Should()
                .NotBeNullOrEmpty();
        }
    }
}