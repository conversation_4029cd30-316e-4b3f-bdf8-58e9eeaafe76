using System.Drawing;

namespace Geometry.Core
{
    public struct PointD
    {
        public double X;
        public double Y;

        public PointD(double x, double y)
        {
            X = x;
            Y = y;
        }

        public double XDistance(PointD point)
        {
            return Math.Abs(X - point.X);
        }   

        public readonly Point ToPoint()
        {
            return new Point((int)X, (int)Y);
        }

        public override readonly bool Equals(object obj)
        {
            return obj is PointD d && this == d;
        }

        public override readonly int GetHashCode()
        {
            return X.GetHashCode() ^ Y.GetHashCode();
        }

        public static bool operator ==(PointD a, PointD b)
        {
            return a.X == b.X && a.Y == b.Y;
        }

        public static bool operator !=(PointD a, PointD b)
        {
            return !(a == b);
        }
    }
}
