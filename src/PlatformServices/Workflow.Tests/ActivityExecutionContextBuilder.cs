using Elsa.Builders;
using Elsa.Models;
using Elsa.Services.Models;
using Microsoft.Extensions.DependencyInjection;

namespace Workflow.Tests
{
    public class ActivityExecutionContextBuilder
    {
        public readonly ActivityExecutionContext Context;

        public ActivityExecutionContextBuilder(
            Action<IServiceCollection>? configureServices = default)
        {
            var services = new ServiceCollection()
                .AddElsa(options => options.AddConsoleActivities());

            configureServices?.Invoke(services);

            var serviceProvider = services.BuildServiceProvider();

            var serviceScope = serviceProvider.CreateScope();

            var workflowBuilder = serviceScope.ServiceProvider.GetRequiredService<IWorkflowBuilder>();

            var workflowInstance = new WorkflowInstance();
            workflowInstance.CorrelationId = Guid.NewGuid().ToString();

            var workflowBlueprint = workflowBuilder.Build(new WorkFlowTests());

            var workflowExecutionContext = new WorkflowExecutionContext(
                serviceScope.ServiceProvider,
                workflowBlueprint,
                workflowInstance);

            Context = new ActivityExecutionContext(
                serviceScope.ServiceProvider,
                workflowExecutionContext,
                workflowBlueprint,
                default,
                false,
                new CancellationToken(false));
        }
    }

    public class WorkFlowTests : IWorkflow
    {
        public void Build(IWorkflowBuilder builder) { }
    }
}
