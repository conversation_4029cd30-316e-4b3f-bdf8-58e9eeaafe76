using System.Text.RegularExpressions;
using Application.Apis.Clients;
using Application.Apis.Clients.Response.Instrument;
using Application.Apis.Clients.Response.Reading;
using Dxf.Core.Extensions;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using FluentAssertions;
using Geometry.Core;
using IxMilia.Dxf;
using IxMilia.Dxf.Blocks;
using IxMilia.Dxf.Entities;
using Moq;
using Workflow.StabilityAnalysis.Activities;
using Workflow.StabilityAnalysis.Classes;
using Workflow.Tests.StabilityAnalysis.Builders;
using Xunit;
using static Workflow.Constants.StabilityAnalysisWorkFlow;

namespace Workflow.Tests.StabilityAnalysis;

[Trait("MountSliFilesActivity", "OnExecuteAsync")]
public class MountSliFilesActivityTests
{
    private readonly Mock<IClientsApiService> _clientsApiServiceMock = new();
    private readonly MountSliFilesActivity _activity;
    private readonly ActivityExecutionContext _context;

    public MountSliFilesActivityTests()
    {
        _context = new ActivityExecutionContextBuilder().Context;
        _activity = new(_clientsApiServiceMock.Object);
    }

    [Fact(DisplayName = "When everything is ok in scenario 1 should return Done")]
    public async Task WhenEverythingIsOkInScenario1ShouldReturnDone()
    {
        _context.SetTransientVariable(Variables.StructureInfo, StructureInfoBuilder.CreateScenario1());

        _clientsApiServiceMock
            .Setup(x => x.GetStaticMaterialsBySearchIds(It.IsAny<List<int>>(), It.IsAny<DateTime?>()))
            .ReturnsAsync(StaticMaterialsResponseBuilder.CreateDefault());
        
        _clientsApiServiceMock
            .Setup(api => api.GetInstrumentsByIds(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<GetInstrumentByIdResponse>
            {
                InstrumentResponseBuilder.CreatePiezometer(),
                InstrumentResponseBuilder.CreateWaterLevelIndicator()
            });

        // Act
        var result = await _activity.ExecuteAsync(_context);
        var files = _context.GetTransientVariable<List<SliDetailAggregator>>(Variables.SliFiles);
        
        // Assert   
        Assert.Equal(OutcomeNames.Done, result.As<OutcomeResult>().Outcomes.First());
        Assert.NotNull(files);
        Assert.Single(files);
        Assert.NotNull(files.First().SliFile.Piezos);

        var piezo = files.First().SliFile.Piezos.First();
        var piezoVertices = piezo.Vertices.Select(x => files.First().SliFile.Vertices.First(y => y.Index == x)).ToList();
        var piezoDxfVertexes = piezoVertices.Select(x => new DxfVertex() { Location = new(x.X, x.Y, 0) }).ToList();

        var waterTableVertices = files.First().SliFile.WaterTable.Vertices.Select(x => files.First().SliFile.Vertices.First(y => y.Index == x)).ToList();
        var waterTableDxfVertexes = waterTableVertices.Select(x => new DxfVertex() { Location = new(x.X, x.Y, 0) }).ToList();

        var expectedPiezo = new List<PointD>
        {
            new PointD { X = 123.456789, Y = 835 },
            new PointD { X = 279.475291963928, Y = 835 },
            new PointD { X = 282.1033198900149, Y = 721.893 },
            new PointD { X = 422.12360200861747, Y = 798 },
            new PointD { X = 600.253161652432, Y = 798 }
        };

        var expectedWaterTable = new List<PointD>
        {
            new PointD { X = 282.1033198900149, Y = 750.5 },
            new PointD { X = 422.12360200861747, Y = 798 },
            new PointD { X = 600.253161652432, Y = 798 }
        };

        piezoDxfVertexes.Select(x => new PointD(x.Location.X, x.Location.Y)).Should().BeEquivalentTo(expectedPiezo, options => options.WithStrictOrdering());
        waterTableDxfVertexes.Select(x => new PointD(x.Location.X, x.Location.Y)).Should().BeEquivalentTo(expectedWaterTable, options => options.WithStrictOrdering());
    }

    [Fact(DisplayName = "When everything is ok in scenario 2 should return Done")]
    public async Task WhenEverythingIsOkInScenario2ShouldReturnDone()
    {
        _context.SetTransientVariable(Variables.StructureInfo, StructureInfoBuilder.CreateScenario2());

        _clientsApiServiceMock
            .Setup(x => x.GetStaticMaterialsBySearchIds(It.IsAny<List<int>>(), It.IsAny<DateTime?>()))
            .ReturnsAsync(StaticMaterialsResponseBuilder.CreateDefault());

        _clientsApiServiceMock
            .Setup(api => api.GetInstrumentsByIds(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<GetInstrumentByIdResponse>
            {
                InstrumentResponseBuilder.CreatePiezometer(),
                InstrumentResponseBuilder.CreateWaterLevelIndicator()
            });

        // Act
        var result = await _activity.ExecuteAsync(_context);
        var files = _context.GetTransientVariable<List<SliDetailAggregator>>(Variables.SliFiles);
        
        SaveToFile(files.First(), "scenario-2");
        
        // Assert   
        Assert.Equal(OutcomeNames.Done, result.As<OutcomeResult>().Outcomes.First());
        Assert.NotNull(files);
        Assert.Single(files);
        Assert.NotNull(files.First().SliFile.Piezos);

        var piezo = files.First().SliFile.Piezos.First();
        var piezoVertices = piezo.Vertices.Select(x => files.First().SliFile.Vertices.First(y => y.Index == x)).ToList();
        var piezoDxfVertexes = piezoVertices.Select(x => new DxfVertex() { Location = new(x.X, x.Y, 0) }).ToList();

        var waterTableVertices = files.First().SliFile.WaterTable.Vertices.Select(x => files.First().SliFile.Vertices.First(y => y.Index == x)).ToList();
        var waterTableDxfVertexes = waterTableVertices.Select(x => new DxfVertex() { Location = new(x.X, x.Y, 0) }).ToList();

        var expectedPiezo = new List<PointD>
        {
            new PointD { X = 123.456789, Y = 798.0 },
            new PointD { X = 123.456789, Y = 798.0 },
            new PointD { X = 282.1033198900149, Y = 721.893 },
            new PointD { X = 422.12360200861747, Y = 798.0 },
            new PointD { X = 600.253161652432, Y = 798.0 }
        };

        var expectedWaterTable = new List<PointD>
        {
            new PointD { X = 282.1033198900149, Y = 750.5 },
            new PointD { X = 422.12360200861747, Y = 798 },
            new PointD { X = 600.253161652432, Y = 798 }
        };

        piezoDxfVertexes.Select(x => new PointD(x.Location.X, x.Location.Y)).Should().BeEquivalentTo(expectedPiezo, options => options.WithStrictOrdering());
        waterTableDxfVertexes.Select(x => new PointD(x.Location.X, x.Location.Y)).Should().BeEquivalentTo(expectedWaterTable, options => options.WithStrictOrdering());
    }

    [Fact(DisplayName = "When everything is ok in scenario 3 should return Done")]
    public async Task WhenEverythingIsOkInScenario3ShouldReturnDone()
    {
        _context.SetTransientVariable(Variables.StructureInfo, StructureInfoBuilder.CreateScenario3());

        _clientsApiServiceMock
            .Setup(x => x.GetStaticMaterialsBySearchIds(It.IsAny<List<int>>(), It.IsAny<DateTime?>()))
            .ReturnsAsync(StaticMaterialsResponseBuilder.CreateDefault());

        _clientsApiServiceMock
            .Setup(api => api.GetInstrumentsByIds(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<GetInstrumentByIdResponse>
            {
                InstrumentResponseBuilder.CreatePiezometer(),
                InstrumentResponseBuilder.CreateWaterLevelIndicator()
            });

        // Act
        var result = await _activity.ExecuteAsync(_context);
        var files = _context.GetTransientVariable<List<SliDetailAggregator>>(Variables.SliFiles);

        SaveToFile(files.First(), "scenario-3");
                    
        // Assert   
        Assert.Equal(OutcomeNames.Done, result.As<OutcomeResult>().Outcomes.First());
        Assert.NotNull(files);
        Assert.Single(files);
        Assert.Empty(files.First().SliFile.Piezos);

        var waterTableVertices = files.First().SliFile.WaterTable.Vertices.Select(x => files.First().SliFile.Vertices.First(y => y.Index == x)).ToList();
        var waterTableDxfVertexes = waterTableVertices.Select(x => new DxfVertex() { Location = new(x.X, x.Y, 0) }).ToList();

        var expectedWaterTable = new List<PointD>
        {
            new PointD { X = 123.456789, Y = 500.0 },
            new PointD { X = 123.456789, Y = 500.0 },
            new PointD { X = 282.1033198900149, Y = 750.5 },
            new PointD { X = 422.12360200861747, Y = 798.0 },
            new PointD { X = 600.253161652432, Y = 798.0 }
        };

        waterTableDxfVertexes.Select(x => new PointD(x.Location.X, x.Location.Y)).Should().BeEquivalentTo(expectedWaterTable, options => options.WithStrictOrdering());

    }
    
    [Fact(DisplayName = "When everything is ok in scenario 4 should return Done")]
    public async Task WhenEverythingIsOkInScenario4ShouldReturnDone()
    {
        _context.SetTransientVariable(Variables.StructureInfo, StructureInfoBuilder.CreateScenario4());

        _clientsApiServiceMock
            .Setup(x => x.GetStaticMaterialsBySearchIds(It.IsAny<List<int>>(), It.IsAny<DateTime?>()))
            .ReturnsAsync(StaticMaterialsResponseBuilder.CreateDefault());

        var pz = InstrumentResponseBuilder.CreatePiezometer();
        pz.CoordinateSetting.Systems.Utm.Easting += 56.01;

        _clientsApiServiceMock
            .Setup(api => api.GetInstrumentsByIds(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<GetInstrumentByIdResponse>
            {
                pz,
                InstrumentResponseBuilder.CreateWaterLevelIndicator()
            });

        // Act
        var result = await _activity.ExecuteAsync(_context);
        var files = _context.GetTransientVariable<List<SliDetailAggregator>>(Variables.SliFiles);

        SaveToFile(files.First(), "scenario-4");

        // Assert   
        Assert.Equal(OutcomeNames.Done, result.As<OutcomeResult>().Outcomes.First());
        Assert.NotNull(files);
        Assert.Single(files);
        Assert.NotNull(files.First().SliFile.Piezos);

        var piezo = files.First().SliFile.Piezos.First();
        var piezoVertices = piezo.Vertices.Select(x => files.First().SliFile.Vertices.First(y => y.Index == x)).ToList();
        var piezoDxfVertexes = piezoVertices.Select(x => new DxfVertex() { Location = new(x.X, x.Y, 0) }).ToList();

        var waterTableVertices = files.First().SliFile.WaterTable.Vertices.Select(x => files.First().SliFile.Vertices.First(y => y.Index == x)).ToList();
        var waterTableDxfVertexes = waterTableVertices.Select(x => new DxfVertex() { Location = new(x.X, x.Y, 0) }).ToList();

        var expectedPiezo = new List<PointD>
        {
            new PointD { X = 123.456789, Y = 835.0 },
            new PointD { X = 279.475291963928, Y = 835.0 },
            new PointD { X = 336.85444963658773, Y = 717.893 },
            new PointD { X = 422.12360200861747, Y = 798.0 },
            new PointD { X = 600.253161652432, Y = 798.0 }
        };

        var expectedWaterTable = new List<PointD>
        {
            new PointD { X = 123.456789, Y = 835.0 },
            new PointD { X = 279.475291963928, Y = 835.0 },
            new PointD { X = 282.1033198900149, Y = 750.5 },
            new PointD { X = 422.12360200861747, Y = 798.0 },
            new PointD { X = 600.253161652432, Y = 798.0 }
        };

        piezoDxfVertexes.Select(x => new PointD(x.Location.X, x.Location.Y)).Should().BeEquivalentTo(expectedPiezo, options => options.WithStrictOrdering());
        waterTableDxfVertexes.Select(x => new PointD(x.Location.X, x.Location.Y)).Should().BeEquivalentTo(expectedWaterTable, options => options.WithStrictOrdering());
        
    }

    [Fact(DisplayName = "When everything is ok in scenario 5 should return Done")]
    public async Task WhenEverythingIsOkInScenario5ShouldReturnDone()
    {
        _context.SetTransientVariable(Variables.StructureInfo, StructureInfoBuilder.CreateScenario5());

        _clientsApiServiceMock
            .Setup(x => x.GetStaticMaterialsBySearchIds(It.IsAny<List<int>>(), It.IsAny<DateTime?>()))
            .ReturnsAsync(StaticMaterialsResponseBuilder.CreateDefault());

        var pz = InstrumentResponseBuilder.CreatePiezometer();
        pz.CoordinateSetting.Systems.Utm.Easting += 56;

        _clientsApiServiceMock
            .Setup(api => api.GetInstrumentsByIds(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<GetInstrumentByIdResponse>
            {
                pz,
                InstrumentResponseBuilder.CreateWaterLevelIndicator()
            });

        // Act
        var result = await _activity.ExecuteAsync(_context);
        var files = _context.GetTransientVariable<List<SliDetailAggregator>>(Variables.SliFiles);

        SaveToFile(files.First(), "scenario-5");

        // Assert   
        Assert.Equal(OutcomeNames.Done, result.As<OutcomeResult>().Outcomes.First());
        Assert.NotNull(files);
        Assert.Single(files);
        Assert.Empty(files.First().SliFile.Piezos);

        var waterTableVertices = files.First().SliFile.WaterTable.Vertices.Select(x => files.First().SliFile.Vertices.First(y => y.Index == x)).ToList();
        var waterTableDxfVertexes = waterTableVertices.Select(x => new DxfVertex() { Location = new(x.X, x.Y, 0) }).ToList();

        var expectedWaterTable = new List<PointD>
        {
            new PointD { X = 123.456789, Y = 835.0 },
            new PointD { X = 279.475291963928, Y = 835.0 },
            new PointD { X = 282.1033198900149, Y = 750.5 },
            new PointD { X = 336.8446743948745, Y = 811.0 },
            new PointD { X = 547.805560660665, Y = 758.0 },
            new PointD { X = 600.253161652432, Y = 758.0 }
        };

        waterTableDxfVertexes.Select(x => new PointD(x.Location.X, x.Location.Y)).Should().BeEquivalentTo(expectedWaterTable, options => options.WithStrictOrdering());
        
    }

    [Fact(DisplayName = "When everything is ok in scenario 6 should return Done")]
    public async Task WhenEverythingIsOkInScenario6ShouldReturnDone()
    {
        _context.SetTransientVariable(Variables.StructureInfo, StructureInfoBuilder.CreateScenario6());

        _clientsApiServiceMock
            .Setup(x => x.GetStaticMaterialsBySearchIds(It.IsAny<List<int>>(), It.IsAny<DateTime?>()))
            .ReturnsAsync(StaticMaterialsResponseBuilder.CreateDefault());

        // Act
        var result = await _activity.ExecuteAsync(_context);
        var files = _context.GetTransientVariable<List<SliDetailAggregator>>(Variables.SliFiles);

        SaveToFile(files.First(), "scenario-6");

        // Assert
        Assert.Equal(OutcomeNames.Done, result.As<OutcomeResult>().Outcomes.First());
        Assert.NotNull(files);
        Assert.Single(files);
        Assert.Empty(files.First().SliFile.Piezos);

        var waterTableVertices = files.First().SliFile.WaterTable.Vertices.Select(x => files.First().SliFile.Vertices.First(y => y.Index == x)).ToList();
        var waterTableDxfVertexes = waterTableVertices.Select(x => new DxfVertex() { Location = new(x.X, x.Y, 0) }).ToList();

        var expectedWaterTable = new List<PointD>
        {
            new PointD { X = 123.456789, Y = 716.871545834442 },
            new PointD { X = 312.456789, Y = 766.871545834442 },
            new PointD { X = 223.456789, Y = 816.871545834442 },
            new PointD { X = 451.66049134586603, Y = 786.23007506585304},
            new PointD { X = 600.253161652432, Y = 786.230075065853 }
        };

        waterTableDxfVertexes.Select(x => new PointD(x.Location.X, x.Location.Y)).Should().BeEquivalentTo(expectedWaterTable, options => options.WithStrictOrdering());
        
    }

    [Fact(DisplayName = "When everything is ok in scenario 7 should return Done")]
    public async Task WhenEverythingIsOkInScenario7ShouldReturnDone()
    {
        _context.SetTransientVariable(Variables.StructureInfo, StructureInfoBuilder.CreateScenario7());

        _clientsApiServiceMock
            .Setup(x => x.GetStaticMaterialsBySearchIds(It.IsAny<List<int>>(), It.IsAny<DateTime?>()))
            .ReturnsAsync(StaticMaterialsResponseBuilder.CreateDefault());

        _clientsApiServiceMock
            .Setup(api => api.GetInstrumentsByIds(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<GetInstrumentByIdResponse>
            {
                InstrumentResponseBuilder.CreatePiezometer(),
                InstrumentResponseBuilder.CreateWaterLevelIndicator()
            });

        // Act
        var result = await _activity.ExecuteAsync(_context);
        var files = _context.GetTransientVariable<List<SliDetailAggregator>>(Variables.SliFiles);

        SaveToFile(files.First(), "scenario-7");

        // Assert   
        Assert.Equal(OutcomeNames.Done, result.As<OutcomeResult>().Outcomes.First());
        Assert.NotNull(files);
        Assert.Single(files);
        Assert.Empty(files.First().SliFile.Piezos);

        var waterTableVertices = files.First().SliFile.WaterTable.Vertices.Select(x => files.First().SliFile.Vertices.First(y => y.Index == x)).ToList();
        var waterTableDxfVertexes = waterTableVertices.Select(x => new DxfVertex() { Location = new(x.X, x.Y, 0) }).ToList();

        var expectedWaterTable = new List<PointD>
        {
            new PointD { X = 123.456789, Y = 716.871545834442 },
            new PointD { X = 282.1033198900149, Y = 750.5 },
            new PointD { X = 312.456789, Y = 766.871545834442 },
            new PointD { X = 223.456789, Y = 816.871545834442 },
            new PointD { X = 451.66049134586603, Y = 786.23007506585304 },
            new PointD { X = 600.253161652432, Y = 786.230075065853 }
        };

        waterTableDxfVertexes.Select(x => new PointD(x.Location.X, x.Location.Y)).Should().BeEquivalentTo(expectedWaterTable, options => options.WithStrictOrdering());
        
    }

    [Fact(DisplayName = "When everything is ok in scenario 8 should return Done")]
    public async Task WhenEverythingIsOkInScenario8ShouldReturnDone()
    {
        _context.SetTransientVariable(Variables.StructureInfo, StructureInfoBuilder.CreateScenario8());

        _clientsApiServiceMock
            .Setup(x => x.GetStaticMaterialsBySearchIds(It.IsAny<List<int>>(), It.IsAny<DateTime?>()))
            .ReturnsAsync(StaticMaterialsResponseBuilder.CreateDefault());

        var pz = InstrumentResponseBuilder.CreatePiezometer();
        pz.CoordinateSetting.Systems.Utm.Easting += 30;

        _clientsApiServiceMock
            .Setup(api => api.GetInstrumentsByIds(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<GetInstrumentByIdResponse>
            {
                pz,
                InstrumentResponseBuilder.CreateWaterLevelIndicator()
            });

        // Act
        var result = await _activity.ExecuteAsync(_context);
        var files = _context.GetTransientVariable<List<SliDetailAggregator>>(Variables.SliFiles);

        SaveToFile(files.First(), "scenario-8");

        // Assert   
        Assert.Equal(OutcomeNames.Done, result.As<OutcomeResult>().Outcomes.First());
        Assert.NotNull(files);
        Assert.Single(files);
        Assert.Empty(files.First().SliFile.Piezos);

        var waterTableVertices = files.First().SliFile.WaterTable.Vertices.Select(x => files.First().SliFile.Vertices.First(y => y.Index == x)).ToList();
        var waterTableDxfVertexes = waterTableVertices.Select(x => new DxfVertex() { Location = new(x.X, x.Y, 0) }).ToList();

        var expectedWaterTable = new List<PointD>
        {
            new PointD { X = 123.456789, Y = 716.871545834442 },
            new PointD { X = 282.1033198900149, Y = 750.5 },
            new PointD { X = 312.456789, Y = 766.87 },
            new PointD { X = 223.456789, Y = 816.871545834442 },
            new PointD { X = 451.66049134586603, Y = 786.23007506585304 },
            new PointD { X = 600.253161652432, Y = 786.230075065853 }
        };

        waterTableDxfVertexes.Select(x => new PointD(x.Location.X, x.Location.Y)).Should().BeEquivalentTo(expectedWaterTable, options => options.WithStrictOrdering());
        
    }

    [Fact(DisplayName = "When everything is ok in scenario 9 should return Done")]
    public async Task WhenEverythingIsOkInScenario9ShouldReturnDone()
    {
        _context.SetTransientVariable(Variables.StructureInfo, StructureInfoBuilder.CreateScenario9());

        _clientsApiServiceMock
            .Setup(x => x.GetStaticMaterialsBySearchIds(It.IsAny<List<int>>(), It.IsAny<DateTime?>()))
            .ReturnsAsync(StaticMaterialsResponseBuilder.CreateDefault());

        var pz = InstrumentResponseBuilder.CreatePiezometer();
        pz.CoordinateSetting.Systems.Utm.Easting += 30;

        _clientsApiServiceMock
            .Setup(api => api.GetInstrumentsByIds(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<GetInstrumentByIdResponse>
            {
                pz,
                InstrumentResponseBuilder.CreateWaterLevelIndicator()
            });

        // Act
        var result = await _activity.ExecuteAsync(_context);
        var files = _context.GetTransientVariable<List<SliDetailAggregator>>(Variables.SliFiles);

        SaveToFile(files.First(), "scenario-9");

        // Assert   
        Assert.Equal(OutcomeNames.Done, result.As<OutcomeResult>().Outcomes.First());
        Assert.NotNull(files);
        Assert.Single(files);

        var waterTableVertices = files.First().SliFile.WaterTable.Vertices.Select(x => files.First().SliFile.Vertices.First(y => y.Index == x)).ToList();
        var waterTableDxfVertexes = waterTableVertices.Select(x => new DxfVertex() { Location = new(x.X, x.Y, 0) }).ToList();

        var expectedWaterTable = new List<PointD>
        {
            new PointD { X = 123.456789, Y = 798.0 },
            new PointD { X = 123.456789, Y = 798.0 },
            new PointD { X = 123.456789, Y = 798.0 },
            new PointD { X = 223.456789, Y = 816.871545834442 },
            new PointD { X = 282.1033198900149, Y = 766.79 },
            new PointD { X = 312.456789, Y = 756.87 },
            new PointD { X = 600.253161652432, Y = 758.0 },
            new PointD { X = 595.4851979259078, Y = 758.0 },
            new PointD { X = 600.253161652432, Y = 758.0 },
        };

        waterTableDxfVertexes.Select(x => new PointD(x.Location.X, x.Location.Y)).Should().BeEquivalentTo(expectedWaterTable, options => options.WithStrictOrdering());
        
    }

    [Fact(DisplayName = "When everything is ok in scenario 10 should return Done")]
    public async Task WhenEverythingIsOkInScenario10ShouldReturnDone()
    {
        _context.SetTransientVariable(Variables.StructureInfo, StructureInfoBuilder.CreateScenario10());

        _clientsApiServiceMock
            .Setup(x => x.GetStaticMaterialsBySearchIds(It.IsAny<List<int>>(), It.IsAny<DateTime?>()))
            .ReturnsAsync(StaticMaterialsResponseBuilder.CreateDefault());

        var pz = InstrumentResponseBuilder.CreatePiezometer();
        pz.CoordinateSetting.Systems.Utm.Easting += 30;

        _clientsApiServiceMock
            .Setup(api => api.GetInstrumentsByIds(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<GetInstrumentByIdResponse>
            {
                pz,
                InstrumentResponseBuilder.CreateWaterLevelIndicator()
            });

        // Act
        var result = await _activity.ExecuteAsync(_context);
        var files = _context.GetTransientVariable<List<SliDetailAggregator>>(Variables.SliFiles);

        SaveToFile(files.First(), "scenario-10");

        // Assert   
        Assert.Equal(OutcomeNames.Done, result.As<OutcomeResult>().Outcomes.First());
        Assert.NotNull(files);
        Assert.Single(files);

        var waterTableVertices = files.First().SliFile.WaterTable.Vertices.Select(x => files.First().SliFile.Vertices.First(y => y.Index == x)).ToList();
        var waterTableDxfVertexes = waterTableVertices.Select(x => new DxfVertex() { Location = new(x.X, x.Y, 0) }).ToList();

        var expectedWaterTable = new List<PointD>
        {
            new PointD { X = 123.456789, Y = 831 },
            new PointD { X = 270.7151335132963, Y = 831 },
            new PointD { X = 282.1033198900149, Y = 766.79 },
            new PointD { X = 312.456789, Y = 756.87 },
            new PointD { X = 418.5237822462833, Y = 800 },
            new PointD { X = 600.253161652432, Y = 800 }
        };

        waterTableDxfVertexes.Select(x => new PointD(x.Location.X, x.Location.Y)).Should().BeEquivalentTo(expectedWaterTable, options => options.WithStrictOrdering());
        
    }

    [Fact(DisplayName = "When everything is ok in scenario 11 should return Done")]
    public async Task WhenEverythingIsOkInScenario11ShouldReturnDone()
    {
        _context.SetTransientVariable(Variables.StructureInfo, StructureInfoBuilder.CreateScenario11());

        _clientsApiServiceMock
            .Setup(x => x.GetStaticMaterialsBySearchIds(It.IsAny<List<int>>(), It.IsAny<DateTime?>()))
            .ReturnsAsync(StaticMaterialsResponseBuilder.CreateDefault());

        _clientsApiServiceMock
            .Setup(api => api.GetInstrumentsByIds(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<GetInstrumentByIdResponse>
            {
                InstrumentResponseBuilder.CreatePiezometer(),
                InstrumentResponseBuilder.CreateWaterLevelIndicator()
            });

        // Act
        var result = await _activity.ExecuteAsync(_context);
        var files = _context.GetTransientVariable<List<SliDetailAggregator>>(Variables.SliFiles);

        SaveToFile(files.First(), "scenario-11");

        // Assert   
        Assert.Equal(OutcomeNames.Done, result.As<OutcomeResult>().Outcomes.First());
        Assert.NotNull(files);
        Assert.Single(files);
        Assert.Empty(files.First().SliFile.Piezos);

        var waterTableVertices = files.First().SliFile.WaterTable.Vertices.Select(x => files.First().SliFile.Vertices.First(y => y.Index == x)).ToList();
        var waterTableDxfVertexes = waterTableVertices.Select(x => new DxfVertex() { Location = new(x.X, x.Y, 0) }).ToList();

        var expectedWaterTable = new List<PointD>
        {
            new PointD { X = 123.456789, Y = 765.5609301957762 },
            new PointD { X = 123.456789, Y = 765.5609301957762 },
            new PointD { X = 282.1033198900149, Y = 750.5 },
            new PointD { X = 422.12360200861747, Y = 798 },
            new PointD { X = 600.253161652432, Y = 798 }
        };

        waterTableDxfVertexes.Select(x => new PointD(x.Location.X, x.Location.Y)).Should().BeEquivalentTo(expectedWaterTable, options => options.WithStrictOrdering());
    }

    [Fact(DisplayName = "When everything is ok in scenario 12 should return Done")]
    public async Task WhenEverythingIsOkInScenario12ShouldReturnDone()
    {
        _context.SetTransientVariable(Variables.StructureInfo, StructureInfoBuilder.CreateScenario12());

        _clientsApiServiceMock
            .Setup(x => x.GetStaticMaterialsBySearchIds(It.IsAny<List<int>>(), It.IsAny<DateTime?>()))
            .ReturnsAsync(StaticMaterialsResponseBuilder.CreateDefault());

        _clientsApiServiceMock
            .Setup(api => api.GetInstrumentsByIds(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<GetInstrumentByIdResponse>
            {
                InstrumentResponseBuilder.CreatePiezometer(),
                InstrumentResponseBuilder.CreateWaterLevelIndicator()
            });

        // Act
        var result = await _activity.ExecuteAsync(_context);
        var files = _context.GetTransientVariable<List<SliDetailAggregator>>(Variables.SliFiles);
        
        SaveToFile(files.First(), "scenario-12");

        // Assert   
        Assert.Equal(OutcomeNames.Done, result.As<OutcomeResult>().Outcomes.First());
        Assert.NotNull(files);
        Assert.Single(files);
        Assert.Empty(files.First().SliFile.Piezos);

        var waterTableVertices = files.First().SliFile.WaterTable.Vertices.Select(x => files.First().SliFile.Vertices.First(y => y.Index == x)).ToList();
        var waterTableDxfVertexes = waterTableVertices.Select(x => new DxfVertex() { Location = new(x.X, x.Y, 0) }).ToList();

        var expectedWaterTable = new List<PointD>
        {
            new PointD { X = 123.456789, Y = 826 },
            new PointD { X = 236.812927696626, Y = 826 },
            new PointD { X = 282.1033198900149, Y = 750.5 },
            new PointD { X = 422.12360200861747, Y = 798 },
            new PointD { X = 600.253161652432, Y = 798 }
        };

        waterTableDxfVertexes.Select(x => new PointD(x.Location.X, x.Location.Y)).Should().BeEquivalentTo(expectedWaterTable, options => options.WithStrictOrdering());
        
    }

    [Fact(DisplayName = "When slope limits in scenario 1 should return Done")]
    public async Task WhenSlopeLimitsInScenario1ShouldReturnDone()
    {
        _context.SetTransientVariable(Variables.StructureInfo, StructureInfoBuilder.CreateScenario1());

        _clientsApiServiceMock
            .Setup(x => x.GetStaticMaterialsBySearchIds(It.IsAny<List<int>>(), It.IsAny<DateTime?>()))
            .ReturnsAsync(StaticMaterialsResponseBuilder.CreateDefault());

        _clientsApiServiceMock
            .Setup(api => api.GetInstrumentsByIds(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<GetInstrumentByIdResponse>
            {
                InstrumentResponseBuilder.CreatePiezometer(),
                InstrumentResponseBuilder.CreateWaterLevelIndicator()
            });

        // Act
        var result = await _activity.ExecuteAsync(_context);
        var files = _context.GetTransientVariable<List<SliDetailAggregator>>(Variables.SliFiles);
        
        SaveToFile(files.First(), "slope-limits-scenario-1");

        // Assert   
        Assert.Equal(OutcomeNames.Done, result.As<OutcomeResult>().Outcomes.First());
        Assert.NotNull(files);
        Assert.Single(files);

        var slopeLimits = files.First().SliFile.SlopeLimits;

        var expectedX1 = 123.456789;
        var expectedY1 = 826;
        var expectedX2 = 600.25316165243;
        var expectedY2 = 776.95947833478;

        slopeLimits.X1.Should().Be(expectedX1);
        slopeLimits.Y1.Should().Be(expectedY1);
        slopeLimits.X2.Should().Be(expectedX2);
        slopeLimits.Y2.Should().Be(expectedY2);
        slopeLimits.X3.Should().Be(null);
        slopeLimits.Y3.Should().Be(null);
        slopeLimits.X4.Should().Be(null);
        slopeLimits.Y4.Should().Be(null);
        
    }

    [Fact(DisplayName = "When slope limits in scenario 13 should return Done")]
    public async Task WhenSlopeLimitsInScenario13ShouldReturnDone()
    {
        _context.SetTransientVariable(Variables.StructureInfo, StructureInfoBuilder.CreateScenario13());

        _clientsApiServiceMock
            .Setup(x => x.GetStaticMaterialsBySearchIds(It.IsAny<List<int>>(), It.IsAny<DateTime?>()))
            .ReturnsAsync(StaticMaterialsResponseBuilder.CreateDefault());

        _clientsApiServiceMock
            .Setup(api => api.GetInstrumentsByIds(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<GetInstrumentByIdResponse>
            {
                InstrumentResponseBuilder.CreatePiezometer(),
                InstrumentResponseBuilder.CreateWaterLevelIndicator()
            });

        // Act
        var result = await _activity.ExecuteAsync(_context);
        var files = _context.GetTransientVariable<List<SliDetailAggregator>>(Variables.SliFiles);

        SaveToFile(files.First(), "slope-limits-scenario-13");

        // Assert   
        Assert.Equal(OutcomeNames.Done, result.As<OutcomeResult>().Outcomes.First());
        Assert.NotNull(files);
        Assert.Single(files);

        var slopeLimits = files.First().SliFile.SlopeLimits;

        var expectedX1 = 170.6744;
        var expectedY1 = 826;
        var expectedX2 = 349.5701;
        var expectedY2 = 818.8124092987733;
        var expectedX3 = 452.5008;
        var expectedY3 = 785.8098709567984;
        var expectedX4 = 577.4647;
        var expectedY4 = 776.297863364887;

        slopeLimits.X1.Should().Be(expectedX1);
        slopeLimits.Y1.Should().Be(expectedY1);
        slopeLimits.X2.Should().Be(expectedX2);
        slopeLimits.Y2.Should().Be(expectedY2);
        slopeLimits.X3.Should().Be(expectedX3);
        slopeLimits.Y3.Should().Be(expectedY3);
        slopeLimits.X4.Should().Be(expectedX4);
        slopeLimits.Y4.Should().Be(expectedY4);
        
    }

    [Fact(DisplayName = "When slope limits in scenario 14 should return Done")]
    public async Task WhenSlopeLimitsInScenario14ShouldReturnDone()
    {   
        _context.SetTransientVariable(Variables.StructureInfo, StructureInfoBuilder.CreateScenario14());

        _clientsApiServiceMock
            .Setup(x => x.GetStaticMaterialsBySearchIds(It.IsAny<List<int>>(), It.IsAny<DateTime?>()))
            .ReturnsAsync(StaticMaterialsResponseBuilder.CreateDefault());

        _clientsApiServiceMock
            .Setup(api => api.GetInstrumentsByIds(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<GetInstrumentByIdResponse>
            {
                InstrumentResponseBuilder.CreatePiezometer(),
                InstrumentResponseBuilder.CreateWaterLevelIndicator()
            });

        // Act
        var result = await _activity.ExecuteAsync(_context);
        var files = _context.GetTransientVariable<List<SliDetailAggregator>>(Variables.SliFiles);

        SaveToFile(files.First(), "slope-limits-scenario-14");

        // Assert   
        Assert.Equal(OutcomeNames.Done, result.As<OutcomeResult>().Outcomes.First());
        Assert.NotNull(files);
        Assert.Single(files);

        var slopeLimits = files.First().SliFile.SlopeLimits;

        var expectedX1 = 123.456789;
        var expectedY1 = 826;
        var expectedX2 = 600.25316165243;
        var expectedY2 = 776.95947833478;

        slopeLimits.X1.Should().Be(expectedX1);
        slopeLimits.Y1.Should().Be(expectedY1);
        slopeLimits.X2.Should().Be(expectedX2);
        slopeLimits.Y2.Should().Be(expectedY2);
        slopeLimits.X3.Should().Be(null);
        slopeLimits.Y3.Should().Be(null);
        slopeLimits.X4.Should().Be(null);
        slopeLimits.Y4.Should().Be(null);

    }

    private void SaveToFile(SliDetailAggregator sliFile, string scenario)
    {
        var newDxf = new DxfFile();
        newDxf.Header.Version = DxfAcadVersion.R2018;
        newDxf.Header.CreationDate = DateTime.UtcNow;

        var externalEntities = sliFile.DxfFile.Entities.Where(x => x.Layer.ToLower() == "external");

        foreach (var entity in externalEntities)
        {
            newDxf.Entities.Add(entity);
        }

        var materialEntities = sliFile.DxfFile.Entities.Where(x => x.Layer.ToLower() == "material");

        foreach (var entity in materialEntities)
        {
            newDxf.Entities.Add(entity);
        }
        var solids = new List<DxfSolid>();

        foreach (var triangle in sliFile.SliFile.Cells)
        {
            var vertice1 = sliFile.SliFile.Vertices.First(x => x.Index == triangle.Vertice1);
            var vertice2 = sliFile.SliFile.Vertices.First(x => x.Index == triangle.Vertice2);
            var vertice3 = sliFile.SliFile.Vertices.First(x => x.Index == triangle.Vertice3);

            var materialIndex = int.Parse(Regex.Match(triangle.Material, @"\d+").Value);
            var materialProperties = sliFile.SliFile.MaterialProperties[materialIndex - 1];

            if (newDxf.Layers.All(x => x.Name != $" {materialProperties.Name}"))
            {
                newDxf.Layers.Add(new DxfLayer($" {materialProperties.Name}", DxfFileExtensions.FromRgb(materialProperties.Red, materialProperties.Green, materialProperties.Blue)));
            }

            var solid = new DxfSolid
            {
                Color = DxfFileExtensions.FromRgb(materialProperties.Red, materialProperties.Green, materialProperties.Blue),
                Layer = $" {materialProperties.Name}",
                FirstCorner = new DxfPoint(vertice1.X, vertice1.Y, 0),
                SecondCorner = new DxfPoint(vertice2.X, vertice2.Y, 0),
                ThirdCorner = new DxfPoint(vertice3.X, vertice3.Y, 0),
                FourthCorner = new DxfPoint(vertice1.X, vertice1.Y, 0),
                ExtrusionDirection = new DxfVector(0, 0, 1)
            };

            solids.Add(solid);
        }

        var solidsGrouped = solids.GroupBy(x => x.Layer);

        foreach (var group in solidsGrouped)
        {
            var block = new DxfBlock()
            {
                Name = group.Key,
                IsAnonymous = true,
                BasePoint = new DxfPoint(0, 0, 0)
            };

            foreach (var solid in group)
            {
                block.Entities.Add(solid);
            }

            newDxf.Blocks.Add(block);
            newDxf.BlockRecords.Add(new DxfBlockRecord(block.Name));

            var insert = new DxfInsert
            {
                Layer = group.Key,
                Name = group.Key,
                Location = new DxfPoint(0, 0, 0)
            };

            newDxf.Entities.Add(insert);
        }

        var allPoints = sliFile.DxfFile.GetAllPoints();

        var maxPoint = new PointD(allPoints.Max(x => x.X), allPoints.Max(x => x.Y));

        var scaleFactor = Math.Min(maxPoint.X, maxPoint.Y) / 100.0;
        if (scaleFactor <= 0)
        {
            scaleFactor = 1.0;
        }

        var textHeight = 2.2 * scaleFactor;

        if (sliFile.SliFile.Piezos.Any())
        {
            newDxf.Layers.Add(new DxfLayer("Piezométrica", DxfColor.FromIndex(143)));

            foreach (var piezo in sliFile.SliFile.Piezos)
            {
                var vertices = piezo.Vertices.Select(x => sliFile.SliFile.Vertices.First(y => y.Index == x)).ToList();
                var dxfVertexes = vertices.Select(x => new DxfVertex() { Location = new(x.X, x.Y, 0) }).ToList();

                if (!dxfVertexes.Any())
                {
                    continue;
                }   

                var polyline = new DxfPolyline(dxfVertexes)
                {
                    Layer = "Piezométrica",
                    IsClosed = false,
                    Color = DxfColor.FromIndex(143)
                };

                newDxf.Entities.Add(polyline);

                var piezoText = new DxfText
                {
                    Layer = "Piezométrica",
                    Color = DxfColor.FromIndex(143),
                    Value = "Piezométrica",
                    TextHeight = textHeight,
                    Location = new DxfPoint(vertices.First().X, vertices.First().Y, 0)
                };
                newDxf.Entities.Add(piezoText);
            }
        }

        if (sliFile.SliFile.WaterTable.Vertices.Any())
        {
            var waterTableVertices = sliFile.SliFile.WaterTable.Vertices.Select(x => sliFile.SliFile.Vertices.First(y => y.Index == x)).ToList();
            var waterTableDxfVertexes = waterTableVertices.Select(x => new DxfVertex() { Location = new(x.X, x.Y, 0) }).ToList();

            newDxf.Layers.Add(new DxfLayer("Freática", DxfColor.FromIndex(5)));

            var waterTablePolyline = new DxfPolyline(waterTableDxfVertexes)
            {
                Layer = "Freática",
                IsClosed = false,
                Color = DxfColor.FromIndex(5)
            };

            newDxf.Entities.Add(waterTablePolyline);

            var waterTableText = new DxfText
            {
                Layer = "Freática",
                Color = DxfColor.FromIndex(5),
                Value = "Freática",
                TextHeight = textHeight,
                Location = new DxfPoint(waterTableDxfVertexes.First().Location.X, waterTableDxfVertexes.First().Location.Y, 0)
            };

            newDxf.Entities.Add(waterTableText);
        }

        foreach (var instrument in sliFile.Instruments.Piezometers.Concat(sliFile.Instruments.WaterLevelIndicators))
        {
            if (instrument.InstrumentInfo == null)
            {
                continue;
            }

            var y1 = (double)(instrument.InstrumentInfo.BaseQuota ?? instrument.InstrumentInfo?.Measurements?.Min(x => x.Quota));
            var y2 = (double)instrument.InstrumentInfo.TopQuota;

            var x = instrument.PointD.X;

            var waterTableVertices = sliFile.SliFile.WaterTable.Vertices.Select(x => sliFile.SliFile.Vertices.First(y => y.Index == x)).ToList();
            var waterTableDxfVertexes = waterTableVertices.Select(x => new DxfVertex() { Location = new(x.X, x.Y, 0) }).ToList();

            if (!waterTableDxfVertexes.Any(z => z.Location == new DxfPoint(instrument.PointD.X, instrument.PointD.Y, 0)))
            {
                continue;
            }

            if (sliFile.SliFile.Piezos.Any())
            {
                foreach (var piezo in sliFile.SliFile.Piezos)
                {
                    var vertices = piezo.Vertices.Select(x => sliFile.SliFile.Vertices.First(y => y.Index == x)).ToList();
                    var dxfVertexes = vertices.Select(x => new DxfVertex() { Location = new(x.X, x.Y, 0) }).ToList();

                    if (!dxfVertexes.Any(z => z.Location == new DxfPoint(instrument.PointD.X, instrument.PointD.Y, 0)))
                    {
                        continue;
                    }
                }
            }

            //Crete a line for the instrument
            var line = new DxfLine(new DxfPoint(x, y1, 0), new DxfPoint(x, y2, 0))
            {
                Layer = "Instrumento",
                Color = DxfColor.FromIndex(8)
            };

            newDxf.Entities.Add(line);

            // Write the coordinates of the instrument
            var text = new DxfText
            {
                Location = new DxfPoint(x, instrument.PointD.Y, 0),
                Value = $"{instrument.PointD.Y}",
                TextHeight = textHeight,
                Layer = $"Cota",
                Color = DxfColor.FromIndex(8)
            };

            newDxf.Entities.Add(text);

            newDxf.Layers.Add(new DxfLayer("Slope_Limits", DxfColor.FromIndex(0)));

            newDxf.Entities.Add(new DxfCircle()
            {
                Layer = "Slope_Limits",
                Color = DxfColor.FromIndex(0),
                Center = new DxfPoint(sliFile.SliFile.SlopeLimits.X1, sliFile.SliFile.SlopeLimits.Y1, 0),
                Radius = scaleFactor
            });

            newDxf.Entities.Add(new DxfCircle()
            {
                Layer = "Slope_Limits",
                Color = DxfColor.FromIndex(0),
                Center = new DxfPoint(sliFile.SliFile.SlopeLimits.X2, sliFile.SliFile.SlopeLimits.Y2, 0),
                Radius = scaleFactor
            });

            if (sliFile.SliFile.SlopeLimits.X3.HasValue && sliFile.SliFile.SlopeLimits.Y3.HasValue)
            {
                newDxf.Entities.Add(new DxfCircle()
                {
                    Layer = "Slope_Limits",
                    Color = DxfColor.FromIndex(0),
                    Center = new DxfPoint(sliFile.SliFile.SlopeLimits.X3.Value, sliFile.SliFile.SlopeLimits.Y3.Value, 0),
                    Radius = scaleFactor
                });
            }

            if (sliFile.SliFile.SlopeLimits.X4.HasValue && sliFile.SliFile.SlopeLimits.Y4.HasValue)
            {
                newDxf.Entities.Add(new DxfCircle()
                {
                    Layer = "Slope_Limits",
                    Color = DxfColor.FromIndex(0),
                    Center = new DxfPoint(sliFile.SliFile.SlopeLimits.X4.Value, sliFile.SliFile.SlopeLimits.Y4.Value, 0),
                    Radius = scaleFactor
                });
            }
        }

        newDxf.Normalize();

        newDxf.Save(@$"{Environment.CurrentDirectory}\dxf\test-{scenario}.dxf");
    }
}
