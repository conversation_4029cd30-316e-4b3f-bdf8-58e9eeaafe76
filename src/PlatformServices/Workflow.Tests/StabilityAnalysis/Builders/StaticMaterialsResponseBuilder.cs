using Application.Apis.Clients.Response._Shared;
using Application.Apis.Clients.Response.StaticMaterial.GetBySearchId;
using Domain.Enums;

namespace Workflow.Tests.StabilityAnalysis.Builders
{
    public static class StaticMaterialsResponseBuilder
    {
        public static List<GetStaticMaterialBySearchIdResponse> CreateDefault()
        {
            return new List<GetStaticMaterialBySearchIdResponse>
            {
                new GetStaticMaterialBySearchIdResponse
                {
                    Id = Guid.Parse("d11840d2-29d7-4d9b-9357-ff89d8116df8"),
                    SearchIdentifier = 19,
                    Client = new ClientResponse
                    {
                        Id = Guid.Parse("624be98d-e4a8-45ba-a270-9d8a94a51703"),
                        Name = "Mosaic",
                        Active = true
                    },
                    ClientUnit = new ClientUnitResponse
                    {
                        Id = Guid.Parse("8165d564-3e51-42a1-834c-2663d8256696"),
                        Name = "UPM",
                        Active = true,
                        ClientId = Guid.Parse("624be98d-e4a8-45ba-a270-9d8a94a51703")
                    },
                    Structure = new StructureResponse
                    {
                        Id = Guid.Parse("640f0c7e-d313-485d-814b-3295cb06858c"),
                        Name = "SantaRita",
                        CoordinateSetting = null
                    },
                    CreatedBy = new UserResponse
                    {
                        Id = Guid.Parse("17747b46-fd86-4276-ab51-2268b5ca14c4"),
                        Username = "igor.oliveira",
                        FirstName = "Igor",
                        Surname = "Oliveira"
                    },
                    Name = "Rocha 5",
                    Active = true,
                    DrainedStaticMaterialValue = new GetStaticMaterialValueBySearchId
                    {
                        Id = Guid.Parse("a0baa1ad-6838-4e90-bace-1dcb65bf415c"),
                        Color = "#3636ff",
                        NaturalSpecificWeight = 21,
                        ConstitutiveModel = Domain.Enums.ConstitutiveModel.Shansep,
                        TensileStrength = 57,
                        M = 22,
                        S = 1,
                        A = 5,
                        MaximumShearStrength = 25,
                        StressHistoryType = (StressHistoryType)1,
                        StressHistoryMethod = (StressHistoryMethod)2,
                        Hu = Domain.Enums.Hu.Custom,
                        CustomHuValue = 1,
                        WaterSurface = (WaterSurface)1,
                        PointValues = new List<PointValue>
                        {
                            new PointValue
                            {
                                Id = Guid.Parse("be660a89-4f98-4f9c-922c-7041c3bf7f20"),
                                Value1 = 15,
                                Value2 = 16,
                                Index = 1
                            },
                            new PointValue
                            {
                                Id = Guid.Parse("47617a16-0413-4221-8a03-731ef74943fc"),
                                Value1 = 17,
                                Value2 = 18,
                                Index = 2
                            }
                        }
                    },
                    UndrainedStaticMaterialValue = new GetStaticMaterialValueBySearchId
                    {
                        Id = Guid.Parse("2a477d90-1b11-41fc-91ae-118503802efd"),
                        Color = "#2a2ab8",
                        NaturalSpecificWeight = 100,
                        ConstitutiveModel = (ConstitutiveModel)6,
                        UcsIntact = 45.004646,
                        M = 45.4654646,
                        S = 1,
                        Hu = (Hu)1,
                        CustomHuValue = 0.5,
                        UseDrainedResistanceOverWaterSurface = false,
                        WaterSurface = (WaterSurface)1,
                        PointValues = new List<PointValue>()
                    },
                    PseudoStaticStaticMaterialValue = new GetStaticMaterialValueBySearchId
                    {
                        Id = Guid.Parse("5f894c2b-21b4-4680-b26b-7ed8b7fe44a3"),
                        Color = "#151573",
                        NaturalSpecificWeight = 25,
                        ConstitutiveModel = (ConstitutiveModel)6,
                        UcsIntact = 47,
                        M = 48,
                        S = 1,
                        Hu = (Hu)1,
                        CustomHuValue = 1,
                        WaterSurface = (WaterSurface)1,
                        PointValues = new List<PointValue>()
                    }
                },
                new GetStaticMaterialBySearchIdResponse
                {
                    Id = Guid.Parse("c4bc886d-f980-465c-a1b7-7bb07c814e92"),
                    SearchIdentifier = 15,
                    Client = new ClientResponse
                    {
                        Id = Guid.Parse("624be98d-e4a8-45ba-a270-9d8a94a51703"),
                        Name = "Mosaic",
                        Active = true
                    },
                    ClientUnit = new ClientUnitResponse
                    {
                        Id = Guid.Parse("8165d564-3e51-42a1-834c-2663d8256696"),
                        Name = "UPM",
                        Active = true,
                        ClientId = Guid.Parse("624be98d-e4a8-45ba-a270-9d8a94a51703")
                    },
                    Structure = new StructureResponse
                    {
                        Id = Guid.Parse("640f0c7e-d313-485d-814b-3295cb06858c"),
                        Name = "SantaRita",
                        CoordinateSetting = null
                    },
                    CreatedBy = new UserResponse
                    {
                        Id = Guid.Parse("17747b46-fd86-4276-ab51-2268b5ca14c4"),
                        Username = "igor.oliveira",
                        FirstName = "Igor",
                        Surname = "Oliveira"
                    },
                    Name = "Rocha 1",
                    Active = true,
                    DrainedStaticMaterialValue = new GetStaticMaterialValueBySearchId
                    {
                        Id = Guid.Parse("740a97ee-5617-4e52-9b40-927e3dfcad8b"),
                        Color = "#9a5100",
                        NaturalSpecificWeight = 20,
                        ConstitutiveModel = (ConstitutiveModel)9,
                        TensileStrength = 56,
                        M = 18,
                        S = 1,
                        A = 2,
                        MaximumShearStrength = 24,
                        StressHistoryType = (StressHistoryType)1,
                        StressHistoryMethod = (StressHistoryMethod)2,
                        Hu = (Hu)1,
                        CustomHuValue = 1,
                        WaterSurface = (WaterSurface)1,
                        PointValues = new List<PointValue>()
                    },
                    UndrainedStaticMaterialValue = new GetStaticMaterialValueBySearchId
                    {
                        Id = Guid.Parse("3edfe112-00b8-41fd-a0d5-4afb35f7c0b6"),
                        Color = "#6b3800",
                        NaturalSpecificWeight = 99,
                        ConstitutiveModel = (ConstitutiveModel)6,
                        UcsIntact = 45.004646,
                        M = 45.4654646,
                        S = 1,
                        Hu = (Hu)1,
                        CustomHuValue = 0.5,
                        WaterSurface = (WaterSurface)1,
                        PointValues = new List<PointValue>()
                    },
                    PseudoStaticStaticMaterialValue = null
                },
                new GetStaticMaterialBySearchIdResponse
                {
                    Id = Guid.Parse("fa62c79a-d2b8-4e82-8305-0b5a82ffb317"),
                    SearchIdentifier = 18,
                    Client = new ClientResponse
                    {
                        Id = Guid.Parse("624be98d-e4a8-45ba-a270-9d8a94a51703"),
                        Name = "Mosaic",
                        Active = true
                    },
                    ClientUnit = new ClientUnitResponse
                    {
                        Id = Guid.Parse("8165d564-3e51-42a1-834c-2663d8256696"),
                        Name = "UPM",
                        Active = true,
                        ClientId = Guid.Parse("624be98d-e4a8-45ba-a270-9d8a94a51703")
                    },
                    Structure = new StructureResponse
                    {
                        Id = Guid.Parse("640f0c7e-d313-485d-814b-3295cb06858c"),
                        Name = "SantaRita",
                        CoordinateSetting = null
                    },
                    CreatedBy = new UserResponse
                    {
                        Id = Guid.Parse("17747b46-fd86-4276-ab51-2268b5ca14c4"),
                        Username = "igor.oliveira",
                        FirstName = "Igor",
                        Surname = "Oliveira"
                    },
                    Name = "Rocha 4",
                    Active = true,
                    DrainedStaticMaterialValue = new GetStaticMaterialValueBySearchId
                    {
                        Id = Guid.Parse("717af6d7-d7f5-4772-ad07-0bec683cd9bc"),
                        Color = "#005050",
                        NaturalSpecificWeight = 21,
                        ConstitutiveModel = (ConstitutiveModel)9,
                        TensileStrength = 57,
                        M = 22,
                        S = 1,
                        A = 5,
                        MaximumShearStrength = 25,
                        StressHistoryType = (StressHistoryType)1,
                        StressHistoryMethod = (StressHistoryMethod)2,
                        Hu = (Hu)1,
                        CustomHuValue = 1,
                        UseDrainedResistanceOverWaterSurface = true,
                        WaterSurface = (WaterSurface)1,
                        PointValues = new List<PointValue>
                        {
                            new PointValue
                            {
                                Id = Guid.Parse("f2aeec5c-cf1d-49c1-a6cb-0cd33a520d09"),
                                Value1 = 15,
                                Value2 = 16,
                                Index = 1
                            },
                            new PointValue
                            {
                                Id = Guid.Parse("0455db90-873b-4841-bf7a-2ac23175f9fd"),
                                Value1 =  17,
                                Value2 = 18,
                                Index = 2
                            }
                        }
                    },
                    UndrainedStaticMaterialValue = new GetStaticMaterialValueBySearchId
                    {
                        Id = Guid.Parse("c2f35783-e7f2-40f0-bdcf-a71c10e0fe30"),
                        Color = "#002929",
                        NaturalSpecificWeight = 100,
                        ConstitutiveModel = (ConstitutiveModel)6,
                        UcsIntact = 45.004646,
                        M = 45.4654646,
                        S = 1,
                        Hu = (Hu)1,
                        CustomHuValue = 0.5,
                        WaterSurface = (WaterSurface)1,
                        PointValues = new List<PointValue>()
                    },
                    PseudoStaticStaticMaterialValue = new GetStaticMaterialValueBySearchId
                    {
                        Id = Guid.Parse("281d7b64-42c1-4d67-957a-47a65776c4c6"),
                        Color = "#001717",
                        NaturalSpecificWeight = 25,
                        ConstitutiveModel = (ConstitutiveModel)6,
                        UcsIntact = 47,
                        M = 48,
                        S = 1,
                        Hu = (Hu)1,
                        CustomHuValue = 1,
                        WaterSurface = (WaterSurface)1,
                        PointValues = new List<PointValue>()
                    }
                },
                new GetStaticMaterialBySearchIdResponse
                {
                    Id = Guid.Parse("3e3af15b-f25c-40c2-800c-b6067360e126"),
                    SearchIdentifier = 16,
                    Client = new ClientResponse
                    {
                        Id = Guid.Parse("624be98d-e4a8-45ba-a270-9d8a94a51703"),
                        Name = "Mosaic",
                        Active = true
                    },
                    ClientUnit = new ClientUnitResponse
                    {
                        Id = Guid.Parse("8165d564-3e51-42a1-834c-2663d8256696"),
                        Name = "UPM",
                        Active = true,
                        ClientId = Guid.Parse("624be98d-e4a8-45ba-a270-9d8a94a51703")
                    },
                    Structure = new StructureResponse
                    {
                        Id = Guid.Parse("640f0c7e-d313-485d-814b-3295cb06858c"),
                        Name = "SantaRita",
                        CoordinateSetting = null
                    },
                    CreatedBy = new UserResponse
                    {
                        Id = Guid.Parse("17747b46-fd86-4276-ab51-2268b5ca14c4"),
                        Username = "igor.oliveira",
                        FirstName = "Igor",
                        Surname = "Oliveira"
                    },
                    Name = "Rocha 2",
                    Active = true,
                    DrainedStaticMaterialValue = new GetStaticMaterialValueBySearchId
                    {
                        Id = Guid.Parse("6b17fb3c-4925-4b6c-b616-19b2eb8e01e4"),
                        Color = "#ff8883",
                        NaturalSpecificWeight = 20,
                        ConstitutiveModel = (ConstitutiveModel)9,
                        TensileStrength = 56,
                        M = 19,
                        S = 1,
                        A = 3,
                        MaximumShearStrength = 24,
                        StressHistoryType = (StressHistoryType)1,
                        StressHistoryMethod = (StressHistoryMethod)2,
                        Hu = (Hu)1,
                        CustomHuValue = 1,
                        UseDrainedResistanceOverWaterSurface = true,
                        WaterSurface = (WaterSurface)1,
                        PointValues = new List<PointValue>
                        {
                            new PointValue
                            {
                                Id = Guid.Parse("653f0b28-1a78-454d-9b8f-d1db857f7cdc"),
                                Value1 = 1,
                                Value2 = 2,
                                Index = 1
                            },
                            new PointValue
                            {
                                Id = Guid.Parse("2f1dfb9d-25de-4916-9119-26e6dff4b6c7"),
                                Value1 = 3,
                                Value2 = 4,
                                Index = 2
                            }
                        }
                    },
                    UndrainedStaticMaterialValue = new GetStaticMaterialValueBySearchId
                    {
                        Id = Guid.Parse("f0d37838-860b-4e0a-b30e-ac363d450cbe"),
                        Color = "#7d0701",
                        NaturalSpecificWeight = 100,
                        ConstitutiveModel = (ConstitutiveModel)6,
                        UcsIntact = 45,
                        M = 45.47,
                        S = 1,
                        Hu = (Hu)1,
                        CustomHuValue = 0.5,
                        WaterSurface = (WaterSurface)1,
                        PointValues = new List<PointValue>()
                    },
                    PseudoStaticStaticMaterialValue = new GetStaticMaterialValueBySearchId
                    {
                        Id = Guid.Parse("014be241-961f-4b80-a11b-db4d971e4552"),
                        Color = "#370300",
                        NaturalSpecificWeight = 23,
                        ConstitutiveModel = (ConstitutiveModel)6,
                        UcsIntact = 46,
                        M = 47,
                        S = 1,
                        Hu = (Hu)1,
                        CustomHuValue = 1,
                        WaterSurface = (WaterSurface)1,
                        PointValues = new List<PointValue>()
                    }
                },
                new GetStaticMaterialBySearchIdResponse
                {
                    Id = Guid.Parse("0a728d48-9511-4638-bbba-9ed098106ffa"),
                    SearchIdentifier = 17,
                    Client = new ClientResponse
                    {
                        Id = Guid.Parse("624be98d-e4a8-45ba-a270-9d8a94a51703"),
                        Name = "Mosaic",
                        Active = true
                    },
                    ClientUnit = new ClientUnitResponse
                    {
                        Id = Guid.Parse("8165d564-3e51-42a1-834c-2663d8256696"),
                        Name = "UPM",
                        Active = true,
                        ClientId = Guid.Parse("624be98d-e4a8-45ba-a270-9d8a94a51703")
                    },
                    Structure = new StructureResponse
                    {
                        Id = Guid.Parse("640f0c7e-d313-485d-814b-3295cb06858c"),
                        Name = "SantaRita",
                        CoordinateSetting = null
                    },
                    CreatedBy = new UserResponse
                    {
                        Id = Guid.Parse("17747b46-fd86-4276-ab51-2268b5ca14c4"),
                        Username = "igor.oliveira",
                        FirstName = "Igor",
                        Surname = "Oliveira"
                    },
                    Name = "Rocha 3",
                    Active = true,
                    DrainedStaticMaterialValue = new GetStaticMaterialValueBySearchId
                    {
                        Id = Guid.Parse("57675290-855f-469d-ba7a-40a244bf264f"),
                        Color = "#360000",
                        NaturalSpecificWeight = 21,
                        ConstitutiveModel = (ConstitutiveModel)9,
                        TensileStrength = 57,
                        M = 21,
                        S = 1,
                        A = 4,
                        MaximumShearStrength = 24,
                        StressHistoryType = (StressHistoryType)1,
                        StressHistoryMethod = (StressHistoryMethod)2,
                        Hu = (Hu)1,
                        CustomHuValue = 1,
                        WaterSurface = (WaterSurface) 1,
                        PointValues = new List<PointValue>
                        {
                            new PointValue
                            {
                                Id = Guid.Parse("ffbefa67-cc1b-4f3f-8d2f-809c91a7368a"),
                                Value1 = 9,
                                Value2 = 10,
                                Index = 1
                            },
                            new PointValue
                            {
                                Id = Guid.Parse("ead43fd4-4eca-4228-b8cf-a87a09254081"),
                                Value1 = 11,
                                Value2 = 12,
                                Index = 2
                            }
                        }
                    },
                    UndrainedStaticMaterialValue = new GetStaticMaterialValueBySearchId
                    {
                        Id = Guid.Parse("030ff5f9-f0c3-4cae-aefa-df595916c609"),
                        Color = "#1f0000",
                        NaturalSpecificWeight = 100,
                        ConstitutiveModel = Domain.Enums.ConstitutiveModel.HoekBrown,
                        UcsIntact = 45.004646,
                        M = 45.4654646,
                        S = 1,
                        Hu =  Domain.Enums.Hu.Custom,
                        CustomHuValue = 0.5,
                        WaterSurface = Domain.Enums.WaterSurface.None,
                        PointValues = new List<PointValue>()
                    },
                    PseudoStaticStaticMaterialValue = new GetStaticMaterialValueBySearchId
                    {
                        Id = Guid.Parse("43208299-6a57-411a-ae8d-26f90f2dae8d"),
                        Color = "#080000",
                        NaturalSpecificWeight = 24,
                        ConstitutiveModel = Domain.Enums.ConstitutiveModel.HoekBrown,
                        UcsIntact = 46,
                        M = 47,
                        S = 1,
                        Hu = Domain.Enums.Hu.Custom,
                        CustomHuValue = 1,
                        WaterSurface = Domain.Enums.WaterSurface.None,
                        PointValues = new List<PointValue>()
                    }
                }
            };
        }
    }
}
