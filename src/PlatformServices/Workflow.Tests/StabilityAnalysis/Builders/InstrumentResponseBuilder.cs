using Application.Apis.Clients.Response._Shared;
using Application.Apis.Clients.Response.Instrument;
using Application.Apis.Clients.Response.Report;
using Coordinate.Core.Classes;
using Coordinate.Core.Enums;
using Domain.Enums;

namespace Workflow.Tests.StabilityAnalysis.Builders
{
    public static class InstrumentResponseBuilder
    {
        public static GetInstrumentByIdResponse CreatePiezometer()
        {
            return new()
            {
                Id = Guid.Parse("4cee7fd1-92c9-4b30-a973-6c79a54dab79"),
                SearchIdentifier = 41,
                Type = (InstrumentType)2,
                Client = new()
                {
                    Id = Guid.Parse("624be98d-e4a8-45ba-a270-9d8a94a51703"),
                    Name = "Mosaic",
                    Active = true
                },
                ClientUnit = new()
                {
                    Id = Guid.Parse("8165d564-3e51-42a1-834c-2663d8256696"),
                    Name = "UPM",
                    Active = true,
                    ClientId = Guid.Parse("624be98d-e4a8-45ba-a270-9d8a94a51703")
                },
                Structure = new()
                {
                    Id = Guid.Parse("640f0c7e-d313-485d-814b-3295cb06858c"),
                    Name = "SantaRita",
                    CoordinateSetting = null
                },
                Identifier = "PZ-14",
                AlternativeName = "",
                CoordinateSetting = new()
                {
                    Datum = (Datum)2,
                    Format = (Coordinate.Core.Enums.Format)1,
                    Systems = new()
                    {
                        Utm = new Utm
                        {
                            ZoneNumber = 24,
                            ZoneLetter = 'L',
                            Northing = 8431753.943351882,
                            Easting = 425069.2627944602
                        },
                        DecimalGeodetic = new DecimalGeodetic
                        {
                            Latitude = -14.184532,
                            Longitude = -39.694405
                        }
                    }
                },
                TopQuota = 794.396m,
                BaseQuota = 700,
                Automated = true,
                Online = true,
                InstallationDate = DateTime.Parse("2022-02-01T00:00:00"),
                DryType = (DryType)2,
                SecurityLevels = new()
                {
                    new()
                    {
                        Id = Guid.Parse("7ef65268-61ae-420d-b5a6-fb1464d3fc97"),
                        Attention = 0,
                        Alert = 0,
                        Emergency = 0,
                        AbruptVariationBetweenReadings = 0,
                        MaximumDailyRainfall = null,
                        RainIntensity = null,
                        Axis = null
                    }
                },
                Measurements = new()
            };
        }

        public static GetInstrumentByIdResponse CreateWaterLevelIndicator()
        {
            return new()
            {
                Id = Guid.Parse("6ba19301-718a-4c96-9d44-00b317567485"),
                SearchIdentifier = 163,
                Type = (InstrumentType)1,
                Client = new()
                {
                    Id = Guid.Parse("624be98d-e4a8-45ba-a270-9d8a94a51703"),
                    Name = "Mosaic",
                    Active = true
                },
                ClientUnit = new()
                {
                    Id = Guid.Parse("8165d564-3e51-42a1-834c-2663d8256696"),
                    Name = "UPM",
                    Active = true,
                    ClientId = Guid.Parse("624be98d-e4a8-45ba-a270-9d8a94a51703")
                },
                Structure = new()
                {
                    Id = Guid.Parse("640f0c7e-d313-485d-814b-3295cb06858c"),
                    Name = "SantaRita",
                    CoordinateSetting = null
                },
                Identifier = "INA-3004",
                AlternativeName = "",
                CoordinateSetting = new CoordinateSetting
                {
                    Datum = (Datum)2,
                    Format = (Coordinate.Core.Enums.Format)1,
                    Systems = new()
                    {
                        Utm = new Utm
                        {
                            ZoneNumber = 24,
                            ZoneLetter = 'L',
                            Northing = 8431753.943351882,
                            Easting = 425069.2627944602
                        },
                        DecimalGeodetic = new DecimalGeodetic
                        {
                            Latitude = -14.184532,
                            Longitude = -39.694405
                        }
                    }
                },
                TopQuota = 786.87m,
                BaseQuota = 700m,
                Automated = true,
                Online = true,
                Elevation = null,
                InstallationDate = DateTime.Parse("2024-04-30T00:00:00"),
                DryType = (DryType)2,
                SecurityLevels = new()
                {
                    new()
                    {
                        Id = Guid.Parse("c4fa1158-5d2f-4650-80ed-25ef39b5b93c"),
                        Attention = null,
                        Alert = null,
                        Emergency = null,
                        AbruptVariationBetweenReadings = null,
                        MaximumDailyRainfall = null,
                        RainIntensity = null,
                        Axis = null
                    }
                },
                Measurements = new()
            };
        }
    }
}
