using Application.Apis.Clients.Response.Instrument;
using Bogus;
using Domain.Enums;
using FluentAssertions;
using Geometry.Core;
using Workflow.Extensions;
using Workflow.StabilityAnalysis.Classes;
using Xunit;

namespace Workflow.Tests.Extensions.InstrumentExtensions;

[Trait("InstrumentExtensions", "YCoordinateIsValid")]
public class YCoordinateIsValidTests
{
    private readonly Faker _faker = new();

    [Fact(DisplayName = "When reading is not dry and Y coordinate is not default, should return true")]
    public void WhenReadingIsNotDryAndYCoordinateIsNotDefault_ShouldReturnTrue()
    {
        var instrument = new Instrument
        {
            PointD = new PointD(_faker.Random.Double(1, 1000), _faker.Random.Double(1, 1000)),
            DryReading = false,
            InstrumentInfo = null
        };

        var result = instrument.YCoordinateIsValid();

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When reading is not dry but Y coordinate is default, should return false")]
    public void WhenReadingIsNotDryButYCoordinateIsDefault_ShouldReturnFalse()
    {
        var instrument = new Instrument
        {
            PointD = new PointD(_faker.Random.Double(1, 1000), 0),
            DryReading = false,
            InstrumentInfo = null
        };

        var result = instrument.YCoordinateIsValid();

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When reading is dry, Y coordinate is not default, and dry type is Base, should return true")]
    public void WhenReadingIsDryAndYCoordinateIsNotDefaultAndDryTypeIsBase_ShouldReturnTrue()
    {
        var instrument = new Instrument
        {
            PointD = new PointD(_faker.Random.Double(1, 1000), _faker.Random.Double(1, 1000)),
            DryReading = true,
            InstrumentInfo = new GetInstrumentByIdResponse
            {
                DryType = DryType.Base
            }
        };

        var result = instrument.YCoordinateIsValid();

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When reading is dry, Y coordinate is not default, but dry type is Interpolated, should return false")]
    public void WhenReadingIsDryAndYCoordinateIsNotDefaultButDryTypeIsInterpolated_ShouldReturnFalse()
    {
        var instrument = new Instrument
        {
            PointD = new PointD(_faker.Random.Double(1, 1000), _faker.Random.Double(1, 1000)),
            DryReading = true,
            InstrumentInfo = new GetInstrumentByIdResponse
            {
                DryType = DryType.Interpolated
            }
        };

        var result = instrument.YCoordinateIsValid();

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When reading is dry but Y coordinate is default, should return false")]
    public void WhenReadingIsDryButYCoordinateIsDefault_ShouldReturnFalse()
    {
        var instrument = new Instrument
        {
            PointD = new PointD(_faker.Random.Double(1, 1000), 0),
            DryReading = true,
            InstrumentInfo = new GetInstrumentByIdResponse
            {
                DryType = DryType.Base
            }
        };

        var result = instrument.YCoordinateIsValid();

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When reading is dry, Y coordinate is not default, but InstrumentInfo is null, should return false")]
    public void WhenReadingIsDryAndYCoordinateIsNotDefaultButInstrumentInfoIsNull_ShouldReturnFalse()
    {
        var instrument = new Instrument
        {
            PointD = new PointD(_faker.Random.Double(1, 1000), _faker.Random.Double(1, 1000)),
            DryReading = true,
            InstrumentInfo = null
        };

        var result = instrument.YCoordinateIsValid();

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When reading is dry, Y coordinate is not default, but DryType is null, should return false")]
    public void WhenReadingIsDryAndYCoordinateIsNotDefaultButDryTypeIsNull_ShouldReturnFalse()
    {
        var instrument = new Instrument
        {
            PointD = new PointD(_faker.Random.Double(1, 1000), _faker.Random.Double(1, 1000)),
            DryReading = true,
            InstrumentInfo = new GetInstrumentByIdResponse
            {
                DryType = null
            }
        };

        var result = instrument.YCoordinateIsValid();

        result.Should().BeFalse();
    }

    [Theory(DisplayName = "When Y coordinate is exactly zero, should return false regardless of other conditions")]
    [InlineData(true, DryType.Base)]
    [InlineData(true, DryType.Interpolated)]
    [InlineData(false, DryType.Base)]
    [InlineData(false, DryType.Interpolated)]
    public void WhenYCoordinateIsExactlyZero_ShouldReturnFalse(bool dryReading, DryType dryType)
    {
        var instrument = new Instrument
        {
            PointD = new PointD(_faker.Random.Double(1, 1000), 0.0),
            DryReading = dryReading,
            InstrumentInfo = new GetInstrumentByIdResponse
            {
                DryType = dryType
            }
        };

        var result = instrument.YCoordinateIsValid();

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When Y coordinate is negative and reading is not dry, should return true")]
    public void WhenYCoordinateIsNegativeAndReadingIsNotDry_ShouldReturnTrue()
    {
        var instrument = new Instrument
        {
            PointD = new PointD(_faker.Random.Double(1, 1000), _faker.Random.Double(-1000, -1)),
            DryReading = false,
            InstrumentInfo = null
        };

        var result = instrument.YCoordinateIsValid();

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When Y coordinate is negative, reading is dry, and dry type is Base, should return true")]
    public void WhenYCoordinateIsNegativeAndReadingIsDryAndDryTypeIsBase_ShouldReturnTrue()
    {
        var instrument = new Instrument
        {
            PointD = new PointD(_faker.Random.Double(1, 1000), _faker.Random.Double(-1000, -1)),
            DryReading = true,
            InstrumentInfo = new GetInstrumentByIdResponse
            {
                DryType = DryType.Base
            }
        };

        var result = instrument.YCoordinateIsValid();

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When Y coordinate is very small positive value and reading is not dry, should return true")]
    public void WhenYCoordinateIsVerySmallPositiveValueAndReadingIsNotDry_ShouldReturnTrue()
    {
        var instrument = new Instrument
        {
            PointD = new PointD(_faker.Random.Double(1, 1000), 0.0001),
            DryReading = false,
            InstrumentInfo = null
        };

        var result = instrument.YCoordinateIsValid();

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When Y coordinate is very large value and reading is not dry, should return true")]
    public void WhenYCoordinateIsVeryLargeValueAndReadingIsNotDry_ShouldReturnTrue()
    {
        var instrument = new Instrument
        {
            PointD = new PointD(_faker.Random.Double(1, 1000), _faker.Random.Double(10000, 100000)),
            DryReading = false,
            InstrumentInfo = null
        };

        var result = instrument.YCoordinateIsValid();

        result.Should().BeTrue();
    }
}
