using FluentAssertions;
using Slide.Core.Objects.Sli;
using Workflow.Extensions;
using Workflow.StabilityAnalysis.Classes;
using Xunit;

namespace Workflow.Tests.Extensions.SlopeLimitsExtensions;

[Trait("SlopeLimitsExtensions", "IsWithinSlopeAlert")]
public class IsWithinSlopeAlertTests
{
    private const double MinDistSlopeAlert = 1;

    [Theory(DisplayName =
        "When distance to slope limit X1 is less than alert limit, then returns true")]
    [InlineData(100, 101)]
    [InlineData(101, 100)]
    [InlineData(100, 100.5)]
    [InlineData(100.5, 100)]
    [InlineData(100, 100)]
    public void WhenDistanceToSlopeLimitX1IsLessThanAlertLimit_ReturnsTrue(
        double slopeLimitX1,
        double referentialX)
    {
        var slopeLimit = new SlopeLimits
        {
            X1 = slopeLimitX1,
            X2 = 20,
            X3 = null,
            X4 = null
        };

        var globalMinFs = new GlobalMinimumFs
        {
            X1 = referentialX,
            X2 = 500
        };

        var result =
            slopeLimit.IsWithinSlopeAlert(globalMinFs, MinDistSlopeAlert);

        result.Should().BeTrue();
    }

    [Theory(DisplayName =
        "When distance to slope limit X2 is less than alert limit, then returns true")]
    [InlineData(100, 101)]
    [InlineData(101, 100)]
    [InlineData(100, 100.5)]
    [InlineData(100.5, 100)]
    [InlineData(100, 100)]
    public void WhenDistanceToSlopeLimitX2IsLessThanAlertLimit_ReturnsTrue(
        double slopeLimitX2,
        double referentialX)
    {
        var slopeLimit = new SlopeLimits
        {
            X1 = 20,
            X2 = slopeLimitX2,
            X3 = null,
            X4 = null
        };

        var globalMinFs = new GlobalMinimumFs
        {
            X1 = 500,
            X2 = referentialX
        };

        var result =
            slopeLimit.IsWithinSlopeAlert(globalMinFs, MinDistSlopeAlert);

        result.Should().BeTrue();
    }

    [Theory(DisplayName =
        "When distance to slope limit X3 is less than alert limit, then returns true")]
    [InlineData(100, 101)]
    [InlineData(101, 100)]
    [InlineData(100, 100.5)]
    [InlineData(100.5, 100)]
    [InlineData(100, 100)]
    public void WhenDistanceToSlopeLimitX3IsLessThanAlertLimit_ReturnsTrue(
        double slopeLimitX3,
        double referentialX)
    {
        var slopeLimit = new SlopeLimits
        {
            X1 = 20,
            X2 = 30,
            X3 = slopeLimitX3,
            X4 = null
        };

        var globalMinFs = new GlobalMinimumFs
        {
            X1 = 500,
            X2 = referentialX
        };

        var result =
            slopeLimit.IsWithinSlopeAlert(globalMinFs, MinDistSlopeAlert);

        result.Should().BeTrue();
    }

    [Theory(DisplayName =
        "When distance to slope limit X4 is less than alert limit, then returns true")]
    [InlineData(100, 101)]
    [InlineData(101, 100)]
    [InlineData(100, 100.5)]
    [InlineData(100.5, 100)]
    [InlineData(100, 100)]
    public void WhenDistanceToSlopeLimitX4IsLessThanAlertLimit_ReturnsTrue(
        double slopeLimitX4,
        double referentialX)
    {
        var slopeLimit = new SlopeLimits
        {
            X1 = 20,
            X2 = 30,
            X4 = null,
            X3 = slopeLimitX4,
        };

        var globalMinFs = new GlobalMinimumFs
        {
            X1 = 500,
            X2 = referentialX
        };

        var result =
            slopeLimit.IsWithinSlopeAlert(globalMinFs, MinDistSlopeAlert);

        result.Should().BeTrue();
    }

    [Theory(DisplayName =
        "When distance to slope limit X1 is greater than alert limit, then returns false")]
    [InlineData(100, 101.5)]
    [InlineData(100, 200)]
    public void WhenDistanceToSlopeLimitX1IsGreaterThanAlertLimit_ReturnsFalse(
        double slopeLimitX1,
        double referentialX)
    {
        var slopeLimit = new SlopeLimits
        {
            X1 = slopeLimitX1,
            X2 = 20,
            X3 = null,
            X4 = null
        };

        var globalMinFs = new GlobalMinimumFs
        {
            X1 = referentialX,
            X2 = 500
        };

        var result =
            slopeLimit.IsWithinSlopeAlert(globalMinFs, MinDistSlopeAlert);

        result.Should().BeFalse();
    }

    [Theory(DisplayName =
        "When distance to slope limit X2 is greater than alert limit, then returns false")]
    [InlineData(100, 101.5)]
    [InlineData(100, 200)]
    public void WhenDistanceToSlopeLimitX2IsGreaterThanAlertLimit_ReturnsFalse(
        double slopeLimitX2,
        double referentialX)
    {
        var slopeLimit = new SlopeLimits
        {
            X1 = 20,
            X2 = slopeLimitX2,
            X3 = null,
            X4 = null
        };

        var globalMinFs = new GlobalMinimumFs
        {
            X1 = referentialX,
            X2 = 500
        };

        var result =
            slopeLimit.IsWithinSlopeAlert(globalMinFs, MinDistSlopeAlert);

        result.Should().BeFalse();
    }

    [Theory(DisplayName =
        "When distance to slope limit X3 is greater than alert limit, then returns false")]
    [InlineData(100, 101.5)]
    [InlineData(100, 200)]
    public void WhenDistanceToSlopeLimitX3IsGreaterThanAlertLimit_ReturnsFalse(
        double slopeLimitX3,
        double referentialX)
    {
        var slopeLimit = new SlopeLimits
        {
            X1 = 20,
            X2 = 30,
            X3 = slopeLimitX3,
            X4 = null
        };

        var globalMinFs = new GlobalMinimumFs
        {
            X1 = 500,
            X2 = referentialX
        };

        var result =
            slopeLimit.IsWithinSlopeAlert(globalMinFs, MinDistSlopeAlert);

        result.Should().BeFalse();
    }

    [Theory(DisplayName =
        "When distance to slope limit X4 is greater than alert limit, then returns false")]
    [InlineData(100, 101.5)]
    [InlineData(100, 200)]
    public void WhenDistanceToSlopeLimitX4IsGreaterThanAlertLimit_ReturnsFalse(
        double slopeLimitX4,
        double referentialX)
    {
        var slopeLimit = new SlopeLimits
        {
            X1 = 20,
            X2 = 25,
            X3 = 30,
            X4 = slopeLimitX4,
        };

        var globalMinFs = new GlobalMinimumFs
        {
            X1 = 500,
            X2 = referentialX
        };

        var result =
            slopeLimit.IsWithinSlopeAlert(globalMinFs, MinDistSlopeAlert);

        result.Should().BeFalse();
    }
}