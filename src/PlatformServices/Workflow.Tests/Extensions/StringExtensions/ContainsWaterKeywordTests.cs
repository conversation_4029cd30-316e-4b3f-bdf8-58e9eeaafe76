using Bogus;
using Workflow.Extensions;

namespace Workflow.Tests.Extensions.StringExtensions;

[<PERSON><PERSON><PERSON>("StringExtensions", "ContainsWaterKeyword")]
public class ContainsWaterKeywordTests
{
    [Theory(DisplayName =
        "When input contains water keyword, then return true")]
    [InlineData("waterline")]
    [InlineData("WATERLINE")]
    [InlineData("linha d'agua")]
    [InlineData("LINHA D'AGUA")]
    [InlineData("piezo")]
    [InlineData("PIEZO")]
    [InlineData("piezometrica")]
    [InlineData("PIEZOMETRICA")]
    [InlineData("watertable")]
    [InlineData("WATERTABLE")]
    [InlineData("water table")]
    [InlineData("WATER TABLE")]
    [InlineData("wt")]
    [InlineData("WT")]
    [InlineData("linha da agua")]
    [InlineData("LINHA DA AGUA")]
    [InlineData("freática")]
    [InlineData("freatica")]
    [InlineData("FREÁTICA")]
    [InlineData("FREATICA")]
    public void WhenInputContainsWaterKeyword_ThenReturnTrue(string input)
    {
        var result = input.ContainsWaterKeyword();

        result.Should().BeTrue();
    }

    [Fact(DisplayName =
        "When input does not contain water keyword, then return false")]
    public void WhenInputDoesNotContainsWaterKeyword_ThenReturnFalse()
    {
        var input = new Faker().Lorem.Sentence();

        var result = input.ContainsWaterKeyword();

        result.Should().BeFalse();
    }
}