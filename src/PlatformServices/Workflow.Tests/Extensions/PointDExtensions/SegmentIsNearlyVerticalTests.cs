using Bogus;
using Geometry.Core;
using Workflow.Extensions;

namespace Workflow.Tests.Extensions.PointDExtensions;

[Trait("PointDExtensions", "SegmentIsNearlyVertical")]
public class SegmentIsNearlyVerticalTests
{
    private readonly Faker _faker = new();
    private const double VerticalTolerance = 1e-10;

    [Fact(DisplayName = "When points have identical X coordinates, should return true")]
    public void WhenPointsHaveIdenticalXCoordinates_ShouldReturnTrue()
    {
        var x = _faker.Random.Double(-1000, 1000);
        var y1 = _faker.Random.Double(-1000, 1000);
        var y2 = _faker.Random.Double(-1000, 1000);
        var point1 = new PointD(x, y1);
        var point2 = new PointD(x, y2);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When X difference is exactly at vertical tolerance, should return true")]
    public void WhenXDifferenceIsExactlyAtVerticalTolerance_ShouldReturnTrue()
    {
        var x1 = 100.0;
        var x2 = x1 + VerticalTolerance;
        var point1 = new PointD(x1, _faker.Random.Double());
        var point2 = new PointD(x2, _faker.Random.Double());

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When X difference is just below vertical tolerance, should return true")]
    public void WhenXDifferenceIsJustBelowVerticalTolerance_ShouldReturnTrue()
    {
        var x1 = 50.0;
        var x2 = x1 + (VerticalTolerance * 0.5);
        var point1 = new PointD(x1, _faker.Random.Double());
        var point2 = new PointD(x2, _faker.Random.Double());

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When points are identical, should return true")]
    public void WhenPointsAreIdentical_ShouldReturnTrue()
    {
        var x = _faker.Random.Double(-500, 500);
        var y = _faker.Random.Double(-500, 500);
        var point1 = new PointD(x, y);
        var point2 = new PointD(x, y);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }
    
    [Fact(DisplayName = "When angle is just above 88 degrees, should return true")]
    public void WhenAngleIsJustAbove88Degrees_ShouldReturnTrue()
    {
        // For 88.1 degrees: this should be within the nearly vertical range
        // The boundary at exactly 88.0 seems to be exclusive
        var deltaX = 1.0;
        var deltaY = Math.Tan(88.1 * Math.PI / 180.0);

        var point1 = new PointD(0, 0);
        var point2 = new PointD(deltaX, deltaY);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When angle is very close to 90 degrees, should return true")]
    public void WhenAngleIsVeryCloseTo90Degrees_ShouldReturnTrue()
    {
        // Use an angle very close to 90 degrees (89.9°)
        var deltaY = Math.Tan(89.9 * Math.PI / 180.0);
        var deltaX = 1.0;
        var point1 = new PointD(0, 0);
        var point2 = new PointD(deltaX, deltaY);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When angle is 89 degrees, should return true")]
    public void WhenAngleIs89Degrees_ShouldReturnTrue()
    {
        // tan(89°) ≈ 57.289
        var deltaY = Math.Tan(89.0 * Math.PI / 180.0);
        var deltaX = 1.0;
        var point1 = new PointD(0, 0);
        var point2 = new PointD(deltaX, deltaY);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When angle is very close to 88 degrees, should return true")]
    public void WhenAngleIsVeryCloseTo88Degrees_ShouldReturnTrue()
    {
        // Use an angle very close to 88 degrees (88.1°)
        var deltaY = Math.Tan(88.1 * Math.PI / 180.0);
        var deltaX = 1.0;
        var point1 = new PointD(0, 0);
        var point2 = new PointD(deltaX, deltaY);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }
    
    [Fact(DisplayName = "When segment is perfectly horizontal, should return false")]
    public void WhenSegmentIsPerfectlyHorizontal_ShouldReturnFalse()
    {
        var y = _faker.Random.Double(-500, 500);
        var x1 = _faker.Random.Double(-500, 500);
        var x2 = x1 + _faker.Random.Double(1, 100);
        var point1 = new PointD(x1, y);
        var point2 = new PointD(x2, y);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When angle is 45 degrees, should return false")]
    public void WhenAngleIs45Degrees_ShouldReturnFalse()
    {
        // tan(45°) = 1
        var deltaX = 10.0;
        var deltaY = 10.0;
        var point1 = new PointD(0, 0);
        var point2 = new PointD(deltaX, deltaY);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When angle is 30 degrees, should return false")]
    public void WhenAngleIs30Degrees_ShouldReturnFalse()
    {
        // tan(30°) ≈ 0.577
        var deltaX = 10.0;
        var deltaY = 5.77;
        var point1 = new PointD(0, 0);
        var point2 = new PointD(deltaX, deltaY);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When angle is 60 degrees, should return false")]
    public void WhenAngleIs60Degrees_ShouldReturnFalse()
    {
        // tan(60°) ≈ 1.732
        var deltaX = 10.0;
        var deltaY = 17.32;
        var point1 = new PointD(0, 0);
        var point2 = new PointD(deltaX, deltaY);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeFalse();
    }
    
    [Fact(DisplayName = "When generating random steep angles within range, should return true")]
    public void WhenGeneratingRandomSteepAnglesWithinRange_ShouldReturnTrue()
    {
        // Generate angle between 88-92 degrees
        var angleDegrees = _faker.Random.Double(88.1, 91.9);
        var angleRadians = angleDegrees * Math.PI / 180.0;
        var deltaX = 1.0;
        var deltaY = Math.Tan(angleRadians);
        
        var point1 = new PointD(_faker.Random.Double(-100, 100), _faker.Random.Double(-100, 100));
        var point2 = new PointD(point1.X + deltaX, point1.Y + deltaY);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When generating random shallow angles outside range, should return false")]
    public void WhenGeneratingRandomShallowAnglesOutsideRange_ShouldReturnFalse()
    {
        // Generate angle outside 88-92 degrees range
        var angleDegrees = _faker.Random.Double(10, 80);
        var angleRadians = angleDegrees * Math.PI / 180.0;
        var deltaX = 10.0;
        var deltaY = Math.Tan(angleRadians) * deltaX;

        var point1 = new PointD(_faker.Random.Double(-100, 100), _faker.Random.Double(-100, 100));
        var point2 = new PointD(point1.X + deltaX, point1.Y + deltaY);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeFalse();
    }
    
    [Fact(DisplayName = "When both coordinates are zero, should return true")]
    public void WhenBothCoordinatesAreZero_ShouldReturnTrue()
    {
        var point1 = new PointD(0, 0);
        var point2 = new PointD(0, 0);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When using extreme double values, should handle correctly")]
    public void WhenUsingExtremeDoubleValues_ShouldHandleCorrectly()
    {
        var point1 = new PointD(double.MaxValue, 0);
        var point2 = new PointD(double.MaxValue, double.MaxValue);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When using very small but non-zero X difference, should return true")]
    public void WhenUsingVerySmallButNonZeroXDifference_ShouldReturnTrue()
    {
        var point1 = new PointD(0, 0);
        var point2 = new PointD(VerticalTolerance / 2, 100);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When segment direction is reversed, should give same result")]
    public void WhenSegmentDirectionIsReversed_ShouldGiveSameResult()
    {
        var point1 = new PointD(10, 20);
        var point2 = new PointD(10, 120);

        var result1 = point1.SegmentIsNearlyVertical(point2);
        var result2 = point2.SegmentIsNearlyVertical(point1);

        result1.Should().Be(result2);
        result1.Should().BeTrue();
    }
    
    [Theory(DisplayName = "When testing specific angle boundaries, should return expected results")]
    [InlineData(87.9, false)] // Just below lower bound
    [InlineData(88.1, true)]  // Just above lower bound (88.0 seems to be exclusive)
    [InlineData(89.0, true)]  // Middle of range
    [InlineData(89.5, true)]  // Middle of range
    [InlineData(89.9, true)]  // Very close to 90°
    public void WhenTestingSpecificAngleBoundaries_ShouldReturnExpectedResults(double angleDegrees, bool expected)
    {
        var angleRadians = angleDegrees * Math.PI / 180.0;
        var deltaX = 1.0;
        var deltaY = Math.Tan(angleRadians);

        var point1 = new PointD(0, 0);
        var point2 = new PointD(deltaX, deltaY);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().Be(expected);
    }

    [Fact(DisplayName = "When using coordinates that would cause overflow in angle calculation, should handle gracefully")]
    public void WhenUsingCoordinatesThatWouldCauseOverflowInAngleCalculation_ShouldHandleGracefully()
    {
        var point1 = new PointD(0, 0);
        var point2 = new PointD(double.Epsilon, double.MaxValue);

        var result = point1.SegmentIsNearlyVertical(point2);

        result.Should().BeTrue(); // Should be treated as vertical due to extremely small deltaX
    }
}
