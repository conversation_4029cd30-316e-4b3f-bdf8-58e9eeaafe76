using Bogus;
using Domain.Enums;
using Geometry.Core;
using Workflow.Extensions;

namespace Workflow.Tests.Extensions.PointDExtensions;

[Trait("PointDExtensions", "IsCloseToEdge")]
public class IsCloseToEdgeTests
{
    private readonly Faker _faker = new();
    private const double EdgeProximityTolerance = 0.5;

    [Fact(DisplayName = "When point is exactly on left edge, should return true")]
    public void WhenPointIsExactlyOnLeftEdge_ShouldReturnTrue()
    {
        var leftEdgeX = _faker.Random.Double(-1000, 1000);
        var points = new List<PointD>
        {
            new(leftEdgeX, _faker.Random.Double()),
            new(leftEdgeX + 100, _faker.Random.Double()),
            new(leftEdgeX + 200, _faker.Random.Double())
        };
        var testPoint = new PointD(leftEdgeX, _faker.Random.Double());

        var result = points.IsCloseToEdge(testPoint, XAxisEdge.Left);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When point is exactly on right edge, should return true")]
    public void WhenPointIsExactlyOnRightEdge_ShouldReturnTrue()
    {
        var rightEdgeX = _faker.Random.Double(-1000, 1000);
        var points = new List<PointD>
        {
            new(rightEdgeX - 200, _faker.Random.Double()),
            new(rightEdgeX - 100, _faker.Random.Double()),
            new(rightEdgeX, _faker.Random.Double())
        };
        var testPoint = new PointD(rightEdgeX, _faker.Random.Double());

        var result = points.IsCloseToEdge(testPoint, XAxisEdge.Right);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When point is within tolerance of left edge, should return true")]
    public void WhenPointIsWithinToleranceOfLeftEdge_ShouldReturnTrue()
    {
        var leftEdgeX = 100.0;
        var points = new List<PointD>
        {
            new(leftEdgeX, _faker.Random.Double()),
            new(leftEdgeX + 50, _faker.Random.Double()),
            new(leftEdgeX + 100, _faker.Random.Double())
        };
        var testPoint = new PointD(leftEdgeX + 0.3, _faker.Random.Double());

        var result = points.IsCloseToEdge(testPoint, XAxisEdge.Left);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When point is within tolerance of right edge, should return true")]
    public void WhenPointIsWithinToleranceOfRightEdge_ShouldReturnTrue()
    {
        var rightEdgeX = 200.0;
        var points = new List<PointD>
        {
            new(rightEdgeX - 100, _faker.Random.Double()),
            new(rightEdgeX - 50, _faker.Random.Double()),
            new(rightEdgeX, _faker.Random.Double())
        };
        var testPoint = new PointD(rightEdgeX - 0.4, _faker.Random.Double());

        var result = points.IsCloseToEdge(testPoint, XAxisEdge.Right);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When point is exactly at tolerance boundary from left edge, should return false")]
    public void WhenPointIsExactlyAtToleranceBoundaryFromLeftEdge_ShouldReturnFalse()
    {
        var leftEdgeX = 50.0;
        var points = new List<PointD>
        {
            new(leftEdgeX, _faker.Random.Double()),
            new(leftEdgeX + 25, _faker.Random.Double()),
            new(leftEdgeX + 75, _faker.Random.Double())
        };
        var testPoint = new PointD(leftEdgeX + EdgeProximityTolerance, _faker.Random.Double());

        var result = points.IsCloseToEdge(testPoint, XAxisEdge.Left);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When point is exactly at tolerance boundary from right edge, should return false")]
    public void WhenPointIsExactlyAtToleranceBoundaryFromRightEdge_ShouldReturnFalse()
    {
        var rightEdgeX = 150.0;
        var points = new List<PointD>
        {
            new(rightEdgeX - 75, _faker.Random.Double()),
            new(rightEdgeX - 25, _faker.Random.Double()),
            new(rightEdgeX, _faker.Random.Double())
        };
        var testPoint = new PointD(rightEdgeX - EdgeProximityTolerance, _faker.Random.Double());

        var result = points.IsCloseToEdge(testPoint, XAxisEdge.Right);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When point is beyond tolerance from left edge, should return false")]
    public void WhenPointIsBeyondToleranceFromLeftEdge_ShouldReturnFalse()
    {
        var leftEdgeX = 0.0;
        var points = new List<PointD>
        {
            new(leftEdgeX, _faker.Random.Double()),
            new(leftEdgeX + 10, _faker.Random.Double()),
            new(leftEdgeX + 20, _faker.Random.Double())
        };
        var testPoint = new PointD(leftEdgeX + 1.0, _faker.Random.Double());

        var result = points.IsCloseToEdge(testPoint, XAxisEdge.Left);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When point is beyond tolerance from right edge, should return false")]
    public void WhenPointIsBeyondToleranceFromRightEdge_ShouldReturnFalse()
    {
        var rightEdgeX = 100.0;
        var points = new List<PointD>
        {
            new(rightEdgeX - 20, _faker.Random.Double()),
            new(rightEdgeX - 10, _faker.Random.Double()),
            new(rightEdgeX, _faker.Random.Double())
        };
        var testPoint = new PointD(rightEdgeX - 2.0, _faker.Random.Double());

        var result = points.IsCloseToEdge(testPoint, XAxisEdge.Right);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When point is on opposite side within tolerance of left edge, should return true")]
    public void WhenPointIsOnOppositeSideWithinToleranceOfLeftEdge_ShouldReturnTrue()
    {
        var leftEdgeX = 75.0;
        var points = new List<PointD>
        {
            new(leftEdgeX, _faker.Random.Double()),
            new(leftEdgeX + 30, _faker.Random.Double()),
            new(leftEdgeX + 60, _faker.Random.Double())
        };
        var testPoint = new PointD(leftEdgeX - 0.2, _faker.Random.Double());

        var result = points.IsCloseToEdge(testPoint, XAxisEdge.Left);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When point is on opposite side within tolerance of right edge, should return true")]
    public void WhenPointIsOnOppositeSideWithinToleranceOfRightEdge_ShouldReturnTrue()
    {
        var rightEdgeX = 125.0;
        var points = new List<PointD>
        {
            new(rightEdgeX - 60, _faker.Random.Double()),
            new(rightEdgeX - 30, _faker.Random.Double()),
            new(rightEdgeX, _faker.Random.Double())
        };
        var testPoint = new PointD(rightEdgeX + 0.3, _faker.Random.Double());

        var result = points.IsCloseToEdge(testPoint, XAxisEdge.Right);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When point has same X as edge but different Y, should return true")]
    public void WhenPointHasSameXAsEdgeButDifferentY_ShouldReturnTrue()
    {
        var edgeX = _faker.Random.Double(-500, 500);
        var points = new List<PointD>
        {
            new(edgeX, 10.0),
            new(edgeX + 50, 20.0),
            new(edgeX + 100, 30.0)
        };
        var testPoint = new PointD(edgeX, 999.0);

        var result = points.IsCloseToEdge(testPoint, XAxisEdge.Left);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When all points have same X coordinate and test point is close, should return true")]
    public void WhenAllPointsHaveSameXCoordinateAndTestPointIsClose_ShouldReturnTrue()
    {
        var sameX = 42.0;
        var points = new List<PointD>
        {
            new(sameX, _faker.Random.Double()),
            new(sameX, _faker.Random.Double()),
            new(sameX, _faker.Random.Double())
        };
        var testPoint = new PointD(sameX + 0.1, _faker.Random.Double());

        var result = points.IsCloseToEdge(testPoint, XAxisEdge.Left);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When points include negative values and test point is close to left edge, should return true")]
    public void WhenPointsIncludeNegativeValuesAndTestPointIsCloseToLeftEdge_ShouldReturnTrue()
    {
        var leftEdgeX = -150.0;
        var points = new List<PointD>
        {
            new(leftEdgeX, _faker.Random.Double()),
            new(-50.0, _faker.Random.Double()),
            new(25.0, _faker.Random.Double()),
            new(100.0, _faker.Random.Double())
        };
        var testPoint = new PointD(leftEdgeX + 0.25, _faker.Random.Double());

        var result = points.IsCloseToEdge(testPoint, XAxisEdge.Left);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When using very small tolerance differences, should handle precision correctly")]
    public void WhenUsingVerySmallToleranceDifferences_ShouldHandlePrecisionCorrectly()
    {
        var edgeX = 100.0;
        var points = new List<PointD>
        {
            new(edgeX, _faker.Random.Double()),
            new(edgeX + 10, _faker.Random.Double())
        };
        var testPoint = new PointD(edgeX + 0.499999, _faker.Random.Double());

        var result = points.IsCloseToEdge(testPoint, XAxisEdge.Left);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When using very small tolerance differences just over boundary, should return false")]
    public void WhenUsingVerySmallToleranceDifferencesJustOverBoundary_ShouldReturnFalse()
    {
        var edgeX = 100.0;
        var points = new List<PointD>
        {
            new(edgeX, _faker.Random.Double()),
            new(edgeX + 10, _faker.Random.Double())
        };
        var testPoint = new PointD(edgeX + 0.500001, _faker.Random.Double());

        var result = points.IsCloseToEdge(testPoint, XAxisEdge.Left);

        result.Should().BeFalse();
    }

    [Theory(DisplayName = "When edge is invalid enum value, should throw ArgumentOutOfRangeException")]
    [InlineData((XAxisEdge)(-1))]
    [InlineData((XAxisEdge)2)]
    [InlineData((XAxisEdge)99)]
    public void WhenEdgeIsInvalidEnumValue_ShouldThrowArgumentOutOfRangeException(XAxisEdge invalidEdge)
    {
        var points = new List<PointD>
        {
            new(_faker.Random.Double(), _faker.Random.Double())
        };
        var testPoint = new PointD(_faker.Random.Double(), _faker.Random.Double());

        var exception = Assert.Throws<ArgumentOutOfRangeException>(() =>
            points.IsCloseToEdge(testPoint, invalidEdge));
        
        exception.ParamName.Should().Be("edge");
    }

    [Fact(DisplayName = "When points list is empty, should throw InvalidOperationException")]
    public void WhenPointsListIsEmpty_ShouldThrowInvalidOperationException()
    {
        var emptyPoints = new List<PointD>();
        var testPoint = new PointD(_faker.Random.Double(), _faker.Random.Double());

        Assert.Throws<InvalidOperationException>(() =>
            emptyPoints.IsCloseToEdge(testPoint, XAxisEdge.Left));
    }

    [Fact(DisplayName = "When points contain extreme double values, should handle correctly")]
    public void WhenPointsContainExtremeDoubleValues_ShouldHandleCorrectly()
    {
        var points = new List<PointD>
        {
            new(double.MinValue, _faker.Random.Double()),
            new(0.0, _faker.Random.Double()),
            new(double.MaxValue, _faker.Random.Double())
        };
        var testPoint = new PointD(double.MinValue + 0.1, _faker.Random.Double());

        var result = points.IsCloseToEdge(testPoint, XAxisEdge.Left);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When point is far from any edge, should return false for left edge")]
    public void WhenPointIsFarFromAnyEdge_ShouldReturnFalseForLeftEdge()
    {
        var points = new List<PointD>
        {
            new(0.0, _faker.Random.Double()),
            new(50.0, _faker.Random.Double()),
            new(100.0, _faker.Random.Double())
        };
        var testPoint = new PointD(25.0, _faker.Random.Double());

        var result = points.IsCloseToEdge(testPoint, XAxisEdge.Left);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When point is far from any edge, should return false for right edge")]
    public void WhenPointIsFarFromAnyEdge_ShouldReturnFalseForRightEdge()
    {
        var points = new List<PointD>
        {
            new(0.0, _faker.Random.Double()),
            new(50.0, _faker.Random.Double()),
            new(100.0, _faker.Random.Double())
        };
        var testPoint = new PointD(75.0, _faker.Random.Double());

        var result = points.IsCloseToEdge(testPoint, XAxisEdge.Right);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When single point and test point is within tolerance, should return true")]
    public void WhenSinglePointAndTestPointIsWithinTolerance_ShouldReturnTrue()
    {
        var singlePointX = _faker.Random.Double(-100, 100);
        var points = new List<PointD>
        {
            new(singlePointX, _faker.Random.Double())
        };
        var testPoint = new PointD(singlePointX + 0.25, _faker.Random.Double());

        var result = points.IsCloseToEdge(testPoint, XAxisEdge.Left);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When single point and test point is beyond tolerance, should return false")]
    public void WhenSinglePointAndTestPointIsBeyondTolerance_ShouldReturnFalse()
    {
        var singlePointX = _faker.Random.Double(-100, 100);
        var points = new List<PointD>
        {
            new(singlePointX, _faker.Random.Double())
        };
        var testPoint = new PointD(singlePointX + 1.0, _faker.Random.Double());

        var result = points.IsCloseToEdge(testPoint, XAxisEdge.Right);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When points have zero X coordinates and test point is close, should return true")]
    public void WhenPointsHaveZeroXCoordinatesAndTestPointIsClose_ShouldReturnTrue()
    {
        var points = new List<PointD>
        {
            new(0.0, _faker.Random.Double()),
            new(10.0, _faker.Random.Double()),
            new(20.0, _faker.Random.Double())
        };
        var testPoint = new PointD(0.3, _faker.Random.Double());

        var result = points.IsCloseToEdge(testPoint, XAxisEdge.Left);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When test point X equals tolerance value from edge, should return false")]
    public void WhenTestPointXEqualsToleranceValueFromEdge_ShouldReturnFalse()
    {
        var edgeX = 50.0;
        var points = new List<PointD>
        {
            new(edgeX, _faker.Random.Double()),
            new(edgeX + 25, _faker.Random.Double())
        };
        var testPoint = new PointD(edgeX + EdgeProximityTolerance, _faker.Random.Double());

        var result = points.IsCloseToEdge(testPoint, XAxisEdge.Left);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When multiple points at same edge and test point is close, should return true")]
    public void WhenMultiplePointsAtSameEdgeAndTestPointIsClose_ShouldReturnTrue()
    {
        var edgeX = _faker.Random.Double(-200, 200);
        var points = new List<PointD>
        {
            new(edgeX, _faker.Random.Double()),
            new(edgeX, _faker.Random.Double()),
            new(edgeX + 100, _faker.Random.Double()),
            new(edgeX + 150, _faker.Random.Double())
        };
        var testPoint = new PointD(edgeX - 0.4, _faker.Random.Double());

        var result = points.IsCloseToEdge(testPoint, XAxisEdge.Left);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When using negative edge coordinates and test point is close to right edge, should return true")]
    public void WhenUsingNegativeEdgeCoordinatesAndTestPointIsCloseToRightEdge_ShouldReturnTrue()
    {
        var rightEdgeX = -10.0;
        var points = new List<PointD>
        {
            new(-100.0, _faker.Random.Double()),
            new(-50.0, _faker.Random.Double()),
            new(rightEdgeX, _faker.Random.Double())
        };
        var testPoint = new PointD(rightEdgeX - 0.35, _faker.Random.Double());

        var result = points.IsCloseToEdge(testPoint, XAxisEdge.Right);

        result.Should().BeTrue();
    }
}
