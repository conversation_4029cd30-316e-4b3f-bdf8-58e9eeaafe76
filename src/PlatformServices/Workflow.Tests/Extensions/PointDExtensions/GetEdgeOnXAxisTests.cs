using Bogus;
using Domain.Enums;
using Geometry.Core;
using Workflow.Extensions;

namespace Workflow.Tests.Extensions.PointDExtensions;

[Trait("PointDExtensions", "GetEdgeOnXAxis")]
public class GetEdgeOnXAxisTests
{
    private readonly Faker _faker = new();

    [Fact(
        DisplayName =
            "When edge is Left and list has single point, should return that point's X coordinate")]
    public void
        WhenEdgeIsLeftAndListHasSinglePoint_ShouldReturnThatPointsXCoordinate()
    {
        var expectedX = _faker.Random.Double(-1000, 1000);
        var points = new List<PointD>
        {
            new(expectedX, _faker.Random.Double())
        };

        var result = points.GetEdgeOnXAxis(XAxisEdge.Left);

        result.Should().Be(expectedX);
    }

    [Fact(
        DisplayName =
            "When edge is Right and list has single point, should return that point's X coordinate")]
    public void
        WhenEdgeIsRightAndListHasSinglePoint_ShouldReturnThatPointsXCoordinate()
    {
        var expectedX = _faker.Random.Double(-1000, 1000);
        var points = new List<PointD>
        {
            new(expectedX, _faker.Random.Double())
        };

        var result = points.GetEdgeOnXAxis(XAxisEdge.Right);

        result.Should().Be(expectedX);
    }

    [Fact(
        DisplayName =
            "When edge is Left and list has multiple points, should return minimum X coordinate")]
    public void
        WhenEdgeIsLeftAndListHasMultiplePoints_ShouldReturnMinimumXCoordinate()
    {
        const double minX = -100.5;
        var points = new List<PointD>
        {
            new(50.0, _faker.Random.Double()),
            new(minX, _faker.Random.Double()),
            new(25.7, _faker.Random.Double()),
            new(0.0, _faker.Random.Double()),
            new(75.3, _faker.Random.Double())
        };

        var result = points.GetEdgeOnXAxis(XAxisEdge.Left);

        result.Should().Be(minX);
    }

    [Fact(
        DisplayName =
            "When edge is Right and list has multiple points, should return maximum X coordinate")]
    public void
        WhenEdgeIsRightAndListHasMultiplePoints_ShouldReturnMaximumXCoordinate()
    {
        const double maxX = 150.8;
        var points = new List<PointD>
        {
            new(50.0, _faker.Random.Double()),
            new(-25.5, _faker.Random.Double()),
            new(maxX, _faker.Random.Double()),
            new(0.0, _faker.Random.Double()),
            new(75.3, _faker.Random.Double())
        };

        var result = points.GetEdgeOnXAxis(XAxisEdge.Right);

        result.Should().Be(maxX);
    }

    [Fact(
        DisplayName =
            "When edge is Left and all points have same X coordinate, should return that X coordinate")]
    public void
        WhenEdgeIsLeftAndAllPointsHaveSameXCoordinate_ShouldReturnThatXCoordinate()
    {
        var sameX = _faker.Random.Double(-500, 500);
        var points = new List<PointD>
        {
            new(sameX, _faker.Random.Double()),
            new(sameX, _faker.Random.Double()),
            new(sameX, _faker.Random.Double())
        };

        var result = points.GetEdgeOnXAxis(XAxisEdge.Left);

        result.Should().Be(sameX);
    }

    [Fact(
        DisplayName =
            "When edge is Right and all points have same X coordinate, should return that X coordinate")]
    public void
        WhenEdgeIsRightAndAllPointsHaveSameXCoordinate_ShouldReturnThatXCoordinate()
    {
        var sameX = _faker.Random.Double(-500, 500);
        var points = new List<PointD>
        {
            new(sameX, _faker.Random.Double()),
            new(sameX, _faker.Random.Double()),
            new(sameX, _faker.Random.Double())
        };

        var result = points.GetEdgeOnXAxis(XAxisEdge.Right);

        result.Should().Be(sameX);
    }

    [Fact(
        DisplayName =
            "When edge is Left and points include negative values, should return most negative X coordinate")]
    public void
        WhenEdgeIsLeftAndPointsIncludeNegativeValues_ShouldReturnMostNegativeXCoordinate()
    {
        const double mostNegativeX = -999.99;
        var points = new List<PointD>
        {
            new(100.0, _faker.Random.Double()),
            new(-50.5, _faker.Random.Double()),
            new(mostNegativeX, _faker.Random.Double()),
            new(-25.0, _faker.Random.Double())
        };

        var result = points.GetEdgeOnXAxis(XAxisEdge.Left);

        result.Should().Be(mostNegativeX);
    }

    [Fact(
        DisplayName =
            "When edge is Right and points include large values, should return largest X coordinate")]
    public void
        WhenEdgeIsRightAndPointsIncludeLargeValues_ShouldReturnLargestXCoordinate()
    {
        const double largestX = 9999.99;
        var points = new List<PointD>
        {
            new(100.0, _faker.Random.Double()),
            new(-50.5, _faker.Random.Double()),
            new(largestX, _faker.Random.Double()),
            new(500.0, _faker.Random.Double())
        };

        var result = points.GetEdgeOnXAxis(XAxisEdge.Right);

        result.Should().Be(largestX);
    }

    [Fact(
        DisplayName =
            "When edge is Left and points include zero, should handle zero correctly")]
    public void WhenEdgeIsLeftAndPointsIncludeZero_ShouldHandleZeroCorrectly()
    {
        var points = new List<PointD>
        {
            new(10.0, _faker.Random.Double()),
            new(0.0, _faker.Random.Double()),
            new(5.0, _faker.Random.Double())
        };

        var result = points.GetEdgeOnXAxis(XAxisEdge.Left);

        result.Should().Be(0.0);
    }

    [Fact(
        DisplayName =
            "When edge is Right and points include zero, should handle zero correctly")]
    public void WhenEdgeIsRightAndPointsIncludeZero_ShouldHandleZeroCorrectly()
    {
        var points = new List<PointD>
        {
            new(-10.0, _faker.Random.Double()),
            new(0.0, _faker.Random.Double()),
            new(-5.0, _faker.Random.Double())
        };

        var result = points.GetEdgeOnXAxis(XAxisEdge.Right);

        result.Should().Be(0.0);
    }

    [Theory(
        DisplayName =
            "When edge is invalid enum value, should throw ArgumentOutOfRangeException")]
    [InlineData((XAxisEdge)(-1))]
    [InlineData((XAxisEdge)2)]
    [InlineData((XAxisEdge)99)]
    public void
        WhenEdgeIsInvalidEnumValue_ShouldThrowArgumentOutOfRangeException(
            XAxisEdge invalidEdge)
    {
        var points = new List<PointD>
        {
            new(_faker.Random.Double(), _faker.Random.Double())
        };

        var exception =
            Assert.Throws<ArgumentOutOfRangeException>(() =>
                points.GetEdgeOnXAxis(invalidEdge));
        exception.ParamName.Should().Be("edge");
    }

    [Fact(
        DisplayName =
            "When list is empty, should throw InvalidOperationException for Left edge")]
    public void
        WhenListIsEmpty_ShouldThrowInvalidOperationExceptionForLeftEdge()
    {
        var emptyPoints = new List<PointD>();

        Assert.Throws<InvalidOperationException>(() =>
            emptyPoints.GetEdgeOnXAxis(XAxisEdge.Left));
    }

    [Fact(
        DisplayName =
            "When list is empty, should throw InvalidOperationException for Right edge")]
    public void
        WhenListIsEmpty_ShouldThrowInvalidOperationExceptionForRightEdge()
    {
        var emptyPoints = new List<PointD>();

        Assert.Throws<InvalidOperationException>(() =>
            emptyPoints.GetEdgeOnXAxis(XAxisEdge.Right));
    }

    [Fact(
        DisplayName =
            "When points have very small differences, should return precise minimum for Left edge")]
    public void
        WhenPointsHaveVerySmallDifferences_ShouldReturnPreciseMinimumForLeftEdge()
    {
        const double baseX = 100.0;
        const double minX = baseX - 0.000001;
        var points = new List<PointD>
        {
            new(baseX, _faker.Random.Double()),
            new(minX, _faker.Random.Double()),
            new(baseX + 0.000001, _faker.Random.Double())
        };

        var result = points.GetEdgeOnXAxis(XAxisEdge.Left);

        result.Should().Be(minX);
    }

    [Fact(
        DisplayName =
            "When points have very small differences, should return precise maximum for Right edge")]
    public void
        WhenPointsHaveVerySmallDifferences_ShouldReturnPreciseMaximumForRightEdge()
    {
        const double baseX = 100.0;
        const double maxX = baseX + 0.000001;
        var points = new List<PointD>
        {
            new(baseX, _faker.Random.Double()),
            new(baseX - 0.000001, _faker.Random.Double()),
            new(maxX, _faker.Random.Double())
        };

        var result = points.GetEdgeOnXAxis(XAxisEdge.Right);

        result.Should().Be(maxX);
    }

    [Fact(
        DisplayName =
            "When points contain extreme double values, should handle correctly for Left edge")]
    public void
        WhenPointsContainExtremeDoubleValues_ShouldHandleCorrectlyForLeftEdge()
    {
        var points = new List<PointD>
        {
            new(double.MaxValue, _faker.Random.Double()),
            new(double.MinValue, _faker.Random.Double()),
            new(0.0, _faker.Random.Double())
        };

        var result = points.GetEdgeOnXAxis(XAxisEdge.Left);

        result.Should().Be(double.MinValue);
    }

    [Fact(
        DisplayName =
            "When points contain extreme double values, should handle correctly for Right edge")]
    public void
        WhenPointsContainExtremeDoubleValues_ShouldHandleCorrectlyForRightEdge()
    {
        var points = new List<PointD>
        {
            new(double.MinValue, _faker.Random.Double()),
            new(double.MaxValue, _faker.Random.Double()),
            new(0.0, _faker.Random.Double())
        };

        var result = points.GetEdgeOnXAxis(XAxisEdge.Right);

        result.Should().Be(double.MaxValue);
    }
}
