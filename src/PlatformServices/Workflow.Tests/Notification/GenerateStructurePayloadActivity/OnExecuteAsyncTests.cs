using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Application.Apis.Clients.Response._Shared;
using Application.Apis.Clients.Response.Structure.GetById;
using Application.Apis.Clients.Response.User;
using Application.Apis.Users.Response;
using Domain.Enums;
using Domain.Messages.Commands.Notification;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Moq;
using Workflow.Tests.Notification.Builders;
using static Workflow.Constants.NotificationWorkFlow;

namespace Workflow.Tests.Notification.GenerateStructurePayloadActivity;

[Trait("GenerateStructurePayloadActivity", "OnExecuteAsync")]
public class GenerateStructurePayloadActivityTests
{
    private const string VariableName = "Message";
    private readonly Mock<IClientsApiService> _clientsApiServiceMock = new();

    private readonly
        Workflow.Notification.AddNotification.Activities.
        GenerateStructurePayloadActivity _activity;

    private readonly ActivityExecutionContext _context;

    private readonly GetStructureByIdResponse _structureResponse = new()
    {
        Id = Guid.NewGuid(),
        Name = "Test Structure",
        ClientUnit = new ClientUnitResponse
        {
            Id = Guid.NewGuid(),
            Name = "Test Client Unit"
        }
    };

    private readonly GetUserByIdResponse _userResponse = new()
    {
        Id = Guid.NewGuid(),
        FirstName = "Test",
        Surname = "User"
    };

    public GenerateStructurePayloadActivityTests()
    {
        _context = new ActivityExecutionContextBuilder().Context;

        _activity =
            new Workflow.Notification.AddNotification.Activities.
                GenerateStructurePayloadActivity(
                    _clientsApiServiceMock.Object);
    }

    private void SetNotificationTransientVariable(CreateNotification message)
    {
        _context.SetTransientVariable(VariableName, message);
    }

    private void MockClientsApiUserById()
    {
    }

    [Fact(DisplayName = "When structure is not found - Returns Fault")]
    public async Task WhenStructureIsNotFound_ReturnsFault()
    {
        SetNotificationTransientVariable(
            CreateNotificationBuilder.GetValid(NotificationTheme
                .StructureUpdated));

        var result = await _activity.ExecuteAsync(_context);

        result
            .Should()
            .BeOfType<FaultResult>();
    }

    [Fact(DisplayName =
        "When structure is not found - Returns exception message")]
    public async Task WhenStructureIsNotFound_ReturnsExceptionMessage()
    {
        SetNotificationTransientVariable(
            CreateNotificationBuilder.GetValid(NotificationTheme
                .StructureUpdated));

        var result = await _activity.ExecuteAsync(_context);

        result
            .As<FaultResult>()
            .Exception
            .Message
            .Should()
            .Be("Estrutura não encontrada");
    }

    [Fact(DisplayName = "When users are not found - Returns Done")]
    public async Task WhenUsersAreNotFound_ReturnsDone()
    {
        SetNotificationTransientVariable(
            CreateNotificationBuilder.GetValid(NotificationTheme
                .StructureUpdated));

        _clientsApiServiceMock
            .Setup(service =>
                service.GetUsersAsync(It.IsAny<ListUserRequest>()))
            .ReturnsAsync(new List<ListUserResponse>());

        _clientsApiServiceMock
            .Setup(client => client.GetStructureById(It.IsAny<Guid>()))
            .ReturnsAsync(_structureResponse);

        var result = await _activity.ExecuteAsync(_context);

        result
            .As<OutcomeResult>()
            .Outcomes
            .First()
            .Should()
            .Be("Done");
    }

    [Fact(DisplayName =
        "When structure notification is received - Returns Done")]
    public async Task
        WhenStructureNotificationIsReceived_ReturnsDone()
    {
        SetNotificationTransientVariable(
            CreateNotificationBuilder.GetValid(NotificationTheme
                .StructureUpdated));

        _clientsApiServiceMock
            .Setup(client => client.GetStructureById(It.IsAny<Guid>()))
            .ReturnsAsync(_structureResponse);

        _clientsApiServiceMock
            .Setup(client => client.GetUsersAsync(It.IsAny<ListUserRequest>()))
            .ReturnsAsync(ListUserResponseBuilder.GetValid());

        _clientsApiServiceMock
            .Setup(client => client.GetUserById(It.IsAny<Guid>()))
            .ReturnsAsync(_userResponse);

        var result = await _activity.ExecuteAsync(_context);

        result
            .As<OutcomeResult>()
            .Outcomes
            .First()
            .Should()
            .Be("Done");
    }

    [Fact(DisplayName =
        "When structure notification is received - Should generate notifications to send")]
    public async Task
        WhenStructureNotificationIsReceived_ShouldGenerateNotificationsToSend()
    {
        SetNotificationTransientVariable(
            CreateNotificationBuilder.GetValid(NotificationTheme
                .StructureUpdated));

        _clientsApiServiceMock
            .Setup(client => client.GetStructureById(It.IsAny<Guid>()))
            .ReturnsAsync(_structureResponse);

        _clientsApiServiceMock
            .Setup(client => client.GetUsersAsync(It.IsAny<ListUserRequest>()))
            .ReturnsAsync(ListUserResponseBuilder.GetValid());

        _clientsApiServiceMock
            .Setup(client => client.GetUserById(It.IsAny<Guid>()))
            .ReturnsAsync(_userResponse);

        _ = await _activity.ExecuteAsync(_context);

        var notifications = _context
            .GetVariable<List<AddNotificationRequest>>(Variables
                .Notification);

        notifications
            .Should()
            .NotBeEmpty();
    }

    [Fact(DisplayName =
        "When structure notification is received - Should generate the expected notification")]
    public async Task
        WhenStructureNotificationIsReceived_ShouldGenerateTheExpectedNotification()
    {
        const NotificationTheme theme = NotificationTheme.StructureUpdated;

        var message = CreateNotificationBuilder.GetValid(theme);
        SetNotificationTransientVariable(message);

        _clientsApiServiceMock
            .Setup(client => client.GetStructureById(It.IsAny<Guid>()))
            .ReturnsAsync(_structureResponse);

        _clientsApiServiceMock
            .Setup(client => client.GetUsersAsync(It.IsAny<ListUserRequest>()))
            .ReturnsAsync(ListUserResponseBuilder.GetValid());

        _clientsApiServiceMock
            .Setup(client => client.GetUserById(It.IsAny<Guid>()))
            .ReturnsAsync(_userResponse);

        _ = await _activity.ExecuteAsync(_context);

        var themeResult = _context
            .GetVariable<List<AddNotificationRequest>>(Variables
                .Notification)?[0].NotificationTheme;

        themeResult
            .Should()
            .Be(theme);
    }
}