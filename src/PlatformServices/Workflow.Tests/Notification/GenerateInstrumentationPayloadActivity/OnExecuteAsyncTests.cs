using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Application.Apis.Clients.Response._Shared;
using Application.Apis.Clients.Response.Instrument;
using Application.Apis.Clients.Response.User;
using Application.Apis.Users.Response;
using Domain.Enums;
using Domain.Messages.Commands.Notification;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Moq;
using Workflow.Tests.Notification.Builders;
using static Workflow.Constants.NotificationWorkFlow;

namespace Workflow.Tests.Notification.GenerateInstrumentationPayloadActivity;

[Trait("GenerateInstrumentationPayloadActivity", "OnExecuteAsync")]
public class OnExecuteAsyncTests
{
    private const string VariableName = "Message";
    private readonly Mock<IClientsApiService> _clientsApiServiceMock = new();

    private readonly
        Workflow.Notification.AddNotification.Activities.
        GenerateInstrumentationPayloadActivity _activity;

    private readonly ActivityExecutionContext _context;

    private readonly GetInstrumentByIdResponse _instrumentResponse = new()
    {
        Id = Guid.NewGuid(),
        Identifier = "Test Instrument",
        Structure = new StructureResponse
        {
            Id = Guid.NewGuid(),
            Name = "Test Structure"
        }
    };

    private readonly GetUserByIdResponse _userResponse = new()
    {
        Id = Guid.NewGuid(),
        FirstName = "Test",
        Surname = "User"
    };

    public OnExecuteAsyncTests()
    {
        _context = new ActivityExecutionContextBuilder().Context;

        _activity =
            new Workflow.Notification.AddNotification.Activities.
                GenerateInstrumentationPayloadActivity(
                    _clientsApiServiceMock.Object);
    }

    private void SetNotificationTransientVariable(CreateNotification message)
    {
        _context.SetTransientVariable(VariableName, message);
    }

    [Fact(DisplayName = "When instrument is not found - Returns Fault")]
    public async Task WhenInstrumentIsNotFound_ReturnsFault()
    {
        SetNotificationTransientVariable(
            CreateNotificationBuilder.GetValid(NotificationTheme
                .InstrumentCreated));

        var result = await _activity.ExecuteAsync(_context);

        result
            .Should()
            .BeOfType<FaultResult>();
    }

    [Fact(DisplayName =
        "When instrument is not found - Returns exception message")]
    public async Task WhenInstrumentIsNotFound_ReturnsExceptionMessage()
    {
        SetNotificationTransientVariable(
            CreateNotificationBuilder.GetValid(NotificationTheme
                .InstrumentCreated));

        var result = await _activity.ExecuteAsync(_context);

        result
            .As<FaultResult>()
            .Exception
            .Message
            .Should()
            .Be("Instrumento não encontrado");
    }

    [Fact(DisplayName = "When users are not found - Returns Done")]
    public async Task WhenUsersAreNotFound_ReturnsDone()
    {
        SetNotificationTransientVariable(
            CreateNotificationBuilder.GetValid(NotificationTheme
                .InstrumentCreated));

        _clientsApiServiceMock
            .Setup(service =>
                service.GetUsersAsync(It.IsAny<ListUserRequest>()))
            .ReturnsAsync(new List<ListUserResponse>());

        _clientsApiServiceMock
            .Setup(client => client.GetInstrumentById(It.IsAny<Guid>()))
            .ReturnsAsync(_instrumentResponse);

        var result = await _activity.ExecuteAsync(_context);

        result
            .As<OutcomeResult>()
            .Outcomes
            .First()
            .Should()
            .Be("Done");
    }

    [Fact(DisplayName =
        "When instrument notification is received - Returns Done")]
    public async Task WhenInstrumentNotificationIsReceived_ReturnsDone()
    {
        SetNotificationTransientVariable(
            CreateNotificationBuilder.GetValid(NotificationTheme
                .InstrumentCreated));

        _clientsApiServiceMock
            .Setup(client => client.GetInstrumentById(It.IsAny<Guid>()))
            .ReturnsAsync(_instrumentResponse);

        _clientsApiServiceMock
            .Setup(client => client.GetUsersAsync(It.IsAny<ListUserRequest>()))
            .ReturnsAsync(ListUserResponseBuilder.GetValid());

        _clientsApiServiceMock
            .Setup(client => client.GetUserById(It.IsAny<Guid>()))
            .ReturnsAsync(_userResponse);

        var result = await _activity.ExecuteAsync(_context);

        result
            .As<OutcomeResult>()
            .Outcomes
            .First()
            .Should()
            .Be("Done");
    }

    [Fact(DisplayName =
        "When instrument notification is received - Should generate notifications to send")]
    public async Task
        WhenInstrumentNotificationIsReceived_ShouldGenerateNotificationsToSend()
    {
        SetNotificationTransientVariable(
            CreateNotificationBuilder.GetValid(NotificationTheme
                .InstrumentCreated));

        _clientsApiServiceMock
            .Setup(client => client.GetInstrumentById(It.IsAny<Guid>()))
            .ReturnsAsync(_instrumentResponse);

        _clientsApiServiceMock
            .Setup(client => client.GetUsersAsync(It.IsAny<ListUserRequest>()))
            .ReturnsAsync(ListUserResponseBuilder.GetValid());

        _clientsApiServiceMock
            .Setup(client => client.GetUserById(It.IsAny<Guid>()))
            .ReturnsAsync(_userResponse);

        _ = await _activity.ExecuteAsync(_context);

        var notifications = _context
            .GetVariable<List<AddNotificationRequest>>(Variables.Notification);

        notifications
            .Should()
            .NotBeEmpty();
    }

    [Fact(DisplayName =
        "When instrument notification is received - Should generate the expected notification")]
    public async Task
        WhenInstrumentNotificationIsReceived_ShouldGenerateTheExpectedNotification()
    {
        const NotificationTheme theme = NotificationTheme.InstrumentCreated;

        var message = CreateNotificationBuilder.GetValid(theme);
        SetNotificationTransientVariable(message);

        _clientsApiServiceMock
            .Setup(client => client.GetInstrumentById(It.IsAny<Guid>()))
            .ReturnsAsync(_instrumentResponse);

        _clientsApiServiceMock
            .Setup(client => client.GetUsersAsync(It.IsAny<ListUserRequest>()))
            .ReturnsAsync(ListUserResponseBuilder.GetValid());

        _clientsApiServiceMock
            .Setup(client => client.GetUserById(It.IsAny<Guid>()))
            .ReturnsAsync(_userResponse);

        _ = await _activity.ExecuteAsync(_context);

        var themeResult = _context
            .GetVariable<List<AddNotificationRequest>>(Variables.Notification)?
            [0].NotificationTheme;

        themeResult
            .Should()
            .Be(theme);
    }
}