using Application.Apis.Clients.Response._Shared;
using Application.Apis.Clients.Response.StabilityAnalysis.GetByIdWithSafetyFactorsMetrics;
using Bogus;
using Domain.Enums;

namespace Workflow.Tests.Notification.Builders
{
    internal static class GetStabilityAnalysisByIdWithSafetyFactorsMetricsResponseBuilder
    {
        public static GetStabilityAnalysisByIdWithSafetyFactorsMetricsResponse GetValid(
            double? safetyFactorValue = null,
            SliFileType? sliFileType = null,
            int countSafetyFactors = 1) =>
                new Faker<GetStabilityAnalysisByIdWithSafetyFactorsMetricsResponse>("pt_BR")
                .CustomInstantiator(faker => new GetStabilityAnalysisByIdWithSafetyFactorsMetricsResponse
                {
                    Id = Guid.NewGuid(),
                    StructureId = Guid.NewGuid(),
                    StructureName = faker.Lorem.Word().ToUpper(),
                    SectionName = faker.Lorem.Word().ToUpper(),
                    CalculatedBy = new Faker<UserResponse>()
                        .CustomInstantiator(fakerUser => new UserResponse
                        {
                            Id = Guid.NewGuid(),
                            FirstName = faker.Person.FirstName,
                            Surname = faker.Person.LastName,
                            Username = faker.Person.UserName
                        })
                        .Generate(),
                    ReadingCreatedDate = DateTime.UtcNow,
                    SafetyFactorMetricResults = SafetyFactorMetricResponseBuilder.GetAll(),
                    SafetyFactorValueResults = SafetyFactorValueResponseBuilder.GetValid(
                        safetyFactorValue,
                        sliFileType,
                        countSafetyFactors)
                })
                .Generate();
    }
}
