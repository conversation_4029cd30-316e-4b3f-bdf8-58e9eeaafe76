using Application.Apis.Clients.Response.User;

namespace Workflow.Tests.Notification.Builders
{
    internal static class ListUserResponseBuilder
    {
        public static List<ListUserResponse> GetValid(
            int count = 1) =>
                Enumerable.Range(1, count)
                .Select(x => new ListUserResponse
                {
                    Id = Guid.NewGuid()
                })
                .ToList();
    }
}
