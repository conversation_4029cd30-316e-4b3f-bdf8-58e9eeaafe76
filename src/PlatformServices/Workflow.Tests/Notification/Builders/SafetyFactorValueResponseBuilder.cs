using Application.Apis.Clients.Response.StabilityAnalysis.GetByIdWithSafetyFactorsMetrics;
using Bogus;
using Domain.Enums;

namespace Workflow.Tests.Notification.Builders
{
    internal static class SafetyFactorValueResponseBuilder
    {
        public static List<SafetyFactorValueResponse> GetValid(
            double? value = null,
            SliFileType? sliFileType = null,
            int count = 1) =>
                new Faker<SafetyFactorValueResponse>()
                    .CustomInstantiator(faker => new SafetyFactorValueResponse
                    {
                        Id = Guid.NewGuid(),
                        Value = value ?? faker.Random.Double(min: 0, max: 3),
                        SliFileType = sliFileType ?? faker.Random.Enum<SliFileType>()
                    })
                    .Generate(count);
    }
}