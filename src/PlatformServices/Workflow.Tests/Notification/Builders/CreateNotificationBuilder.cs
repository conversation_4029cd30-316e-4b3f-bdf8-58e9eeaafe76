using Domain.Enums;
using Domain.Messages.Commands.Notification;
using static Domain.Enums.NotificationTheme;

namespace Workflow.Tests.Notification.Builders
{
    internal static class CreateNotificationBuilder
    {
        public static CreateNotification GetValid(NotificationTheme theme) =>
            theme switch
            {
                StructureUpdated or StructureDeleted => new
                    StructureNotification()
                    {
                        Id = Guid.NewGuid(),
                        Theme = theme
                    },
                
                InstrumentCreated or InstrumentUpdated or InstrumentDeleted => new
                    InstrumentNotification()
                    {
                        Id = Guid.NewGuid(),
                        Theme = theme
                    },

                _ => new CreateNotification
                {
                    Id = Guid.NewGuid(),
                    Theme = theme
                }
            };
    }
}