using Application.Apis.Clients.Response.StabilityAnalysis.GetByIdWithSafetyFactorsMetrics;
using Domain.Enums;

namespace Workflow.Tests.Notification.Builders
{
    internal static class SafetyFactorMetricResponseBuilder
    {
        public static IEnumerable<SafetyFactorMetricResponse> GetAll() =>
            new List<SafetyFactorMetricResponse>
            {
                new SafetyFactorMetricResponse
                {
                    Id = Guid.NewGuid(),
                    SoilConditionType = SoilConditionType.Drained,
                    AlertLevel = SafetyFactorAlertLevel.Attention,
                    ReferenceValue = 1.5
                },
                new SafetyFactorMetricResponse
                {
                    Id = Guid.NewGuid(),
                    SoilConditionType = SoilConditionType.Drained,
                    AlertLevel = SafetyFactorAlertLevel.Alert,
                    ReferenceValue = 1.3
                },
                new SafetyFactorMetricResponse
                {
                    Id = Guid.NewGuid(),
                    SoilConditionType = SoilConditionType.Drained,
                    AlertLevel = SafetyFactorAlertLevel.Emergency,
                    ReferenceValue = 1.1
                },
                new SafetyFactorMetricResponse
                {
                    Id = Guid.NewGuid(),
                    SoilConditionType = SoilConditionType.Undrained,
                    AlertLevel = SafetyFactorAlertLevel.Alert,
                    ReferenceValue = 1.3
                },
                new SafetyFactorMetricResponse
                {
                    Id = Guid.NewGuid(),
                    SoilConditionType = SoilConditionType.Undrained,
                    AlertLevel = SafetyFactorAlertLevel.Emergency,
                    ReferenceValue = 1.1
                },
                new SafetyFactorMetricResponse
                {
                    Id = Guid.NewGuid(),
                    SoilConditionType = SoilConditionType.PseudoStatic,
                    AlertLevel = SafetyFactorAlertLevel.Emergency,
                    ReferenceValue = 1.1
                },
            };
    }
}
