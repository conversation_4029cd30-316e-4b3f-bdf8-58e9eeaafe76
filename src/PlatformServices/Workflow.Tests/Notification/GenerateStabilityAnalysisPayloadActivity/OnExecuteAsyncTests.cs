using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Application.Apis.Clients.Response.StabilityAnalysis.GetByIdWithSafetyFactorsMetrics;
using Domain.Enums;
using Domain.Extensions;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Moq;
using Workflow.Notification.AddNotification.Activities;
using Workflow.Tests.Notification.Builders;
using static Workflow.Constants.NotificationWorkFlow;

namespace Workflow.Tests.Notification.GenerateStabilityAnalysisPayloadActivity
{
    [Trait("GenerateStabilityAnalysisPayloadActivity", "OnExecuteAsync")]
    public class OnExecuteAsyncTests
    {
        const string VariableName = "Message";
        private readonly Mock<IClientsApiService> _clientsApiServiceMock = new();
        private readonly Workflow.Notification.AddNotification.Activities.GenerateStabilityAnalysisPayloadActivity _activity;
        private readonly ActivityExecutionContext _context;
        private GetStabilityAnalysisByIdWithSafetyFactorsMetricsResponse _stabilityAnalysisWithSafetyFactorsMetris = new();

        public OnExecuteAsyncTests()
        {
            _context = new ActivityExecutionContextBuilder().Context;

            _activity = new Workflow.Notification.AddNotification.Activities.GenerateStabilityAnalysisPayloadActivity(
                _clientsApiServiceMock.Object);
        }

        private void SetNotificationTransientVariable(
            NotificationTheme theme)
        {
            _context.SetTransientVariable(
                VariableName,
                CreateNotificationBuilder.GetValid(theme));
        }

        private void GetStabilityAnalysisWithSafetyFactorsMetris(
            double? safetyFactorValue = null,
            SliFileType? sliFileType = null,
            int countSafetyFactors = 1)
        {
            _stabilityAnalysisWithSafetyFactorsMetris = GetStabilityAnalysisByIdWithSafetyFactorsMetricsResponseBuilder
                .GetValid(
                    safetyFactorValue,
                    sliFileType,
                    countSafetyFactors);
        }

        private void MockClientsApiStabilityAnalysisWithSafetyFactorsMetrics()
        {
            _clientsApiServiceMock
                .Setup(client => client.GetStabilityAnalysisWithSafetyFactorsMetricsAsync(It.IsAny<Guid>()))
                .ReturnsAsync(_stabilityAnalysisWithSafetyFactorsMetris);
        }

        private void MockClientsApiUsers()
        {
            _clientsApiServiceMock
                .Setup(client => client.GetUsersAsync(It.IsAny<ListUserRequest>()))
                .ReturnsAsync(ListUserResponseBuilder.GetValid());
        }

        [Fact(DisplayName = "When stability analysis is not found - Returns Fault")]
        public async Task WhenStabilityAnalysisIsNotFound_ReturnsFault()
        {
            SetNotificationTransientVariable(NotificationTheme.StabilityAnalysisCreated);

            var result = await _activity.ExecuteAsync(_context);

            result
                .Should()
                .BeOfType<FaultResult>();
        }

        [Fact(DisplayName = "When stability analysis is not found - Returns exception message")]
        public async Task WhenStabilityAnalysisIsNotFound_ReturnsExceptionMessage()
        {
            SetNotificationTransientVariable(NotificationTheme.StabilityAnalysisCreated);

            var result = await _activity.ExecuteAsync(_context);

            result
                .As<FaultResult>()
                .Exception
                .Message
                .Should()
                .Be("Stability Analysis not found");
        }

        [Fact(DisplayName = "When users are not found - Returns Done")]
        public async Task WhenUsersAreNotFound_ReturnsDone()
        {
            SetNotificationTransientVariable(NotificationTheme.StabilityAnalysisCreated);
            GetStabilityAnalysisWithSafetyFactorsMetris();
            MockClientsApiStabilityAnalysisWithSafetyFactorsMetrics();

            var result = await _activity.ExecuteAsync(_context);

            result
                .As<OutcomeResult>()
                .Outcomes
                .First()
                .Should()
                .Be("Done");
        }

        [Theory(DisplayName = "When a stability analysis notification is received - Returns Done")]
        [InlineData(NotificationTheme.StabilityAnalysisCreated)]
        [InlineData(NotificationTheme.StabilityAnalysisUpdated)]
        public async Task WhenAStabilityAnalysisNotificationIsReceived_ReturnsDone(
            NotificationTheme theme)
        {
            SetNotificationTransientVariable(theme);
            MockClientsApiUsers();
            GetStabilityAnalysisWithSafetyFactorsMetris();
            MockClientsApiStabilityAnalysisWithSafetyFactorsMetrics();

            var result = await _activity.ExecuteAsync(_context);

            result
                .As<OutcomeResult>()
                .Outcomes
                .First()
                .Should()
                .Be("Done");
        }

        [Theory(DisplayName = "When a stability analysis notification is received - Should generate notifications to send")]
        [InlineData(NotificationTheme.StabilityAnalysisCreated)]
        [InlineData(NotificationTheme.StabilityAnalysisUpdated)]
        public async Task WhenAStabilityAnalysisNotificationIsReceived_ShouldGenerateNotificationsToSend(
            NotificationTheme theme)
        {
            SetNotificationTransientVariable(theme);
            MockClientsApiUsers();
            GetStabilityAnalysisWithSafetyFactorsMetris();
            MockClientsApiStabilityAnalysisWithSafetyFactorsMetrics();

            _ = await _activity.ExecuteAsync(_context);

            _context
                .GetTransientVariable<List<AddNotificationRequest>>(Variables.Notification)
                .Should()
                .NotBeNull();
        }

        [Theory(DisplayName = "When a stability analysis notification is received - Should generate the expected notification")]
        [InlineData(NotificationTheme.StabilityAnalysisCreated)]
        [InlineData(NotificationTheme.StabilityAnalysisUpdated)]
        public async Task WhenAStabilityAnalysisNotificationIsReceived_ShouldGenerateTheExpectedNotification(
            NotificationTheme expectedTheme)
        {
            SetNotificationTransientVariable(expectedTheme);
            MockClientsApiUsers();
            GetStabilityAnalysisWithSafetyFactorsMetris();
            MockClientsApiStabilityAnalysisWithSafetyFactorsMetrics();

            _ = await _activity.ExecuteAsync(_context);

            Assert
                .True(_context
                .GetTransientVariable<List<AddNotificationRequest>>(Variables.Notification)?
                .Any(notification => notification.NotificationTheme == expectedTheme));
        }

        [Theory(DisplayName = "When safety factors are within acceptable levels - Returns Done")]
        [InlineData(SliFileType.NonCircularDrained, 1.6554757)]
        [InlineData(SliFileType.CircularDrained, 1.53454)]
        [InlineData(SliFileType.NonCircularUndrained, 1.42557)]
        [InlineData(SliFileType.CircularUndrained, 1.355465)]
        [InlineData(SliFileType.NonCircularPseudoStatic, 1.211445)]
        [InlineData(SliFileType.CircularPseudoStatic, 1.10001)]
        public async Task WhenSafetyFactorsAreWithinAcceptableLevels_ReturnsDone(
            SliFileType sliFileType,
            double safetyFactorValue)
        {
            SetNotificationTransientVariable(NotificationTheme.StabilityAnalysisCreated);
            MockClientsApiUsers();
            GetStabilityAnalysisWithSafetyFactorsMetris(
                safetyFactorValue,
                sliFileType);
            MockClientsApiStabilityAnalysisWithSafetyFactorsMetrics();

            var result = await _activity.ExecuteAsync(_context);

            result
                .As<OutcomeResult>()
                .Outcomes
                .First()
                .Should()
                .Be("Done");
        }

        [Theory(DisplayName = "When safety factors are within acceptable levels - Should generate notifications to send")]
        [InlineData(SliFileType.NonCircularDrained, 1.69999)]
        [InlineData(SliFileType.CircularDrained, 1.54547)]
        [InlineData(SliFileType.NonCircularUndrained, 1.49988)]
        [InlineData(SliFileType.CircularUndrained, 1.39911)]
        [InlineData(SliFileType.NonCircularPseudoStatic, 1.29902)]
        [InlineData(SliFileType.CircularPseudoStatic, 1.11165)]
        public async Task WhenSafetyFactorsAreWithinAcceptableLevels_ShouldGenerateNotificationsToSend(
            SliFileType sliFileType,
            double safetyFactorValue)
        {
            SetNotificationTransientVariable(NotificationTheme.StabilityAnalysisCreated);
            MockClientsApiUsers();
            GetStabilityAnalysisWithSafetyFactorsMetris(
                safetyFactorValue,
                sliFileType);
            MockClientsApiStabilityAnalysisWithSafetyFactorsMetrics();

            var result = await _activity.ExecuteAsync(_context);

            _context
                .GetTransientVariable<List<AddNotificationRequest>>(Variables.Notification)
                .Should()
                .NotBeNull();
        }

        [Theory(DisplayName = "When safety factors are within acceptable levels - Should send the other notification themes")]
        [InlineData(NotificationTheme.StabilityAnalysisCreated, SliFileType.NonCircularDrained, 1.629584)]
        [InlineData(NotificationTheme.StabilityAnalysisUpdated, SliFileType.CircularDrained, 1.534958)]
        [InlineData(NotificationTheme.StabilityAnalysisCreated, SliFileType.NonCircularUndrained, 1.4001)]
        [InlineData(NotificationTheme.StabilityAnalysisUpdated, SliFileType.CircularUndrained, 1.308945)]
        [InlineData(NotificationTheme.StabilityAnalysisCreated, SliFileType.NonCircularPseudoStatic, 1.20134)]
        [InlineData(NotificationTheme.StabilityAnalysisUpdated, SliFileType.CircularPseudoStatic, 1.10385)]
        public async Task WhenSafetyFactorsAreWithinAcceptableLevels_ShouldSendTheOtherNotificationThemes(
            NotificationTheme expectedTheme,
            SliFileType sliFileType,
            double safetyFactorValue)
        {
            SetNotificationTransientVariable(expectedTheme);
            MockClientsApiUsers();
            GetStabilityAnalysisWithSafetyFactorsMetris(
                safetyFactorValue,
                sliFileType);
            MockClientsApiStabilityAnalysisWithSafetyFactorsMetrics();

            var result = await _activity.ExecuteAsync(_context);

            Assert
                .True(_context
                .GetTransientVariable<List<AddNotificationRequest>>(Variables.Notification)?
                .All(notification => notification.NotificationTheme == expectedTheme));
        }

        [Theory(DisplayName = "When safety factors are within acceptable levels - Should not send safety factor notification")]
        [InlineData(NotificationTheme.StabilityAnalysisCreated, SliFileType.NonCircularDrained, 1.6453)]
        [InlineData(NotificationTheme.StabilityAnalysisUpdated, SliFileType.CircularDrained, 1.501)]
        [InlineData(NotificationTheme.StabilityAnalysisCreated, SliFileType.NonCircularUndrained, 1.4003)]
        [InlineData(NotificationTheme.StabilityAnalysisUpdated, SliFileType.CircularUndrained, 1.33456)]
        [InlineData(NotificationTheme.StabilityAnalysisCreated, SliFileType.NonCircularPseudoStatic, 1.21124)]
        [InlineData(NotificationTheme.StabilityAnalysisUpdated, SliFileType.CircularPseudoStatic, 1.10101)]
        public async Task WhenSafetyFactorsAreWithinAcceptableLevels_ShouldNotSendSafetyFactorNotification(
            NotificationTheme theme,
            SliFileType sliFileType,
            double safetyFactorValue)
        {
            var unexpectedTheme = NotificationTheme.StabilityAnalysisSafetyFactorBelowToleratedLevels;
            SetNotificationTransientVariable(theme);
            MockClientsApiUsers();
            GetStabilityAnalysisWithSafetyFactorsMetris(
                safetyFactorValue,
                sliFileType);
            MockClientsApiStabilityAnalysisWithSafetyFactorsMetrics();

            var result = await _activity.ExecuteAsync(_context);

            Assert
                .True(_context
                .GetTransientVariable<List<AddNotificationRequest>>(Variables.Notification)?
                .All(notification => notification.NotificationTheme != unexpectedTheme));
        }

        [Theory(DisplayName = "When safety factors are below tolerable levels - Returns Done")]
        [InlineData(SliFileType.NonCircularDrained, 1.44578)]
        [InlineData(SliFileType.NonCircularUndrained, 1.200124)]
        [InlineData(SliFileType.NonCircularPseudoStatic, 1.0345)]
        public async Task WhenSafetyFactorsAreBelowTolerableLevels_ReturnsDone(
            SliFileType sliFileType,
            double safetyFactorValue)
        {
            SetNotificationTransientVariable(NotificationTheme.StabilityAnalysisCreated);
            MockClientsApiUsers();
            GetStabilityAnalysisWithSafetyFactorsMetris(
                safetyFactorValue,
                sliFileType);
            MockClientsApiStabilityAnalysisWithSafetyFactorsMetrics();

            var result = await _activity.ExecuteAsync(_context);

            result
                .As<OutcomeResult>()
                .Outcomes
                .First()
                .Should()
                .Be("Done");
        }

        [Theory(DisplayName = "When safety factors are below tolerable levels - Should generate notifications to send")]
        [InlineData(SliFileType.CircularDrained, 1.3578)]
        [InlineData(SliFileType.CircularUndrained, 1.43451)]
        [InlineData(SliFileType.CircularPseudoStatic, 0.9999)]
        public async Task WhenSafetyFactorsAreBelowTolerableLevels_ShouldGenerateNotificationsToSend(
            SliFileType sliFileType,
            double safetyFactorValue)
        {
            SetNotificationTransientVariable(NotificationTheme.StabilityAnalysisCreated);
            MockClientsApiUsers();
            GetStabilityAnalysisWithSafetyFactorsMetris(
                safetyFactorValue,
                sliFileType);
            MockClientsApiStabilityAnalysisWithSafetyFactorsMetrics();

            _ = await _activity.ExecuteAsync(_context);

            _context
                .GetTransientVariable<List<AddNotificationRequest>>(Variables.Notification)
                .Should()
                .NotBeNull();
        }

        [Theory(DisplayName = "When safety factors are below tolerable levels - Should generate notification for safety factors")]
        [InlineData(NotificationTheme.StabilityAnalysisSafetyFactorBelowToleratedLevels, SliFileType.NonCircularDrained, 1.43465)]
        [InlineData(NotificationTheme.StabilityAnalysisSafetyFactorBelowToleratedLevels, SliFileType.NonCircularUndrained, 1.2001)]
        [InlineData(NotificationTheme.StabilityAnalysisSafetyFactorBelowToleratedLevels, SliFileType.NonCircularPseudoStatic, 1.00034)]
        public async Task WhenSafetyFactorsAreBelowTolerableLevels_ShouldGenerateNotificationForSafetyFactors(
            NotificationTheme expectedTheme,
            SliFileType sliFileType,
            double safetyFactorValue)
        {
            SetNotificationTransientVariable(NotificationTheme.StabilityAnalysisCreated);
            MockClientsApiUsers();
            GetStabilityAnalysisWithSafetyFactorsMetris(
                safetyFactorValue,
                sliFileType);
            MockClientsApiStabilityAnalysisWithSafetyFactorsMetrics();

            _ = await _activity.ExecuteAsync(_context);

            Assert
                .True(_context
                .GetTransientVariable<List<AddNotificationRequest>>(Variables.Notification)?
                .Any(notification => notification.NotificationTheme == expectedTheme));
        }

        [Theory(DisplayName = "When safety factors are below tolerable levels - Should generate the notification message per soil condition")]
        [InlineData(SliFileType.CircularDrained, 1.47455, SoilConditionType.Drained, SafetyFactorAlertLevel.Attention)]
        [InlineData(SliFileType.CircularDrained, 1.32415, SoilConditionType.Drained, SafetyFactorAlertLevel.Attention)]
        [InlineData(SliFileType.NonCircularDrained, 1.1003, SoilConditionType.Drained, SafetyFactorAlertLevel.Alert)]
        [InlineData(SliFileType.NonCircularDrained, 1.05, SoilConditionType.Drained, SafetyFactorAlertLevel.Emergency)]
        [InlineData(SliFileType.CircularUndrained, 1.112, SoilConditionType.Undrained, SafetyFactorAlertLevel.Alert)]
        [InlineData(SliFileType.NonCircularUndrained, 1.067, SoilConditionType.Undrained, SafetyFactorAlertLevel.Emergency)]
        [InlineData(SliFileType.CircularPseudoStatic, 1.0987, SoilConditionType.PseudoStatic, SafetyFactorAlertLevel.Emergency)]
        [InlineData(SliFileType.NonCircularPseudoStatic, 0.9544, SoilConditionType.PseudoStatic, SafetyFactorAlertLevel.Emergency)]
        public async Task WhenSafetyFactorsAreBelowTolerableLevels_ShouldGenerateTheNotificationMessagePerSoilCondition(
            SliFileType sliFileType,
            double safetyFactorValue,
            SoilConditionType expectedSoilConditionType,
            SafetyFactorAlertLevel expectedAlertLevel)
        {
            SetNotificationTransientVariable(NotificationTheme.StabilityAnalysisCreated);
            MockClientsApiUsers();
            GetStabilityAnalysisWithSafetyFactorsMetris(
                safetyFactorValue,
                sliFileType);
            MockClientsApiStabilityAnalysisWithSafetyFactorsMetrics();

            _ = await _activity.ExecuteAsync(_context);

            Assert
                .True(_context
                    .GetTransientVariable<List<AddNotificationRequest>>(Variables.Notification)?
                    .Any(notification =>
                        notification.NotificationMessage.Contains(expectedSoilConditionType.GetDescription())
                        && notification.NotificationMessage.Contains(expectedAlertLevel.GetDescription())));
        }
    }
}
