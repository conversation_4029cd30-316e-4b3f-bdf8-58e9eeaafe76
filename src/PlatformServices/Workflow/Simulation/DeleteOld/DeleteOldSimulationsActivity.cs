using Application.Apis.Clients;
using Elsa.ActivityResults;
using Elsa.Services;
using Elsa.Services.Models;

namespace Workflow.Simulation.DeleteOld
{
    public sealed class DeleteOldSimulationsActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public DeleteOldSimulationsActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var simulations = await _clientsApiService.ListSimulations(new()
                {
                    DaysOfCreation = 30,
                    ShouldKeep = false
                });

                if (simulations == null || !simulations.Any())
                {
                    return Done();
                }

                foreach (var simulation in simulations)
                {
                    await _clientsApiService.DeleteSimulation(simulation.Id);
                }

                return Done();
            }
            catch (Exception e)
            {
                return Fault(e);
            }
        }
    }
}
