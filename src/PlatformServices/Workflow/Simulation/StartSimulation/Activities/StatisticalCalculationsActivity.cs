using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Application.Apis.Clients.Response.Simulation.GetById;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services;
using Elsa.Services.Models;
using Serilog;
using static Workflow.Constants.SimulationWorkFlow;

namespace Workflow.Simulation.StartSimulation.Activities
{
    public class StatisticalCalculationsActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public StatisticalCalculationsActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var simulation = context.GetTransientVariable<GetSimulationByIdResponse>(Variables.Simulation);

                if (simulation.NeedToDoStatisticalCalculations)
                {
                    if (simulation.UpstreamLinimetricRulerStatisticalMeasure != null
                        && simulation.UpstreamLinimetricRulerStatisticalMeasure != Domain.Enums.ReadingStatisticalMeasure.Specific
                        && simulation.UpstreamLinimetricRulerStatisticalMeasure != Domain.Enums.ReadingStatisticalMeasure.Latest)
                    {
                        var request = new GetReadingStatsRequest()
                        {
                            StartDate = simulation.StartDate,
                            EndDate = simulation.EndDate,
                            ReadingStatisticalMeasure = simulation.UpstreamLinimetricRulerStatisticalMeasure.Value,
                            Instruments = new()
                            {
                                new()
                                {
                                     InstrumentId = simulation.UpstreamLinimetricRulerId.Value
                                }
                            }
                        };

                        var readingsStats = await _clientsApiService.GetReadingStats(request);

                        if (readingsStats.Any())
                        {
                            simulation.UpstreamLinimetricRulerQuota = readingsStats.First().Quota;
                        }
                    }

                    if (simulation.DownstreamLinimetricRulerStatisticalMeasure != null
                        && simulation.DownstreamLinimetricRulerStatisticalMeasure != Domain.Enums.ReadingStatisticalMeasure.Specific
                        && simulation.DownstreamLinimetricRulerStatisticalMeasure != Domain.Enums.ReadingStatisticalMeasure.Latest)
                    {
                        var request = new GetReadingStatsRequest()
                        {
                            StartDate = simulation.StartDate,
                            EndDate = simulation.EndDate,
                            ReadingStatisticalMeasure = simulation.DownstreamLinimetricRulerStatisticalMeasure.Value,
                            Instruments = new()
                            {
                                new()
                                {
                                     InstrumentId = simulation.DownstreamLinimetricRulerId.Value
                                }
                            }
                        };

                        var readingsStats = await _clientsApiService.GetReadingStats(request);

                        if (readingsStats.Any())
                        {
                            simulation.DownstreamLinimetricRulerQuota = readingsStats.First().Quota;
                        }
                    }

                    foreach (var section in simulation.Sections)
                    {
                        if (simulation.ReadingStatisticalMeasure != null
                            && simulation.ReadingStatisticalMeasure != Domain.Enums.ReadingStatisticalMeasure.Specific)
                        {
                            var request = new GetReadingStatsRequest()
                            {
                                StartDate = simulation.StartDate,
                                EndDate = simulation.EndDate,
                                ReadingStatisticalMeasure = simulation.ReadingStatisticalMeasure.Value,
                                Instruments = section.Instruments.Select(i => new GetReadingStatsInstrument
                                {
                                    InstrumentId = i.InstrumentId,
                                    MeasurementId = i.MeasurementId
                                }).ToList()
                            };

                            var readingsStats = await _clientsApiService.GetReadingStats(request);

                            var instrumentsWithoutStats = section.Instruments
                                .Where(i => !readingsStats.Any(rs => rs.InstrumentId == i.InstrumentId)).ToList();

                            section.IgnoredInstruments = string.Join(", ", instrumentsWithoutStats.Select(i => i.InstrumentIdentifier));

                            section.Instruments = section.Instruments
                                .Where(i => readingsStats.Any(rs => rs.InstrumentId == i.InstrumentId)).ToList();

                            foreach (var readingStat in readingsStats)
                            {
                                var instrument = section
                                    .Instruments
                                    .FirstOrDefault(i => i.InstrumentId == readingStat.InstrumentId && i.MeasurementId == readingStat.MeasurementId);

                                if (instrument != null)
                                {
                                    instrument.Quota = readingStat.Quota;
                                }
                            }
                        }
                    }

                    var sectionsToCalculate = simulation.Sections
                        .Where(s => s.BeachLengthStatisticalMeasure != null
                            && s.BeachLengthStatisticalMeasure != Domain.Enums.ReadingStatisticalMeasure.Specific)
                        .ToList();

                    if (sectionsToCalculate.Any())
                    {
                        var request = new GetBeachLengthStatsRequest()
                        {
                            SectionIds = sectionsToCalculate.Select(s => s.SectionId).ToList(),
                            StartDate = simulation.StartDate,
                            EndDate = simulation.EndDate,
                            ReadingStatisticalMeasure = sectionsToCalculate.First().BeachLengthStatisticalMeasure.Value
                        };

                        var beachLengthStats = await _clientsApiService.GetBeachLengthStats(request);

                        foreach (var beachLengthStat in beachLengthStats)
                        {
                            var section = simulation.Sections.FirstOrDefault(s => s.SectionId == beachLengthStat.SectionId);

                            if (section != null)
                            {
                                section.BeachLength = beachLengthStat.Length;
                            }
                        }
                    }

                    simulation.NeedToDoStatisticalCalculations = false;
                    context.SetTransientVariable(Variables.Simulation, simulation);
                }

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Error in StatisticalCalculationsActivity");
                context.JournalData.Add(Workflow.Constants.StabilityAnalysisWorkFlow.Variables.ErrorMessage, $"Error in StatisticalCalculationsActivity: {e.Message}");
                return Outcome(OutcomeNames.Cancel);
            }
        }
    }
}
