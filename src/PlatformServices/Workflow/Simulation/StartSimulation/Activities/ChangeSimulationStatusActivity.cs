using Application.Apis.Clients;
using Elsa.ActivityResults;
using Elsa.Services;
using Elsa.Services.Models;
using Serilog;
using static Workflow.Constants.SimulationWorkFlow;
using StabilityAnalysisVariables = Workflow.Constants.StabilityAnalysisWorkFlow;

namespace Workflow.Simulation.StartSimulation.Activities
{
    public class ChangeSimulationStatusActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public ChangeSimulationStatusActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var simulationId = context.GetTransientVariable<Domain.Messages.Commands.Simulation.StartSimulation>(Variables.Command).Id;
                var errorMessage = context.GetTransientVariable<string>(StabilityAnalysisVariables.Variables.ErrorMessage);

                var entityHasBeenUpdated = await _clientsApiService.UpdateSimulationStatus(new()
                {
                    Id = simulationId,
                    Status = Domain.Enums.SimulationStatus.Failed,
                    Event = string.IsNullOrEmpty(errorMessage) ? "Erro inesperado nos processos internos da simulação." : errorMessage
                });

                if (!entityHasBeenUpdated)
                {
                    Log.Warning("There was a problem updating simulation {0} status", simulationId);
                    return Fault($"There was a problem updating simulation {simulationId} status");
                }

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Error in ChangeSimulationStatusActivity");
                return Fault(e);
            }
        }
    }
}
