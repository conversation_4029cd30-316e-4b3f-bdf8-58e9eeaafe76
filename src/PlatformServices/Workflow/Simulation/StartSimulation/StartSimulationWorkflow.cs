using Elsa;
using Elsa.Activities.ControlFlow;
using Elsa.Activities.MassTransit;
using Elsa.Activities.Primitives;
using Elsa.Builders;
using Workflow.Simulation.StartSimulation.Activities;
using Workflow.StabilityAnalysis.Activities;
using static Workflow.Constants.StabilityAnalysisWorkFlow;

namespace Workflow.Simulation.StartSimulation
{
    public class StartSimulationWorkflow : IWorkflow
    {
        public void Build(IWorkflowBuilder builder)
        {
            builder
                .AsTransient()
                .WithDisplayName("Start simulation workflow")
                .ReceiveMassTransitMessage(activity => activity.Set(x => x.MessageType, x => typeof(Domain.Messages.Commands.Simulation.StartSimulation)))
                .WithDisplayName("Receive start simulation message")
                .WithDescription("When receive the message, will get the simulation.")
                .SetTransientVariable(Variables.Command, context => context.GetInput<Domain.Messages.Commands.Simulation.StartSimulation>())
                .Then<GetSimulationActivity>(activity =>
                {
                    activity
                        .When(OutcomeNames.Cancel)
                        .Then<ChangeSimulationStatusActivity>()
                        .Finish();
                })
                .Then<StatisticalCalculationsActivity>(activity =>
                {
                    activity
                        .When(OutcomeNames.Cancel)
                        .Then<ChangeSimulationStatusActivity>()
                        .Finish();
                })
                .Then<MountSimulationStructureInfoActivity>(activity =>
                {
                    activity
                        .When(OutcomeNames.Cancel)
                        .Then<ChangeSimulationStatusActivity>()
                        .Finish();
                })
                .Then<MountSliFilesActivity>(activity =>
                {
                    activity
                        .When(OutcomeNames.Cancel)
                        .Then<CreateStabilityAnalysisErrorNotificationActivity>()
                        .Then<ChangeSimulationStatusActivity>()
                        .Finish();
                })
                .Then<CheckDrainageAlertActivity>(activity =>
                {
                    activity
                        .When(OutcomeNames.Cancel)
                        .Then<CreateStabilityAnalysisErrorNotificationActivity>()
                        .Then<ChangeSimulationStatusActivity>()
                        .Finish();
                })
                .Then<MountSltmFilesActivity>(activity =>
                {
                    activity
                        .When(OutcomeNames.Cancel)
                        .Then<CreateStabilityAnalysisErrorNotificationActivity>()
                        .Then<ChangeSimulationStatusActivity>()
                        .Finish();
                })
                .Then<GetS01FilesActivity>(activity =>
                {
                    activity
                        .When(OutcomeNames.Cancel)
                        .Then<CreateStabilityAnalysisErrorNotificationActivity>()
                        .Then<ChangeSimulationStatusActivity>()
                        .Finish();
                })
                .Then<ExtractInformationFromS01FilesActivity>(activity =>
                {
                    activity
                        .When(OutcomeNames.Cancel)
                        .Then<CreateStabilityAnalysisErrorNotificationActivity>()
                        .Then<ChangeSimulationStatusActivity>()
                        .Finish();
                })                
                .Then<CheckSlopeLimitWarningActivity>(activity =>
                {
                    activity
                        .When(OutcomeNames.Cancel)
                        .Then<CreateStabilityAnalysisErrorNotificationActivity>()
                        .Then<ChangeSimulationStatusActivity>()
                        .Finish();
                })
                .Then<PatchSimulationActivity>(activity =>
                {
                    activity
                        .When(OutcomeNames.Cancel)
                        .Then<CreateStabilityAnalysisErrorNotificationActivity>()
                        .Then<ChangeSimulationStatusActivity>()
                        .Finish();
                })
                .Finish();
        }
    }
}