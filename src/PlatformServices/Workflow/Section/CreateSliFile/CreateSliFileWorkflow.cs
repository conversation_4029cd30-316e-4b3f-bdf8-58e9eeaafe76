using Elsa.Activities.ControlFlow;
using Elsa.Activities.MassTransit;
using Elsa.Activities.Primitives;
using Elsa.Builders;
using Workflow.Section.Activities;
using static Workflow.Constants.SectionWorkFlow;

namespace Workflow.Section.CreateSliFile;

public class CreateSliFileWorkflow : IWorkflow
{
    public void Build(IWorkflowBuilder builder)
    {
        builder
            .WithDeleteCompletedInstances(true)
            .WithDisplayName("Create initial SLI file workflow")
            .ReceiveMassTransitMessage(
                activity => activity.Set(x => x.MessageType, x => typeof(Domain.Messages.Commands.Section.CreateSliFile)))
            .WithDisplayName("Receive command to create file")
            .SetTransientVariable(Variables.Command, context => context.GetInput<Domain.Messages.Commands.Section.CreateSliFile>())
            .Then<CreateInitialSliFileActivity>()
            .Finish();
    }
}