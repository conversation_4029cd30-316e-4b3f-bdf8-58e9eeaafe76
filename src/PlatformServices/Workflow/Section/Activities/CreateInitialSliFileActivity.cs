using System.Text;
using Application.Apis.Clients;
using Dxf.Core.Extensions;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using IxMilia.Dxf;
using Serilog;
using Slide.Core;
using TriangleNet.Geometry;
using TriangleNet.Meshing;
using Workflow.Extensions;
using static Workflow.Constants.SectionWorkFlow;
using Activity = Elsa.Services.Activity;
using File = Application.Apis._Shared.File;

namespace Workflow.Section.Activities
{
    public class CreateInitialSliFileActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public CreateInitialSliFileActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var sectionId = (context.GetTransientVariable<Domain.Messages.Commands.Section.CreateSliFile>(Variables.Command)).SectionId;

                var section = await _clientsApiService
                    .GetSectionById(sectionId);

                if (section == null)
                {
                    return Done();
                }

                foreach (var sectionReview in section.Reviews)
                {
                    if (sectionReview.ConstructionStages.Any())
                    {
                        foreach (var constructionStage in sectionReview.ConstructionStages)
                        {
                            var constructionStageSli = MountSliFile(constructionStage.Drawing);

                            if (constructionStageSli == null)
                            {
                                continue;
                            }

                            var result = await _clientsApiService.AddSliToSectionReview(new()
                            {
                                SectionId = sectionId,
                                ReviewId = sectionReview.Id,
                                ConstructionStageId = constructionStage.Id,
                                Sli = constructionStageSli
                            });

                            if (!result.IsSuccessStatusCode)
                            {
                                Log.Error("Error adding SLI to construction stage.");
                                return Fault("Error adding SLI to construction stage.");
                            }
                        }

                        continue;
                    }

                    var reviewSli = MountSliFile(sectionReview.Drawing);

                    if (reviewSli == null)
                    {
                        continue;
                    }

                    var resultSectionReview = await _clientsApiService.AddSliToSectionReview(new()
                    {
                        SectionId = sectionId,
                        ReviewId = sectionReview.Id,
                        Sli = reviewSli
                    });

                    if (!resultSectionReview.IsSuccessStatusCode)
                    {
                        Log.Error("Error adding SLI to section review.");
                        return Fault("Error adding SLI to section review.");
                    }
                }

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Error in CreateInitialSliFileActivity");
                return Fault(e);
            }
        }

        private static File MountSliFile(File dxfFile)
        {
            if (dxfFile == null || string.IsNullOrEmpty(dxfFile.Base64))
            {
                return null;
            }

            var dxf = new DxfFile().LoadWithBase64(dxfFile.Base64);

            var externalVertices = dxf.GetExternalVertices();

            if (externalVertices.Count == 0)
            {
                Log.Information("No external vertices found in the DXF file.");
                return null;
            }

            // Send the first vertex to the end of the list
            externalVertices = externalVertices.Skip(1).Concat(externalVertices.Take(1)).ToList();

            var materialVertices = dxf.GetMaterialVertices();

            if (materialVertices.Count == 0)
            {
                Log.Information("No materials found in the DXF file.");
                return null;
            }

            var polygon = new Polygon()
                .AddExternal(externalVertices)
                .AddMaterials(materialVertices);

            var triangulationOptions = new ConstraintOptions()
            {
                ConformingDelaunay = true,
                Convex = false,
                SegmentSplitting = 2
            };
            
            var mesh = polygon.Triangulate(triangulationOptions);

            var sliFile = new SliFile()
                .FromDxf(externalVertices, materialVertices, mesh);

            var textBytes = Encoding.UTF8.GetBytes(sliFile.Save());

            return new()
            {
                Base64 = Convert.ToBase64String(textBytes),
                Name = $"{DateTime.Now:ddMMyyyy_HHmmss}.sli"
            };
        }
    }
}
