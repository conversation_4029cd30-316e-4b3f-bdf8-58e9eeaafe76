using Application.Apis.Clients;
using Application.Report;
using Domain.Enums;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services.Models;
using Workflow.Extensions;
using static Workflow.Constants.ReportWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.Report.Activities
{
    [Activity(
        Category = "Report",
        DisplayName = "Update report emission dates",
        Description = "Will update report emission dates for internal control.",
        Outcomes = new[] { OutcomeNames.Done }
    )]
    public sealed class UpdateReportEmissionDatesActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public UpdateReportEmissionDatesActivity(
            IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
            Thread.CurrentThread.ChangeCulture(Locale.PtBr);
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
            ActivityExecutionContext context)
        {
            try
            {
                var report = context.GetTransientVariable<PendingReport>(Variables.Report);

                if (report == null)
                {
                    return Outcome(OutcomeNames.Done);
                }

                var lastEmissionDate = DateTime.UtcNow;

                var nextEmissionDate = report.Configuration
                    .GetNextEmissionDate(lastEmissionDate);

                _ = await _clientsApiService
                    .UpdateReportEmissionDates(
                        report.Configuration.Id,
                        nextEmissionDate.Value,
                        lastEmissionDate);

                context.JournalData.Add(Variables.ReportUpdatedDates, true);

                return Outcome(OutcomeNames.Done);
            }
            catch (Exception e)
            {
                return Fault(e);
            }
        }
    }
}
