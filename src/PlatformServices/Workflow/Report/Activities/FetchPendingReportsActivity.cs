using Application.Apis.Clients;
using Cronos;
using Domain.Enums;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services.Models;
using Workflow.Extensions;
using static Workflow.Constants.ReportWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.Report.Activities
{
    [Activity(
        Category = "Report",
        DisplayName = "Fetch Pending Reports",
        Description = "Will search for pending reports since last run.",
        Outcomes = new[] { OutcomeNames.True, OutcomeNames.False }
    )]
    public sealed class FetchPendingReportsActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public FetchPendingReportsActivity(
            IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
            Thread.CurrentThread.ChangeCulture(Locale.PtBr);
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
            ActivityExecutionContext context)
        {
            try
            {
                Thread.CurrentThread.ChangeCulture(Locale.PtBr);

                var expression = context.GetVariable<string>(Variables.CronExpression);
                var hoursToLookBack = context.GetVariable<int>(Variables.HoursToLookBack);

                var lastExecutionDate = CronExpression
                    .Parse(expression, CronFormat.IncludeSeconds)
                    .GetOccurrences(DateTime.UtcNow.AddHours(-hoursToLookBack), DateTime.UtcNow)
                    .TakeLast(2)
                    .First();

                var pendingReports = await _clientsApiService.GetPendingReports(lastExecutionDate, DateTime.UtcNow);

                context.SetTransientVariable(Variables.PendingReports, pendingReports);
                context.JournalData.Add(Variables.PendingReports, pendingReports);

                return (pendingReports?.Any() ?? false)
                    ? Outcome(OutcomeNames.True)
                    : Outcome(OutcomeNames.False);
            }
            catch (Exception ex)
            {
                context.JournalData.Add("ExceptionMessage", ex.Message);
                return Fault(ex);
            }
        }
    }
}
