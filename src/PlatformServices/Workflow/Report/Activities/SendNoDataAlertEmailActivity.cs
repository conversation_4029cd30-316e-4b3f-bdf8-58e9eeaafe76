using Application.Email;
using Application.Report;
using Domain.Enums;
using Domain.Resources;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Microsoft.Extensions.Localization;
using System.Net.Mail;
using System.Text;
using Workflow.Extensions;
using static Workflow.Constants.ReportWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.Report.Activities
{
    public sealed class SendNoDataAlertEmailActivity : Activity
    {
        private readonly IEmailService _emailService;
        private readonly IStringLocalizer<Texts> _localizer;

        public SendNoDataAlertEmailActivity(
            IEmailService emailService,
            IStringLocalizer<Texts> localizer)
        {
            _emailService = emailService;
            _localizer = localizer;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
            ActivityExecutionContext context)
        {
            try
            {
                Thread.CurrentThread.ChangeCulture(Locale.PtBr);
                var report = context.GetTransientVariable<PendingReport>(Variables.Report);

                if (report == null)
                {
                    return Outcome(OutcomeNames.Done);
                }

                var message = new MailMessage();

                message.To.Add(string.Join(',', report.Configuration.DestinationEmails));
                message.Subject = _localizer.GetString(LocaleKeys.EoRReportNoDataEmailTitle).Value;

                message.Body = string.Format(
                    _localizer.GetString(LocaleKeys.EoRReportNoDataEmailBody),
                    report.Configuration.Title,
                    report.Configuration.StartDate,
                    report.Configuration.EndDate);

                await _emailService.Send(message);

                context.JournalData.Add(Variables.NoDataEmailSent, true);

                return Outcome(OutcomeNames.Done);
            }
            catch (Exception e)
            {
                return Fault(e);
            }
        }
    }
}
