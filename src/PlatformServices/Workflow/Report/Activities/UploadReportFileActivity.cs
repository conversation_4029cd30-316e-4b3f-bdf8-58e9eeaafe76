using Application.BlobStorage;
using Application.Report;
using Domain.Enums;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services.Models;
using Microsoft.Extensions.Options;
using Workflow.Extensions;
using static Workflow.Constants.ReportWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.Report.Activities
{
    [Activity(
        Category = "Report",
        DisplayName = "Upload Report File",
        Description = "Will upload the report file.",
        Outcomes = new[] { OutcomeNames.Done }
    )]
    public sealed class UploadReportFileActivity : Activity
    {
        private readonly IBlobStorageService _blobStorageService;
        private readonly BlobStorageOptions _blobStorageOptions;

        public UploadReportFileActivity(
            IBlobStorageService blobStorageService,
            IOptions<BlobStorageOptions> blobStorageOptions)
        {
            _blobStorageService = blobStorageService;
            _blobStorageOptions = blobStorageOptions.Value;
            Thread.CurrentThread.ChangeCulture(Locale.PtBr);
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
            ActivityExecutionContext context)
        {
            try
            {
                var report = context.GetTransientVariable<PendingReport>(Variables.Report);

                using var fileStream = new FileStream(report.Result.FileInfo.Path, FileMode.Open, FileAccess.Read);

                await _blobStorageService.UploadAsync(fileStream, report.Result.FileInfo.Name, _blobStorageOptions.ClientsContainer);

                context.JournalData.Add(Variables.ReportFileUploaded, true);

                return Outcome(OutcomeNames.Done);
            }
            catch (Exception ex)
            {
                return Fault(ex);
            }
        }
    }
}
