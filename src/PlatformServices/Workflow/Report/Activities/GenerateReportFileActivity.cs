using Application.Report;
using Domain.Enums;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services.Models;
using Microsoft.AspNetCore.Hosting;
using Workflow.Extensions;
using static Workflow.Constants.ReportWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.Report.Activities
{
    [Activity(
        Category = "Report",
        DisplayName = "Generate Report File",
        Description = "Will generate the report file with the found data.",
        Outcomes = new[] { OutcomeNames.Done }
    )]
    public sealed class GenerateReportFileActivity : Activity
    {
        private readonly IWebHostEnvironment _webHostEnvironment;

        public GenerateReportFileActivity(
            IWebHostEnvironment webHostEnvironment)
        {
            _webHostEnvironment = webHostEnvironment;
            Thread.CurrentThread.ChangeCulture(Locale.PtBr);
        }

        protected override IActivityExecutionResult OnExecute(
            ActivityExecutionContext context)
        {
            try
            {
                var report = context.GetTransientVariable<PendingReport>(Variables.Report);

                var reportGenerator = ReportGeneratorFactory
                    .GetInstance(report.Configuration.SubjectType);

                var outputFilePath = reportGenerator.Generate(report, _webHostEnvironment.WebRootPath);

                context.JournalData.Add(Variables.ReportFilePath, outputFilePath);

                return Outcome(OutcomeNames.Done);
            }
            catch (Exception ex)
            {
                return Fault(ex);
            }
        }
    }
}
