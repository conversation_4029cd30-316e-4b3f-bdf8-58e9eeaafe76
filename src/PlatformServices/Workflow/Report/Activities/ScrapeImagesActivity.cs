using Application.Apis.Clients.Response.Report;
using Application.Report;
using Application.WebScraper;
using Domain.Enums;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services;
using Elsa.Services.Models;
using Microsoft.AspNetCore.Hosting;
using Workflow.Extensions;
using static Workflow.Constants.ReportWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.Report.Activities
{
    [Activity(
        Category = "Report",
        DisplayName = "Scrape Images",
        Description = "Will scrape and save all the captured images that compose the report.",
        Outcomes = new[] { OutcomeNames.Done }
    )]
    public sealed class ScrapeImagesActivity : Activity
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly IDictionary<ReportSubjectType, IWebScraper> _webScrapers;
        private readonly IDistributedLockProvider _distributedLockProvider;

        public ScrapeImagesActivity(
            IWebHostEnvironment webHostEnvironment,
            IEoRReportScraper eorWebScraper,
            IDistributedLockProvider distributedLockProvider)
        {
            _webHostEnvironment = webHostEnvironment;

            _webScrapers = new Dictionary<ReportSubjectType, IWebScraper>
            {
                [ReportSubjectType.EoR] = eorWebScraper
            };
            
            _distributedLockProvider = distributedLockProvider;

            Thread.CurrentThread.ChangeCulture(Locale.PtBr);
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
            ActivityExecutionContext context)
        {
            try
            {
                var report = context.GetTransientVariable<PendingReport>(Variables.Report);
                
                await using var @lock = await _distributedLockProvider
                    .AcquireLockAsync($"get-report-data-{report.Configuration.Id}");

                if (@lock == null)
                {
                    context.JournalData.Add(Variables.Report,
                        $"Could not acquire lock. Report {report.Configuration.Id} is being processed by another workflow instance.");

                    return Outcome(OutcomeNames.Cancel);
                }

                await _webScrapers[report.Configuration.SubjectType].ScrapeAsync(report, _webHostEnvironment.WebRootPath);

                return Outcome(OutcomeNames.Done);
            }
            catch (Exception ex)
            {
                return Fault(ex);
            }
        }
    }
}
