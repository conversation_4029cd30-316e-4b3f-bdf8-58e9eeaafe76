using Application.Apis.Clients;
using Application.Apis.Clients.Response.Report;
using Application.Report;
using Domain.Entities.Report;
using Domain.Enums;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services;
using Elsa.Services.Models;
using Workflow.Extensions;
using static Workflow.Constants.ReportWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.Report.Activities
{
    [Activity(
        Category = "Report",
        DisplayName = "Search Report Data",
        Description = "Will search for data to fill the report.",
        Outcomes = new[] { OutcomeNames.True, OutcomeNames.False, OutcomeNames.Cancel }
    )]
    public sealed class SearchReportDataActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;
        private readonly IDistributedLockProvider _distributedLockProvider;

        public SearchReportDataActivity(
            IClientsApiService clientsApiService,
            IDistributedLockProvider distributedLockProvider)
        {
            _clientsApiService = clientsApiService;
            _distributedLockProvider = distributedLockProvider;
            Thread.CurrentThread.ChangeCulture(Locale.PtBr);
        }

        protected override async ValueTask<IActivityExecutionResult>
            OnExecuteAsync(
                ActivityExecutionContext context)
        {
            try
            {
                var item = context.GetInput<GetPendingReportResponse>();

                await using var @lock = await _distributedLockProvider
                    .AcquireLockAsync($"get-report-data-{item.Id}");

                if (@lock == null)
                {
                    context.JournalData.Add(Variables.Report,
                        $"Could not acquire lock. Report {item.Id} is being processed by another workflow instance.");

                    return Outcome(OutcomeNames.Cancel);
                }

                var reportData = await _clientsApiService
                    .GetReportData<GetEoRReportDataResponse>(item.Id);

                var pendingReport = new PendingReport
                {
                    Configuration = item,
                    RawData = reportData,
                    Result = new ReportResult
                    {
                        IntermediatePayload =
                            new Dictionary<string, object>(),
                        DefinitivePayload = reportData?.ToPayload(item),
                        ImagePayload =
                            new Dictionary<string, ReportPicture>()
                    },
                };

                context.SetTransientVariable(Variables.Report,
                    pendingReport);

                if (reportData == null)
                {
                    context.JournalData.Add(Variables.Report,
                        "No report data was found.");
                    return Outcome(OutcomeNames.False);
                }
                
                context.JournalData.Add(Variables.Report, pendingReport);
                return Outcome(OutcomeNames.True);
            }
            catch (Exception ex)
            {
                return Fault(ex);
            }
        }
    }
}