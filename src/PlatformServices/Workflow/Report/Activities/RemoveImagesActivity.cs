using Application.Report;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services.Models;
using Microsoft.AspNetCore.Hosting;
using static Workflow.Constants.ReportWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.Report.Activities
{
    [Activity(
        Category = "Report",
        DisplayName = "Remove images",
        Description = "Will remove the scraped images.",
        Outcomes = new[] { OutcomeNames.Done }
    )]
    public sealed class RemoveImagesActivity : Activity
    {
        private readonly IWebHostEnvironment _webHostEnvironment;

        public RemoveImagesActivity(IWebHostEnvironment webHostEnvironment)
        {
            _webHostEnvironment = webHostEnvironment;
        }

        protected override IActivityExecutionResult OnExecute(
            ActivityExecutionContext context)
        {
            try
            {
                var report = context.GetTransientVariable<PendingReport>(Variables.Report);
                var imageDirectory = report.GetOutputImageDirectory(_webHostEnvironment.WebRootPath);

                if (Directory.Exists(imageDirectory))
                {
                    Directory.Delete(imageDirectory, true);
                }

                return Outcome(OutcomeNames.Done);
            }
            catch (Exception ex)
            {
                return Fault(ex);
            }
        }
    }
}
