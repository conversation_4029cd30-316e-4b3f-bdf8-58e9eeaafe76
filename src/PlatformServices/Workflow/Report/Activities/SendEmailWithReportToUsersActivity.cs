using Application.Email;
using Application.Report;
using Domain.Enums;
using Domain.Resources;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services.Models;
using Microsoft.Extensions.Localization;
using Workflow.Extensions;
using static Workflow.Constants.ReportWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.Report.Activities
{
    [Activity(
        Category = "Report",
        DisplayName = "Send a report to users via email",
        Description = "Will send one or more reports via email to one or more destinations.",
        Outcomes = new[] { OutcomeNames.Done }
    )]
    public sealed class SendEmailWithReportToUsersActivity : Activity
    {
        private readonly IEmailService _emailService;
        private readonly IStringLocalizer<Texts> _localizer;

        public SendEmailWithReportToUsersActivity(
            IEmailService emailService,
            IStringLocalizer<Texts> localizer)
        {
            _emailService = emailService;
            _localizer = localizer;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
            ActivityExecutionContext context)
        {
            try
            {
                Thread.CurrentThread.ChangeCulture(Locale.PtBr);
                var report = context.GetTransientVariable<PendingReport>(Variables.Report);

                var message = report.ToMailMessage(_localizer);

                await _emailService.Send(message);

                context.JournalData.Add(Variables.ReportEmailSent, true);

                return Done();
            }
            catch (Exception ex)
            {
                return Fault(ex);
            }
        }
    }
}
