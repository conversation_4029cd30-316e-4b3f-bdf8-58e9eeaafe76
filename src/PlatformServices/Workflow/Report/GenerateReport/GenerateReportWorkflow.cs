using Elsa;
using Elsa.Activities.ControlFlow;
using Elsa.Builders;
using Workflow.Report.Activities;

namespace Workflow.Report.GenerateReport;

public sealed class GenerateReportWorkflow : IWorkflow
{
    public void Build(IWorkflowBuilder builder)
    {
        builder
            .WithDeleteCompletedInstances(true)
            .WithDisplayName("Generate and send reports to users")
            .Then<SearchReportDataActivity>(activityBuilder =>
            {
                activityBuilder
                    .When(OutcomeNames.Cancel)
                    .Finish();
                        
                activityBuilder
                    .When(OutcomeNames.False)
                    .Then<UpdateReportEmissionDatesActivity>()
                    .Then<SendNoDataAlertEmailActivity>();

                activityBuilder
                    .When(OutcomeNames.True)
                    .Then<ScrapeImagesActivity>()
                    .Then<GenerateReportFileActivity>()
                    .Then<UploadReportFileActivity>()
                    .Then<SendEmailWithReportToUsersActivity>()
                    .Then<RemoveImagesActivity>()
                    .Then<UpdateReportEmissionDatesActivity>();
            });
    }
}