using Elsa.Activities.MassTransit;
using Elsa.Builders;
using Workflow.User.AcceptedTerm.Activities;

namespace Workflow.User.AcceptedTerm;

public class AcceptedTermWorkflow : IWorkflow
{
    public void Build(IWorkflowBuilder builder)
    {
        builder
            .WithDeleteCompletedInstances(true)
            .WithDisplayName("Send email when user accepts terms of service workflow")
            .ReceiveMassTransitMessage(
                activity => activity.Set(x => x.MessageType, x => typeof(Domain.Messages.Events.User.AcceptedTerm))
            )
            .WithDisplayName("Receive user accepted term message")
            .WithDescription("When receive the message, will send a email to the user notifying.")
            .Then<SendEmailToUsersAcceptedTermActivity>()
            .WithDisplayName("Send email");
    }
}