using Application.Email;
using Domain.Resources;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services;
using Elsa.Services.Models;
using Microsoft.Extensions.Localization;
using System.Net.Mail;
using System.Text;
using Workflow.Extensions;

namespace Workflow.User.AcceptedTerm.Activities
{
    [Activity(
        Category = "Users",
        DisplayName = "Sends email to users who have accepted the term of service",
        Description = "Will send email to users who have accepted the term of service",
        Outcomes = new[] { OutcomeNames.Done }
    )]
    public class SendEmailToUsersAcceptedTermActivity : Activity
    {
        private readonly IEmailService _emailService;
        private readonly IStringLocalizer<Texts> _localizer;

        public SendEmailToUsersAcceptedTermActivity(
            IEmailService emailService,
            IStringLocalizer<Texts> localizer)
        {
            _emailService = emailService;
            _localizer = localizer;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var message = context
                    .GetInput<Domain.Messages.Events.User.AcceptedTerm>();

                Thread.CurrentThread.ChangeCulture(message.Locale);

                var mailMessage = new MailMessage()
                {
                    Subject = _localizer.GetString(LocaleKeys.AcceptedTermsEmailTitle).Value,
                    Body = string.Format(
                    _localizer.GetString(LocaleKeys.AcceptedTermsEmailBody).Value,
                    message.FirstName, message.Date.ToShortDateString())
                };

                mailMessage.To.Add(message.EmailAddress);

                await _emailService.Send(mailMessage);

                return Done();
            }
            catch (Exception e)
            {
                return Fault(e);
            }
        }
    }
}
