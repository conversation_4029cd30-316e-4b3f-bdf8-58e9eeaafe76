using Elsa.Activities.Temporal;
using Elsa.Builders;

namespace Workflow.Notification.DeleteOld;

public class DeleteOldNotificationsWorkflow : IWorkflow
{
    public void Build(IWorkflowBuilder builder)
    {
        builder
            .WithDeleteCompletedInstances(true)
            .WithDisplayName("Delete old notifications workflow")
            .Cron("0 0 6 ? 1-12 * *")
            .WithDisplayName("Cron running every month at 6:00UTC")
            .Then<DeleteOldNotificationsActivity>();
    }
}