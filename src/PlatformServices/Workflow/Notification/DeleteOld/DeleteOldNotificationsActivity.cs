using Application.Apis.Clients;
using Elsa.ActivityResults;
using Elsa.Services;
using Elsa.Services.Models;

namespace Workflow.Notification.DeleteOld
{
    public class DeleteOldNotificationsActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public DeleteOldNotificationsActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                await _clientsApiService.DeleteNotifications();
                return Done();
            }
            catch (Exception e)
            {
                return Fault(e);
            }
        }
    }
}
