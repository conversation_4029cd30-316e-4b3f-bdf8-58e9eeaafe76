using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Domain.Extensions;
using Domain.Messages.Commands.Notification;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using MassTransit;
using Serilog;
using static Application.Apis.Clients.Extensions.UsersExtensions;
using static Workflow.Constants.NotificationWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.Notification.AddNotification.Activities
{
    public class GenerateControlLetterPayloadActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public GenerateControlLetterPayloadActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var message = context.GetTransientVariable<CreateNotification>(Variables.Message);

                var instrumentResponse = await _clientsApiService.GetInstrumentById(message.Id);

                if (instrumentResponse == null)
                {
                    return Fault(new Exception("Instrumento não encontrado"));
                }

                var users = await _clientsApiService.GetUsersAsync(
                    GetListUserRequestForNotification(instrumentResponse.Structure.Id));

                if (users == null || !users.Any())
                {
                    return Done();
                }

                var notificationMessage = String.Format(
                    message.GetControlLetterMessageByAction(),
                    instrumentResponse.Identifier,
                    instrumentResponse.Structure.Name,
                    message.CreatedDate.ToString("dd/MM/yyyy HH:mm:ss"));

                context.SetTransientVariable(
                    Variables.Notification,
                    new List<AddNotificationRequest>()
                    {
                        new AddNotificationRequest
                        {
                            Users = users.Select(x => x.Id).ToList(),
                            NotificationTheme = message.Theme,
                            NotificationMessage = notificationMessage
                        }
                    });

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Ocorreu um problema ao processar a notificação: " + e.Message);
                return Fault(e);
            }
        }
    }
}
