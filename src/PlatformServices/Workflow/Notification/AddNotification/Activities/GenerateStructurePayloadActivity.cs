using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Domain.Messages.Commands.Notification;
using Elsa.ActivityResults;
using Elsa.Services;
using Elsa.Services.Models;
using static Workflow.Constants.NotificationWorkFlow;
using static Application.Apis.Clients.Extensions.UsersExtensions;

namespace Workflow.Notification.AddNotification.Activities;

public sealed class GenerateStructurePayloadActivity : Activity
{
    private readonly IClientsApiService _clientsApiService;

    public GenerateStructurePayloadActivity(
        IClientsApiService clientsApiService)
    {
        _clientsApiService = clientsApiService;
    }

    protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
        ActivityExecutionContext context)
    {
        try
        {
            var input =
                context.GetTransientVariable<CreateNotification>(Variables
                    .Message);

            var message = input.ToStructureNotification();

            var structureResponse =
                await _clientsApiService.GetStructureById(message.Id);

            if (structureResponse == null)
            {
                return Fault(new Exception("Estrutura não encontrada"));
            }

            message.AuxiliaryData.Add("StructureName", structureResponse.Name);
            message.AuxiliaryData.Add("ClientUnitName",
                structureResponse.ClientUnit.Name);

            var users = await _clientsApiService.GetUsersAsync(
                GetListUserRequestForNotification(structureResponse.Id));

            if (users == null || !users.Any())
            {
                return Done();
            }

            var userId = message.CreatedById ?? Guid.Empty;

            var modifiedBy = await _clientsApiService.GetUserById(userId);

            message.AuxiliaryData.Add("ModifiedBy",
                modifiedBy?.GetFullName() ?? UnknownUser);

            context.SetTransientVariable(
                Variables.Notification,
                new List<AddNotificationRequest>
                {
                    new()
                    {
                        Users = users.Select(user => user.Id).ToList(),
                        NotificationTheme = message.Theme,
                        NotificationMessage = message.GenerateMessage(),
                    }
                });

            return Done();
        }
        catch (Exception ex)
        {
            return Fault(ex);
        }
    }
}