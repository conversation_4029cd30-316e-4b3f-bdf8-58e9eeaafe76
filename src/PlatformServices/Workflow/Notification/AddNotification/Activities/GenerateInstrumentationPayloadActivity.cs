using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Domain.Messages.Commands.Notification;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Serilog;
using static Application.Apis.Clients.Extensions.UsersExtensions;
using static Workflow.Constants.NotificationWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.Notification.AddNotification.Activities;

public class GenerateInstrumentationPayloadActivity : Activity
{
    private readonly IClientsApiService _clientsApiService;

    public GenerateInstrumentationPayloadActivity(
        IClientsApiService clientsApiService)
    {
        _clientsApiService = clientsApiService;
    }

    protected override async ValueTask<IActivityExecutionResult>
        OnExecuteAsync(ActivityExecutionContext context)
    {
        try
        {
            var input =
                context.GetTransientVariable<CreateNotification>(Variables
                    .Message);

            var message = input.ToInstrumentNotification();

            var instrumentResponse =
                await _clientsApiService.GetInstrumentById(message.Id);

            if (instrumentResponse == null)
            {
                return Fault(new Exception("Instrumento não encontrado"));
            }

            message.AuxiliaryData.Add("InstrumentId",
                instrumentResponse.Id.ToString());
            message.AuxiliaryData.Add("InstrumentName",
                instrumentResponse.Identifier);
            message.AuxiliaryData.Add("StructureName",
                instrumentResponse.Structure.Name);

            var users = await _clientsApiService.GetUsersAsync(
                GetListUserRequestForNotification(instrumentResponse
                    .Structure.Id));

            if (users == null || !users.Any())
            {
                return Done();
            }

            var userId = message.CreatedById ?? Guid.Empty;

            var modifiedBy = await _clientsApiService.GetUserById(userId);

            message.AuxiliaryData.Add("ModifiedBy",
                modifiedBy?.GetFullName() ?? UnknownUser);

            context.SetTransientVariable(
                Variables.Notification,
                new List<AddNotificationRequest>
                {
                    new()
                    {
                        Users = users.Select(x => x.Id).ToList(),
                        NotificationTheme = message.Theme,
                        NotificationMessage = message.GenerateMessage(),
                    }
                });

            return Done();
        }
        catch (Exception e)
        {
            Log.Error(e,
                "Ocorreu um problema ao processar a notificação: " +
                e.Message);
            return Fault(e);
        }
    }
}