
using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Application.Apis.Clients.Response.StabilityAnalysis.GetByIdWithSafetyFactorsMetrics;
using Domain.Enums;
using Domain.Extensions;
using Domain.Messages.Commands.Notification;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services.Models;
using static Application.Apis.Clients.Extensions.UsersExtensions;
using static Workflow.Constants.NotificationWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.Notification.AddNotification.Activities
{
    [Activity(
        Category = "Notification",
        DisplayName = "Send Stability Analysis Notification",
        Description = "Will process an stability analysis notifications",
        Outcomes = new[] { OutcomeNames.Done }
    )]
    public class GenerateStabilityAnalysisPayloadActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public GenerateStabilityAnalysisPayloadActivity(
            IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
            ActivityExecutionContext context)
        {
            try
            {
                var notification = context.GetTransientVariable<CreateNotification>(Variables.Message);

                var stabilityAnalysis = await _clientsApiService
                    .GetStabilityAnalysisWithSafetyFactorsMetricsAsync(notification.Id);

                if (stabilityAnalysis == null)
                {
                    return Fault(new InvalidOperationException("Stability Analysis not found"));
                }

                var usersIds = (await _clientsApiService
                    .GetUsersAsync(
                        GetListUserRequestForNotification(stabilityAnalysis.StructureId)))?
                    .Select(user => user.Id)
                    .ToList();

                if (usersIds == null || !usersIds.Any())
                {
                    return Done();
                }

                var notificationsToSend = new List<AddNotificationRequest>
                {
                    new AddNotificationRequest
                    {
                        Users = usersIds,
                        NotificationTheme = notification.Theme,
                        NotificationMessage = String.Format(
                            notification.Theme.GetStabilityAnalysisMessageByAction(),
                            stabilityAnalysis.StructureName,
                            stabilityAnalysis.ReadingCreatedDate,
                            stabilityAnalysis.CalculatedByFullName ?? UnknownUser)
                    }
                };

                var notificationsForSafetyFactores = CheckSafetyFactorsValues(stabilityAnalysis, usersIds);
                if (notificationsForSafetyFactores != null || !notificationsForSafetyFactores.Any())
                {
                    notificationsToSend.AddRange(notificationsForSafetyFactores);
                }

                if (stabilityAnalysis.Warnings != null && stabilityAnalysis.Warnings.Any())
                {
                    var warnings = stabilityAnalysis.Warnings
                        .Where(warning => !string.IsNullOrWhiteSpace(warning));

                    if (warnings.Any())
                    {
                        notificationsToSend.Add(new()
                        {
                            Users = usersIds,
                            NotificationTheme = NotificationTheme.StabilityAnalysisWithWarnings,
                            NotificationMessage = String.Format(
                            NotificationTheme.StabilityAnalysisWithWarnings.GetStabilityAnalysisMessageByAction(),
                            stabilityAnalysis.StructureName,
                            stabilityAnalysis.ReadingCreatedDate,
                            stabilityAnalysis.CalculatedByFullName ?? UnknownUser,
                            string.Join(", ", warnings))
                        });
                    }
                }   

                context.SetTransientVariable(
                    Variables.Notification,
                    notificationsToSend);

                return Done();
            }
            catch (Exception exception)
            {
                return Fault(exception);
            }
        }

        public List<AddNotificationRequest> CheckSafetyFactorsValues(
            GetStabilityAnalysisByIdWithSafetyFactorsMetricsResponse stabilityAnalysis,
            List<Guid> usersIds)
        {
            var notifications = new List<AddNotificationRequest>();

            foreach (var safetyFactor in stabilityAnalysis.SafetyFactorValueResults)
            {
                var metrics = stabilityAnalysis
                    .SafetyFactorMetricResults
                        .Where(m => m.SoilConditionType == safetyFactor.SliFileType.MapSoilConditionType())
                        .OrderByDescending(m => m.ReferenceValue);

                foreach (var (actualMetric, index) in metrics.Select((metric, index) => (metric, index)))
                {
                    var nextMetric = metrics.ElementAtOrDefault(index + 1);

                    if (safetyFactor.Value < actualMetric.ReferenceValue
                        && (nextMetric == null
                        || safetyFactor.Value >= nextMetric.ReferenceValue))
                    {
                        notifications.Add(new AddNotificationRequest
                        {
                            Users = usersIds,
                            NotificationTheme = NotificationTheme.StabilityAnalysisSafetyFactorBelowToleratedLevels,
                            NotificationMessage = String.Format(
                                NotificationTheme.StabilityAnalysisSafetyFactorBelowToleratedLevels.GetStabilityAnalysisMessageByAction(),
                                actualMetric.AlertLevel.GetDescription(),
                                actualMetric.SoilConditionType.GetDescription(),
                                stabilityAnalysis.SectionName,
                                stabilityAnalysis.StructureName,
                                actualMetric.ReferenceValue)
                        });

                        break;
                    }
                }
            }

            return notifications;
        }
    }
}
