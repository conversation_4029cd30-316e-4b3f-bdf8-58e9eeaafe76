using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Domain.Extensions;
using Domain.Messages.Commands.Notification;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Serilog;
using static Application.Apis.Clients.Extensions.UsersExtensions;
using static Workflow.Constants.NotificationWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.Notification.AddNotification.Activities
{
    public class GenerateRepairedOrDamagedInstrumentPayloadActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public GenerateRepairedOrDamagedInstrumentPayloadActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var message = context.GetTransientVariable<CreateNotification>(Variables.Message);

                var instrumentResponse = await _clientsApiService.GetInstrumentById(message.Id);

                if (instrumentResponse == null)
                {
                    return Fault(new Exception("Instrumento não encontrado"));
                }

                var users = await _clientsApiService.GetUsersAsync(
                    GetListUserRequestForNotification(instrumentResponse.Structure.Id));

                if (users == null || !users.Any())
                {
                    return Done();
                }

                var userId = Guid.Empty;

                if (message.CreatedById != Guid.Empty && message.CreatedById != null)
                {
                    userId = (Guid)message.CreatedById;
                }

                var modifiedBy = await _clientsApiService.GetUserById(userId);

                var modifiedByFullName = modifiedBy != null
                    ? modifiedBy.FirstName + " " + modifiedBy.Surname
                    : UnknownUser;

                var notificationMessage = String.Format(
                    message.GetInstrumentationMessageByAction(),
                    instrumentResponse.Identifier,
                    instrumentResponse.Structure.Name,
                    message.CreatedDate.ToString("dd/MM/yyyy HH:mm:ss"),
                    modifiedByFullName);

                context.SetTransientVariable(
                    Variables.Notification,
                    new List<AddNotificationRequest>()
                    {
                        new AddNotificationRequest
                        {
                            Users = users.Select(x => x.Id).ToList(),
                            NotificationTheme = message.Theme,
                            NotificationMessage = notificationMessage
                        }
                    });

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Ocorreu um problema ao processar a notificação: " + e.Message);
                return Fault(e);
            }
        }
    }
}
