using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Elsa.ActivityResults;
using Elsa.Services;
using Elsa.Services.Models;
using Serilog;
using static Workflow.Constants.NotificationWorkFlow;

namespace Workflow.Notification.AddNotification.Activities
{
    public class SendNotificationActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;
        private const int MaxNotificationMessageLength = 512;

        public SendNotificationActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var notificationsToSend = context.GetTransientVariable<List<AddNotificationRequest>>(Variables.Notification);
                if (notificationsToSend == null || !notificationsToSend.Any())
                {
                    return Fault(new Exception("Mensagem de notificação não encontrada."));
                }

                foreach (var notification in notificationsToSend)
                {
                    if (notification.NotificationMessage.Length > MaxNotificationMessageLength)
                    {
                        notification.NotificationMessage = notification.NotificationMessage.Substring(0, MaxNotificationMessageLength);
                    }

                    await _clientsApiService.AddNotification(notification);
                }

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Ocorreu um problema ao enviar a notificação: " + e.Message);
                return Fault(e);
            }
        }
    }
}
