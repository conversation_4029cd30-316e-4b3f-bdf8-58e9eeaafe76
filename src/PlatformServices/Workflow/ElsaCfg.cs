using Elsa;
using Elsa.Activities.Email.Options;
using Elsa.Activities.MassTransit.Extensions;
using Elsa.Options;
using Elsa.Persistence.EntityFramework.Core.Extensions;
using Elsa.Persistence.EntityFramework.SqlServer;
using Elsa.Retention.Extensions;
using Elsa.Retention.Options;
using Microsoft.Extensions.DependencyInjection;
using Workflow.AutomatedReading.Activities;
using Workflow.AutomatedReading.Activities.Converters.AtlanticNikel;
using Workflow.AutomatedReading.Activities.Converters.Mosaic;
using Workflow.AutomatedReading.DeleteReadings;
using Workflow.AutomatedReading.ImportReadings;
using Workflow.Client.DisabledClient;
using Workflow.Client.DisabledClient.Activities;
using Workflow.Client.NotifyExpiring;
using Workflow.Client.NotifyExpiring.Activities;
using Workflow.InspectionSheet.TranscribeInspectionSheet;
using Workflow.InspectionSheet.TranscribeInspectionSheet.Activities;
using Workflow.Notification.AddNotification;
using Workflow.Notification.AddNotification.Activities;
using Workflow.Notification.DeleteOld;
using Workflow.OccurrenceListReport;
using Workflow.OccurrenceListReport.Activities;
using Workflow.Package.Activities;
using Workflow.Package.CalculateStability;
using Workflow.Reading.Activities;
using Workflow.Reading.GenerateAlerts;
using Workflow.Reading.ReadingCreated;
using Workflow.Reading.ReadingUpdated;
using Workflow.Reading.RecalculateStability;
using Workflow.Report.Activities;
using Workflow.Report.FetchPendingReports;
using Workflow.Report.GenerateReport;
using Workflow.Section.Activities;
using Workflow.Section.CreateSliFile;
using Workflow.Simulation.DeleteOld;
using Workflow.Simulation.StartSimulation;
using Workflow.Simulation.StartSimulation.Activities;
using Workflow.StabilityAnalysis.Activities;
using Workflow.User.AcceptedTerm;
using Workflow.User.AcceptedTerm.Activities;

namespace Workflow
{
    public static class ElsaCfg
    {
        public static IServiceCollection AddWorkflows(
               this IServiceCollection services,
               string connectionString,
               SmtpOptions smtpOptions,
               CleanupOptions cleanupOptions)
        {
            services
                .AddElsaApiEndpoints()
                .AddElsa(elsa => elsa
                    .UseEntityFrameworkPersistence(ef => ef.UseSqlServer(connectionString))
                    .ConfigureDistributedLockProvider(options => options.UseSqlServerLockProvider(connectionString))
                    .AddConsoleActivities()
                    .AddEmailActivities(x =>
                    {
                        x.Host = smtpOptions.Host;
                        x.Port = smtpOptions.Port;
                        x.UserName = smtpOptions.UserName;
                        x.Password = smtpOptions.Password;
                        x.DefaultSender = smtpOptions.DefaultSender;
                        x.RequireCredentials = true;
                        x.SecureSocketOptions = MailKit.Security.SecureSocketOptions.Auto;
                    })
                    .AddQuartzTemporalActivities()
                    .AddMassTransitActivities()
                    .AddActivities()
                    .AddWorkflows()
                )
                .AddRetentionServices(options =>
                {
                    options.SweepInterval = cleanupOptions.SweepInterval;
                    options.TimeToLive = cleanupOptions.TimeToLive;
                    options.BatchSize = cleanupOptions.BatchSize;
                });

            return services;
        }

        private static ElsaOptionsBuilder AddActivities(this ElsaOptionsBuilder options)
        {
            options
                .AddActivity<SendEmailToUsersAcceptedTermActivity>()
                .AddActivity<GetExpiringClientsActivity>()
                .AddActivity<GetUsersToNotifyActivity>()
                .AddActivity<AddNotificationToExpiringClientsActivity>()
                .AddActivity<SendEmailToExpiringClientsActivity>()
                .AddActivity<DisableUsersWithClientIdActivity>()
                .AddActivity<GenerateSecurityLevelAlertActivity>()
                .AddActivity<GetPackageActivity>()
                .AddActivity<GetSectionsActivity>()
                .AddActivity<GetInstrumentsActivity>()
                .AddActivity<ValidateSectionsFilesActivity>()
                .AddActivity<GetStructureActivity>()
                .AddActivity<ValidateSectionsStructureActivity>()
                .AddActivity<CreateInitialSliFileActivity>()
                .AddActivity<DeleteOldNotificationsActivity>()
                .AddActivity<FetchPendingReportsActivity>()
                .AddActivity<SearchReportDataActivity>()
                .AddActivity<ScrapeImagesActivity>()
                .AddActivity<GenerateReportFileActivity>()
                .AddActivity<UploadReportFileActivity>()
                .AddActivity<SendEmailWithReportToUsersActivity>()
                .AddActivity<UpdateReportEmissionDatesActivity>()
                .AddActivity<RemoveImagesActivity>()
                .AddActivity<CheckDrainageAlertActivity>()
                .AddActivity<CreateDrainageAlertActivity>()
                .AddActivity<SendNotificationActivity>()
                .AddActivity<GenerateInstrumentationPayloadActivity>()
                .AddActivity<GenerateReadingsPayloadActivity>()
                .AddActivity<GenerateControlLetterPayloadActivity>()
                .AddActivity<CreateStabilityAnalysisActivity>()
                .AddActivity<CreateStabilityAnalysisErrorNotificationActivity>()
                .AddActivity<GenerateRepairedOrDamagedInstrumentPayloadActivity>()
                .AddActivity<GenerateStructurePayloadActivity>()
                .AddActivity<GetRelatedReadingValuesActivity>()
                .AddActivity<CreatePackagesActivity>()
                .AddActivity<CalculatePackagesStabilityActivity>()
                .AddActivity<GetSimulationActivity>()
                .AddActivity<StatisticalCalculationsActivity>()
                .AddActivity<PatchSimulationActivity>()
                .AddActivity<MountSliFilesActivity>()
                .AddActivity<MountSltmFilesActivity>()
                .AddActivity<GetS01FilesActivity>()
                .AddActivity<ExtractInformationFromS01FilesActivity>()
                .AddActivity<MountSimulationStructureInfoActivity>()
                .AddActivity<MountStructureInfoActivity>()
                .AddActivity<GenerateStabilityAnalysisPayloadActivity>()
                .AddActivity<GetClientConfigurationsActivity>()
                .AddActivity<GetPendingReadingFilesActivity>()
                .AddActivity<MoveProcessedFilesActivity>()
                .AddActivity<SendReadingsActivity>()
                .AddActivity<DeleteProcessedFilesActivity>()
                .AddActivity<MosaicFileConverterActivity>()
                .AddActivity<DeleteOldSimulationsActivity>()
                .AddActivity<ValeFileConverterActivity>()
                .AddActivity<ChangePackageStatusActivity>()
                .AddActivity<ChangeSimulationStatusActivity>()
                .AddActivity<SendNoDataAlertEmailActivity>()
                .AddActivity<AtlanticNikelFileConverterActivity>()
                .AddActivity<GetInspectionSheetActivity>()
                .AddActivity<TranscribeAudioActivity>()
                .AddActivity<CreateOccurrenceListReportActivity>()
                .AddActivity<SendOccurrenceListReportEmailActivity>()
                .AddActivity<CheckSlopeLimitWarningActivity>()
                .AddActivity<CreateAutomatedReadingErrorNotificationActivity>()
                .AddActivity<UpdateInspectionSheetActivity>();

            return options;
        }

        private static ElsaOptionsBuilder AddWorkflows(this ElsaOptionsBuilder options)
        {
            options
                .AddWorkflow<NotifyExpiringClientWorkflow>()
                .AddWorkflow<DeleteOldNotificationsWorkflow>()
                .AddWorkflow<DisabledClientWorkflow>()
                .AddWorkflow<ReadingCreatedWorkflow>()
                .AddWorkflow<ReadingUpdatedWorkflow>()
                .AddWorkflow<CalculateStabilityWorkflow>()
                .AddWorkflow<CreateSliFileWorkflow>()
                .AddWorkflow<AcceptedTermWorkflow>()
                .AddWorkflow<FetchPendingReportsWorkflow>()
                .AddWorkflow<GenerateReportWorkflow>()
                .AddWorkflow<AddNotificationWorkflow>()
                .AddWorkflow<RecalculateStabilityWorkflow>()
                .AddWorkflow<StartSimulationWorkflow>()
                .AddWorkflow<GenerateAlertsWorkflow>()
                .AddWorkflow<ImportReadingsWorkFlow>()
                .AddWorkflow<DeleteOldSimulationsWorkflow>()
                .AddWorkflow<DeleteReadingsWorkFlow>()
                .AddWorkflow<GenerateOccurrenceListReportWorkflow>()
                .AddWorkflow<TranscribeInspectionSheetWorkflow>();

            return options;
        }
    }
}
