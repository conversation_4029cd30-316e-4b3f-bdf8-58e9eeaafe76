using Domain.Messages.Commands.User;
using Elsa.ActivityResults;
using Elsa.Services;
using Elsa.Services.Models;
using MassTransit;

namespace Workflow.Client.DisabledClient.Activities
{
    public class DisableUsersWithClientIdActivity : Activity
    {
        private readonly ISendEndpointProvider _sendEndpointProvider;

        public DisableUsersWithClientIdActivity(ISendEndpointProvider sendEndpointProvider)
        {
            _sendEndpointProvider = sendEndpointProvider;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var message = new DisableUsers
                {
                    ClientId = context.GetInput<Domain.Messages.Events.Client.DisabledClient>()!.Id
                };

                var endpoint = await _sendEndpointProvider.GetSendEndpoint(new(Queues.DisableUsersWithClientIdQueue));
                await endpoint.Send(message);

                return Done();
            }
            catch (Exception e)
            {
                return Fault(e);
            }
        }
    }
}
