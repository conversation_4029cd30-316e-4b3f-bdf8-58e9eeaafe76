using Elsa.Activities.ControlFlow;
using Elsa.Activities.MassTransit;
using Elsa.Builders;
using Workflow.Client.DisabledClient.Activities;

namespace Workflow.Client.DisabledClient;

public class DisabledClientWorkflow : IWorkflow
{
    public void Build(IWorkflowBuilder builder)
    {
        builder
            .WithDeleteCompletedInstances(true)
            .WithDisplayName("Disable entities when client is disabled workflow")
            .ReceiveMassTransitMessage(
                activity => activity.Set(x => x.MessageType, x => typeof(Domain.Messages.Events.Client.DisabledClient)))
            .WithDisplayName("Receive client disabled message")
            .WithDescription("When receive the message, will disable all entities related to the client.")
            .Then<DisableUsersWithClientIdActivity>()
            .WithDisplayName("Disable client units")
            .WithDescription("This activity will disable all units related to the client.")
            .Finish();
    }
}