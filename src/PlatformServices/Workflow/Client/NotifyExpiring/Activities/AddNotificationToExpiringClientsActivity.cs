using Application.Apis.Clients;
using Application.Apis.Clients.Response;
using Domain.Enums;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services.Models;
using static Workflow.Constants.ClientWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.Client.NotifyExpiring.Activities
{
    [Activity(
        Category = "Client",
        DisplayName = "Add notifications for users",
        Description = "Will add notifications for users linked to the expiring client.",
        Outcomes = new[] { OutcomeNames.Done }
    )]
    public class AddNotificationToExpiringClientsActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public AddNotificationToExpiringClientsActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var expiringClient = context
                    .GetVariable<GetExpiringClientsResponse>(Variables.CurrentValue)!;

                var users = await _clientsApiService.GetUsersAsync(new()
                {
                    ClientId = expiringClient.Id,
                    Active = true,
                    Roles = new()
                        {
                            (int)Role.SuperSupport,
                            (int)Role.SuperAdministrator,
                            (int)Role.Administrator
                        }
                });

                if (users == null || !users.Any())
                {
                    return Done();
                }

                string notificationMessageTemplate = GetNotificationMessageTemplateByPeriod(expiringClient.Period);

                var notificationMessage = String.Format(
                    notificationMessageTemplate,
                    expiringClient.Name,
                    expiringClient.DaysToExpire.ToString());

                await _clientsApiService.AddNotification(new()
                {
                    Users = users.Select(x => x.Id).ToList(),
                    NotificationTheme = Domain.Enums.NotificationTheme.License,
                    NotificationMessage = notificationMessage
                });

                return Done();
            }
            catch (Exception e)
            {
                return Fault(e);
            }
        }

        private static string GetNotificationMessageTemplateByPeriod(Domain.Enums.Period period)
        {
            return period == Domain.Enums.Period.Trial
                ? "O período de testes do cliente {0} vai expirar em {1} dia(s).Por favor contate um administrador para renovar o período."
                : "O período contratual do cliente {0} vai expirar em {1} dia(s). Por favor contate um administrador para renovar o período.";
        }
    }
}
