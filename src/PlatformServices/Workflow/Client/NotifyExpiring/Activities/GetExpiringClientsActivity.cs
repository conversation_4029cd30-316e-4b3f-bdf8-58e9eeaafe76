using Application.Apis.Clients;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services;
using Elsa.Services.Models;
using static Workflow.Constants.ClientWorkFlow;

namespace Workflow.Client.NotifyExpiring.Activities
{
    [Activity(
        Category = "Client",
        DisplayName = "Get expiring clients",
        Description = "Will get clients who are expiring their contract or trial period.",
        Outcomes = new[] { OutcomeNames.Done }
    )]
    public class GetExpiringClientsActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public GetExpiringClientsActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var expiringClients = await _clientsApiService.GetExpiringClients();
                context.SetTransientVariable(Variables.ExpiringClients, expiringClients);

                return Done();
            }
            catch (Exception e)
            {
                return Fault(e);
            }
        }
    }
}
