using Application.Apis.Clients;
using Application.Apis.Clients.Response;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services;
using Elsa.Services.Models;
using static Workflow.Constants.ClientWorkFlow;

namespace Workflow.Client.NotifyExpiring.Activities
{
    [Activity(
        Category = "Client",
        DisplayName = "Get users related to a client",
        Description = "Will get users related to a client to notify them.",
        Outcomes = new[] { OutcomeNames.Done }
    )]
    public class GetUsersToNotifyActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public GetUsersToNotifyActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var expiringClient = context
                    .GetVariable<GetExpiringClientsResponse>(Variables.CurrentValue);

                var users = await _clientsApiService.GetUsersEmails(new()
                {
                    ClientId = expiringClient.Id
                });

                context.SetTransientVariable(Variables.UsersToNotify, users);

                return Done();
            }
            catch (Exception e)
            {
                return Fault(e);
            }
        }
    }
}
