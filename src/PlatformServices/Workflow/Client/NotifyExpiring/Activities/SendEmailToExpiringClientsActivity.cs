using Application.Apis.Clients.Response;
using Application.Apis.Users.Response;
using Application.Email;
using Domain.Resources;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services;
using Elsa.Services.Models;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Localization;
using System.Net.Mail;
using System.Text;
using Workflow.Extensions;
using static Workflow.Constants.ClientWorkFlow;

namespace Workflow.Client.NotifyExpiring.Activities
{
    [Activity(
        Category = "Client",
        DisplayName = "Sends email to users related to expiring clients",
        Description = "Will sends email to users related to expiring clients.",
        Outcomes = new[] { OutcomeNames.Done }
    )]
    public class SendEmailToExpiringClientsActivity : Activity
    {
        private readonly IEmailService _emailService;
        private readonly IStringLocalizer<Texts> _localizer;
        private readonly IHostEnvironment _hostEnvironment;

        public SendEmailToExpiringClientsActivity(
            IEmailService emailService,
            IStringLocalizer<Texts> localizer,
            IHostEnvironment hostEnvironment)
        {
            _emailService = emailService;
            _localizer = localizer;
            _hostEnvironment = hostEnvironment;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                if (!_hostEnvironment.IsProduction())
                {
                    return Done(); // Skip sending emails
                }

                var expiringClient = context
                    .GetVariable<GetExpiringClientsResponse>(Variables.CurrentValue)!;
                var users = context
                    .GetVariable<IEnumerable<GetUserEmailResponse>>(Variables.UsersToNotify)!;

                foreach (var user in users)
                {
                    var message = new MailMessage();

                    message.To.Add(user.Email);

                    AddContentAndTitleInEmail(message,
                        user.Locale,
                        expiringClient.Period,
                        expiringClient.Name,
                        expiringClient.DaysToExpire);

                    await _emailService.Send(message);
                }

                return Done();
            }
            catch (Exception e)
            {
                return Fault(e);
            }
        }

        private void AddContentAndTitleInEmail(
          MailMessage message,
          Domain.Enums.Locale locale,
          Domain.Enums.Period period,
          string clientName,
          int daysToExpire
          )
        {
            Thread.CurrentThread.ChangeCulture(locale);

            message.Subject = period == Domain.Enums.Period.Trial
                ? _localizer
                    .GetString(LocaleKeys.ExpiringTrialPeriodTitle).Value
                : _localizer
                    .GetString(LocaleKeys.ExpiringContractualPeriodTitle).Value;

            message.Body = period == Domain.Enums.Period.Trial
                ? string.Format(_localizer
                    .GetString(LocaleKeys.ExpiringTrialPeriodBody).Value,
                    clientName, daysToExpire)
                : string.Format(_localizer
                    .GetString(LocaleKeys.ExpiringContractualPeriodBody).Value,
                    clientName, daysToExpire);
        }
    }
}
