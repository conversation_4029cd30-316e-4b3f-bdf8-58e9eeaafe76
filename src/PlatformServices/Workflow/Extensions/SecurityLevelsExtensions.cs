using Application.Apis.Clients.Response._Shared;
using Domain.Enums;

namespace Workflow.Extensions
{
    public static class SecurityLevelsExtensions
    {
        public static IEnumerable<SecurityLevelType> GetSecurityLevelAlertsFromDiff(
            this SecurityLevels securityLevel,
            decimal diff)
        {
            var result = new List<SecurityLevelType>();

            if (diff >= securityLevel.Emergency)
            {
                result.Add(SecurityLevelType.Emergency);
            }
            else if (diff >= securityLevel.Alert)
            {
                result.Add(SecurityLevelType.Alert);
            }
            else if (diff >= securityLevel.Attention)
            {
                result.Add(SecurityLevelType.Attention);
            }

            if (diff >= securityLevel.AbruptVariationBetweenReadings)
            {
                result.Add(SecurityLevelType.AbruptVariationBetweenReadings);
            }

            return result;
        }
    }
}
