using Application.Apis.Clients.Response.Report;
using Application.WebScraper.Constants;
using Domain.Enums;
using Domain.Extensions;
using static Application.WebScraper.Constants.TextPlaceholders.Eor;

namespace Workflow.Extensions
{
    public static class EorReportExtensions
    {
        public static IDictionary<string, object> ToPayload(
            this GetEoRReportDataResponse that,
            GetPendingReportResponse reportConfiguration)
        {
            Thread.CurrentThread.ChangeCulture(Locale.PtBr);

            return new Dictionary<string, object>
            {
                [ReportResponsibleName] = reportConfiguration.ResponsibleName,
                [ReportEmissionDate] = DateTime.UtcNow.ToString("dd/MM/yyyy"),
                [ReportStartDate] = reportConfiguration.StartDate.ToString("dd/MM/yyyy"),
                [ReportEndDate] = reportConfiguration.EndDate.ToString("dd/MM/yyyy"),
                [ReportMonthAndYearOfStartDate] = reportConfiguration.StartDate.ToString("MMMM/yyyy"),
                [ReportMonthOfStartDate] = reportConfiguration.StartDate.ToString("MMMM"),
                [ReportMonthOfEndDate] = reportConfiguration.EndDate.ToString("MMMM"),
                [ReportYearOfEndDate] = reportConfiguration.EndDate.ToString("yyyy"),
                [ReportPreviousYearOfEndDate] = reportConfiguration.EndDate.AddYears(-1).ToString("yyyy"),
                [StructureName] = that.Structure.Name,
                [ClientUnitCity] = that.Structure.CityName,
                [ClientUnitName] = that.ClientUnit.Name,
                [ClientName] = that.Client.Name,
                [StructureCoordinateEasting] = that.Structure.UtmCoordinatesEasting,
                [StructureCoordinateNorthing] = that.Structure.UtmCoordinatesNorthing,
                [StructureDatum] = that.Structure.Datum,
                [StructurePurpose] = that.Structure.Purpose,
                [StructureConstructionStages] = that.Structure.ConstructionStages,
                [StructureCrestQuota] = that.Structure.CrestQuota,
                [StructureTotalHeight] = that.Structure.TotalHeight,
                [StructureCrestDimensionWidth] = that.Structure.CrestWidth,
                [StructureCrestDimensionLength] = that.Structure.CrestLength,
                [TextPlaceholders.Eor.StructureStatus] = that.Structure.Status.GetDescription(),
                [StructureYearOfConstructionInitialDike] = that.Structure.ConstructionYear,
                [StructureSectionType] = that.Structure.SectionType,
                [StructureFoundationType] = that.Structure.FoundationType,
                [StructureRaisingMethod] = that.Structure.RaisingMethod,
                [StructureExpectedElevations] = that.Structure.ExpectedElevations,
                [StructureElevationsMade] = that.Structure.ElevationsMade,
                [StructureReservoirDesignVolume] = that.Structure.ReservoirDesignVolume,
                [StructureCurrentReservoirVolume] = that.Structure.CurrentReservoirVolume,
                [StructureUpstreamSlope] = that.Structure.UpstreamSlope,
                [StructureDownstreamSlope] = that.Structure.DownstreamSlope,
                [StructureInternalDrainage] = that.Structure.InternalDrainage,
                [StructureSuperficialDrainage] = that.Structure.SuperficialDrainage,
                [StructureBasinAreaInSquareKilometers] = that.Structure.BasinAreaInSquareKilometers,
                [StructureProjectPrecipitation] = that.Structure.ProjectPrecipitation,
                [StructureFullOfProject] = that.Structure.FullOfProject,
                [StructureMaximumInfluentFlow] = that.Structure.MaximumInfluentFlow,
                [StructureProjectFlow] = that.Structure.ProjectFlow,
                [StructureNormalMaximumWaterLevel] = that.Structure.NormalMaximumWaterLevel,
                [StructureMaximumWaterLevelMaximorum] = that.Structure.MaximumWaterLevelMaximorum,
                [StructureFreeboardNormalMaximumWaterLevel] = that.Structure.FreeboardNormalMaximumWaterLevel,
                [StructureFreeboardMaximumWaterLevelMaximorum] = that.Structure.FreeboardMaximumWaterLevelMaximorum,
                [StructureSpillway] = that.Structure.Spillway,
                [StructureNumberOfPiezometers] = that.InstrumentStatistics.NumberOfPiezometers,
                [StructureNumberOfWaterLevelIndicators] = that.InstrumentStatistics.NumberOfWaterLevelIndicators,
                [StructureNumberOfSurfaceLandmarks] = that.InstrumentStatistics.NumberOfSurfaceLandmarks,
                [StructureNumberSettlementGauges] = that.InstrumentStatistics.NumberOfSettlementGauges,
                [StructureNumberOfInclinometers] = that.InstrumentStatistics.NumberOfInclinometers,
                [StructureNumberOfPluviographs] = that.InstrumentStatistics.NumberOfPluviographs,
                [StructureNumberOfPluviometers] = that.InstrumentStatistics.NumberOfPluviometers,
                [ReadingsNumberOfPluviometers] = that.InstrumentStatistics.NumberOfPluviometersWithReadings,
                [ReadingsNumberOfPiezometers] = that.InstrumentStatistics.NumberOfPiezometersWithReadings,
                [ReadingsNumberOfWaterLevelIndicators] = that.InstrumentStatistics.NumberOfWaterLevelIndicatorsWithReadings,
                [ReadingsNumberOfSurfaceLandmarks] = that.InstrumentStatistics.NumberOfSurfaceLandmarksWithReadings,
                [ReadingsNumberSettlementGauges] = that.InstrumentStatistics.NumberOfSettlementGaugesWithReadings,
                [ReadingsNumberOfInclinometers] = that.InstrumentStatistics.NumberOfInclinometersWithReadings,
                [ReadingsNumberOfPluviographs] = that.InstrumentStatistics.NumberOfPluviographsWithReadings,
                [PluviometryReportPeriodAccumulated] = that.PluviometryStatistics?.ReportPeriodAccumulated,
                [PluviometryAllTimeRelativeAverage] = that.PluviometryStatistics?.AllTimeRelativeAverage,
                [PluviometryLastYearRelativeAverage] = that.PluviometryStatistics?.LastYearRelativeAverage,
                [PluviometryAllTimeAverage] = that.PluviometryStatistics?.AllTimeAverage,
                [PluviometryCurrentYearAccumulated] = that.PluviometryStatistics?.Data?.FirstOrDefault(data => data.Type == PluviometryTimePeriod.CurrentYear)?.TotalPluviometry,
                [PluviometryLastYearAccumulated] = that.PluviometryStatistics?.Data?.FirstOrDefault(data => data.Type == PluviometryTimePeriod.LastYear)?.TotalPluviometry,
            };
        }
    }
}
