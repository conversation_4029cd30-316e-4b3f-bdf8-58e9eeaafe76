using Slide.Core.Objects.Sli;
using Workflow.StabilityAnalysis.Classes;

namespace Workflow.Extensions;

public static class SlopeLimitsExtensions
{
    /// <summary>
    /// Determines whether the given global minimum Factor of safety (Fs) values are within the slope alert limits. Based on X coordinates.
    /// </summary>
    /// <param name="slopeLimit">The slope limits to compare against.</param>
    /// <param name="globalMinFs">The global minimum factor of safety values to evaluate.</param>
    /// <param name="minDistSlopeAlert">The minimum distance threshold for triggering a slope alert.</param>
    /// <returns>
    /// True if any of the global minimum Fs values are within the specified distance of the slope limits; otherwise, false.
    /// </returns>
    /// <remarks>
    /// This method checks the distances between the global minimum Fs values (X1 and X2) and the slope limit boundaries (X1, X2, X3, X4).
    /// If any of these distances are less than or equal to the specified minimum distance, the method returns true.
    /// </remarks>
    public static bool IsWithinSlopeAlert(
        this SlopeLimits slopeLimit,
        GlobalMinimumFs globalMinFs,
        double minDistSlopeAlert)
    {
        return IsDistanceUnderAlertLimit(globalMinFs.X1) ||
               IsDistanceUnderAlertLimit(globalMinFs.X2);
        
        bool IsDistanceUnderAlertLimit(double referentialX)
        {
            var distanceToX1 = Math.Abs(referentialX - slopeLimit.X1);
            var distanceToX2 = Math.Abs(referentialX - slopeLimit.X2);

            var distanceToX3 = slopeLimit.X3.HasValue
                ? Math.Abs(referentialX - slopeLimit.X3.Value)
                : null as double?;

            var distanceToX4 = slopeLimit.X4.HasValue
                ? Math.Abs(referentialX - slopeLimit.X4.Value)
                : null as double?;

            return distanceToX1 <= minDistSlopeAlert ||
                   distanceToX2 <= minDistSlopeAlert ||
                   distanceToX3 <= minDistSlopeAlert ||
                   distanceToX4 <= minDistSlopeAlert;
        }
    }
}