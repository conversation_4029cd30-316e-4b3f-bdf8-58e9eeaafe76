using System.Globalization;
using System.Text;

namespace Workflow.Extensions;

public static class StringExtensions
{
    private static readonly string[] WaterKeywords =
    {
        "waterline", "linha d'agua", "piezo", "piezometrica", "watertable", "water table", "wt", "linha da agua", "freática"
    };

    private static readonly string[] LimitsKeywords =
    {
        "limites", "limits", "slope_limits", "slopelimits", "slope-limits"
    };
    
    /// <summary>
    /// Determines whether the input string contains any of the predefined keywords 
    /// related to slope limits, ignoring case and diacritics.
    /// </summary>
    /// <param name="input">The input string to check for slope limit keywords.</param>
    /// <returns>
    /// <c>true</c> if the input string contains any slope limit-related keywords; 
    /// otherwise, <c>false</c>.
    /// </returns>
    /// <remarks>
    /// This method uses a predefined list of keywords such as "limites", "limits", 
    /// "slope_limits", "slopelimits", and "slope-limits". It normalizes the input 
    /// string and keywords by removing diacritics and converting them to lowercase 
    /// before performing the comparison.
    /// </remarks>
    public static bool ContainsLimitsKeyword(this string input)
    {
        return LimitsKeywords.Any(keyword =>
            input
                .ToLower()
                .RemoveDiacritics()
                .Contains(keyword.RemoveDiacritics()));
    }

    /// <summary>
    /// Determines whether the input string contains any of the predefined keywords
    /// related to water, ignoring case and diacritics.
    /// </summary>
    /// <param name="input">The input string to check for water-related keywords.</param>
    /// <returns>
    /// <c>true</c> if the input string contains any water-related keywords;
    /// otherwise, <c>false</c>.
    /// </returns>
    /// <remarks>
    /// This method uses a predefined list of keywords such as "waterline", "linha d'agua",
    /// "piezo", "piezometrica", "watertable", "water table", "wt", "linha da agua", and "freática".
    /// It normalizes the input string and keywords by removing diacritics and converting them
    /// to lowercase before performing the comparison.
    /// </remarks>
    public static bool ContainsWaterKeyword(this string input)
    {
        return WaterKeywords.Any(keyword =>
            input
                .ToLower()
                .RemoveDiacritics()
                .Contains(keyword.RemoveDiacritics()));
    }

    private static string RemoveDiacritics(this string text)
    {
        if (text == null)
            return null;

        var normalizedString = text.Normalize(NormalizationForm.FormD);
        var stringBuilder = new StringBuilder();

        foreach (var c in normalizedString)
        {
            var unicodeCategory = CharUnicodeInfo.GetUnicodeCategory(c);
            if (unicodeCategory != UnicodeCategory.NonSpacingMark)
            {
                stringBuilder.Append(c);
            }
        }

        return stringBuilder.ToString().Normalize(NormalizationForm.FormC);
    }
}