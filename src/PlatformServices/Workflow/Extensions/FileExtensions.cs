using Dxf.Core.Extensions;
using IxMilia.Dxf;
using File = Application.Apis._Shared.File;

namespace Workflow.Extensions;

public static class FileExtensions
{
    /// <summary>
    /// Loads a DXF file from a base64-encoded string.
    /// </summary>
    /// <param name="dxfToUse">The file object containing the base64-encoded DXF data.</param>
    /// <returns>
    /// A <see cref="DxfFile"/> object representing the loaded DXF file.
    /// </returns>
    /// <exception cref="ArgumentNullException">
    /// Thrown if the provided <paramref name="dxfToUse"/> is null.
    /// </exception>
    public static DxfFile LoadDxfFile(this File dxfToUse)
    {
        ArgumentNullException.ThrowIfNull(dxfToUse);
        
        var dxf = new DxfFile();
        return dxf.LoadWithBase64(dxfToUse.Base64);
    }
}