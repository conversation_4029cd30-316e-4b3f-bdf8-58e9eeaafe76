using Domain.Enums;
using Workflow.StabilityAnalysis.Classes;

namespace Workflow.Extensions;

public static class InstrumentExtensions
{
    public static bool YCoordinateIsValid(this Instrument instrument)
    {
        var isValidWhenReadingIsNotDry =
            instrument.PointD.Y != default && !instrument.DryReading;

        var isValidWhenReadingIsDry = instrument.DryReading
            && instrument.PointD.Y != default
            && instrument.InstrumentInfo?.DryType == DryType.Base;

        return isValidWhenReadingIsNotDry || isValidWhenReadingIsDry;
    }
}
