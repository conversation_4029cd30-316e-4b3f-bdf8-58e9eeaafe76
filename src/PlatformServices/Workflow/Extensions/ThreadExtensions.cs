using Domain.Enums;
using System.Globalization;

namespace Workflow.Extensions
{
    public static class ThreadExtensions
    {
        public static void ChangeCulture(this Thread thread, Locale locale)
        {
            switch (locale)
            {
                case Locale.PtBr:
                    thread.CurrentUICulture = new CultureInfo("pt-BR");
                    thread.CurrentCulture = new CultureInfo("pt-BR");
                    break;
                case Locale.En:
                    thread.CurrentUICulture = new CultureInfo("en-US");
                    thread.CurrentCulture = new CultureInfo("en-US");
                    break;
                case Locale.Es:
                    thread.CurrentUICulture = new CultureInfo("es-ES");
                    thread.CurrentCulture = new CultureInfo("es-ES");
                    break;
                default:
                    break;
            }
        }
    }
}
