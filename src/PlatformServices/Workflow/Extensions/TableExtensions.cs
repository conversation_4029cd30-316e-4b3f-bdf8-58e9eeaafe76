using MigraDoc.DocumentObjectModel;
using MigraDoc.DocumentObjectModel.Tables;

namespace Workflow.Extensions
{
    public static class TableExtensions
    {
        /// <summary>
        /// Applies a global style to a table, setting border colors, widths, and cell padding.
        /// </summary>
        public static void ApplyGlobalTableFormatting(this Table table)
        {
            table.Borders.Width = 1;
            table.Format.Alignment = ParagraphAlignment.Center;
            table.Rows.Alignment = RowAlignment.Center;

            table.SetEdge(0, 0, table.Columns.Count, table.Rows.Count,
                Edge.Box | Edge.Interior,
                BorderStyle.Single, Unit.Zero, Color.FromRgb(229, 233, 239));

            table.BottomPadding = 0;
            table.TopPadding = 0;
            table.LeftPadding = 0;
            table.RightPadding = 0;

            table.Borders.Color = Color.FromRgb(229, 233, 239);
        }
    }
}
