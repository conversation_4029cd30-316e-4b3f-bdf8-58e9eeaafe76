using System.Text.RegularExpressions;
using IxMilia.Dxf.Entities;
using Slide.Core.Enums;

namespace Workflow.Extensions;

public static class DxfEntityExtensions
{
    /// <summary>
    /// Determines the polyline options based on the layer name of the given DXF entity.
    /// </summary>
    /// <param name="firstEntity">The DXF entity to evaluate.</param>
    /// <returns>
    /// A tuple containing two <see cref="PolyType"/> values:
    /// <list type="bullet">
    /// <item><description>The first value represents the left point option.</description></item>
    /// <item><description>The second value represents the right point option.</description></item>
    /// </list>
    /// </returns>
    /// <remarks>
    /// The method uses a regular expression to parse the layer name of the DXF entity.
    /// If the layer name matches the expected format, the polyline options are determined
    /// based on whether the parsed values contain the word "end".
    /// </remarks>
    public static (PolyType leftPointOption, PolyType rightPointOption) DeterminePolylineOptions(
        this DxfEntity firstEntity)
    {
        var leftPointOption = PolyType.PolyRandom;
        var rightPointOption = PolyType.PolyRandom;

        if (!string.IsNullOrEmpty(firstEntity?.Layer))
        {
            var match = Regex.Match(firstEntity.Layer,
                @"BLOCK[_\s-]SEARCH[_\s-]POLYLINE[_\s-](\w+[_\s-]\w+[_\s-]\w+)[_\s-](\w+[_\s-]\w+[_\s-]\w+)",
                RegexOptions.IgnoreCase);
            if (match.Success)
            {
                leftPointOption =
                    match.Groups[1].Value.Contains("end",
                        StringComparison.OrdinalIgnoreCase)
                        ? PolyType.PolyEndpoints
                        : PolyType.PolyRandomStart;
                rightPointOption =
                    match.Groups[2].Value.Contains("end",
                        StringComparison.OrdinalIgnoreCase)
                        ? PolyType.PolyEndpoints
                        : PolyType.PolyRandomStart;
            }
        }

        return (leftPointOption, rightPointOption);
    }
}