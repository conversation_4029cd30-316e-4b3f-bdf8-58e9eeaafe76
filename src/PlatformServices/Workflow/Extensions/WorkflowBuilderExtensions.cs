using Elsa.Builders;
using Elsa.Services.Models;

namespace Elsa.Activities.Workflows
{
    public static class WorkflowBuilderExtensions
    {
        public static IActivityBuilder RunWorkflow<T>(
            this IBuilder builder,
            RunWorkflow.RunWorkflowMode mode,
            Func<ActivityExecutionContext,
            object> input,
            string correlationId) where T : IWorkflow =>
            builder.RunWorkflow(activity => activity.WithWorkflow<T>().WithMode(mode).WithInput(input).WithCorrelationId(correlationId));
    }
}
