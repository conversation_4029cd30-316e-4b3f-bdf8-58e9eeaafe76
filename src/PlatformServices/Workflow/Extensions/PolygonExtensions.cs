using Slide.Core.Objects.Sli;
using TriangleNet.Geometry;

namespace Workflow.Extensions;

public static class PolygonExtensions
{
    public static Polygon AddExternal(
        this Polygon polygon,
        IEnumerable<Vertice> externalVertices)
    {
        var contour = new Contour(
            externalVertices.Select(x => new Vertex(x.X, x.Y, 1)),
            1);

        polygon.Add(contour);

        return polygon;
    }

    public static Polygon AddMaterials(
        this Polygon polygon,
        IEnumerable<(Vertice, int)> materials)
    {
        materials
            .GroupBy(x => x.Item2)
            .SelectMany(group => group.Take(group.Count() - 1)
                .Zip(
                    group.Skip(1),
                    (element1, element2) => new
                    {
                        Element1 = element1.Item1, Element2 = element2.Item1
                    })
                .Where(data => data.Element1 != null || data.Element2 != null)
                .Select(data => new
                {
                    Vertices = new[] { data.Element1, data.Element2 }
                        .Where(el => el != null)
                        .Select(el => new Vertex(el.X, el.Y))
                        .ToArray()
                }))
            .ToList()
            .ForEach(data =>
            {
                if (data.Vertices.Length > 1)
                {
                    polygon.Add(
                        new Segment(data.Vertices[0], data.Vertices[1], 1),
                        1);
                }

                foreach (Vertex vertex in data.Vertices)
                {
                    polygon.Points.Add(vertex);
                }
            });

        return polygon;
    }
}