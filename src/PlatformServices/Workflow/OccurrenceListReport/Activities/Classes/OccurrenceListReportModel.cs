using Application.Apis.Clients.Response.Occurrence;
using Application.Apis.Clients.Response.OccurrenceListReport;
using Domain.Resources;
using Microsoft.Extensions.Localization;
using System.Net.Mail;

namespace Workflow.OccurrenceListReport.Activities.Classes
{
    public sealed record OccurrenceListReportModel
    {
        public GetOccurrenceListReportByIdResponse Configuration { get; set; }
        public List<byte[]> Pdfs { get; set; }
        public List<SearchOccurrenceUnpaginatedResponse> Occurrences { get; set; }

        public MailMessage ToMailMessage(IStringLocalizer<Texts> localizer)
        {
            var message = new MailMessage();

            message.To.Add(Configuration.CreatedBy.Email);
            message.Subject = localizer
                .GetString(LocaleKeys.OccurrenceListReportEmailTitle)
                .Value;

            message.Body = string.Format(
                localizer.GetString(LocaleKeys.OccurrenceListReportEmailBody),
                Configuration.CreatedDate.ToString("dd/MM/yyyy"),
                string.Join(", ", Occurrences.Select(x => x.StructureName).Distinct()));

            foreach (var pdf in Pdfs)
            {
                var stream = new MemoryStream(pdf);
                stream.Position = 0;

                var attachment = new Attachment(stream, "application/pdf")
                {
                    Name = $"{Guid.NewGuid()}.pdf"
                };

                message.Attachments.Add(attachment);
            }

            return message;
        }
    }
}
