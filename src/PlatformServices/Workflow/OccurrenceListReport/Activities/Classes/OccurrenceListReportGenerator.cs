using Application.Apis.Clients.Request.Occurrence;
using Domain.Enums;
using Domain.Extensions;
using MigraDoc.DocumentObjectModel;
using MigraDoc.DocumentObjectModel.Shapes;
using MigraDoc.DocumentObjectModel.Tables;
using MigraDoc.Rendering;
using Workflow.Extensions;

namespace Workflow.OccurrenceListReport.Activities.Classes
{
    public sealed class OccurrenceListReportGenerator
    {
        private readonly Document _document;
        private const string _tableHeaderStyle = "TableHeader";
        private const string _tableCellStyle = "TableCell";
        private const string _headerStyle = "Header";

        public OccurrenceListReportGenerator()
        {
            _document = new Document();
            _document.Info.Title = "Relatório de listagem de ocorrências";
            DefineStyles(_document);
        }

        private void DefineStyles(Document document)
        {
            // Base style
            var style = document.Styles["Normal"];
            style.Font.Name = "Arial";
            style.Font.Size = 10;

            // Header style
            var header = document.Styles.AddStyle(_headerStyle, "Normal");
            header.Font.Size = 18;
            header.Font.Bold = true;

            // Table header style
            var tableHeader = document.Styles.AddStyle(_tableHeaderStyle, "Normal");
            tableHeader.Font.Bold = true;
            tableHeader.Font.Size = 10;
            tableHeader.Font.Color = Color.FromRgb(16, 57, 118);
            tableHeader.ParagraphFormat.Shading.Color = Color.FromRgb(229, 233, 239);
            tableHeader.ParagraphFormat.SpaceBefore = 0;
            tableHeader.ParagraphFormat.SpaceAfter = 0;

            // Table cell style
            var tableCell = document.Styles.AddStyle(_tableCellStyle, "Normal");
            tableCell.Font.Size = 9;
            tableCell.Font.Color = Color.FromRgb(16, 57, 118);
        }

        private void SetupMargins(MigraDoc.DocumentObjectModel.Section section)
        {
            section.PageSetup.TopMargin = Unit.FromCentimeter(2.5);
            section.PageSetup.BottomMargin = Unit.FromCentimeter(2.5);
            section.PageSetup.LeftMargin = Unit.FromCentimeter(2.5);
            section.PageSetup.RightMargin = Unit.FromCentimeter(2.5);
            section.PageSetup.DifferentFirstPageHeaderFooter = false;
        }

        private void SetupHeader(MigraDoc.DocumentObjectModel.Section section, OccurrencesByClient reportData)
        {
            section.PageSetup.DifferentFirstPageHeaderFooter = true;

            var headerTable = section.Headers.FirstPage.AddTable();
            headerTable.Borders.Width = 0;
            headerTable.Format.Alignment = ParagraphAlignment.Center;
            var imageColumnWidth = Unit.FromCentimeter(2.5);

            headerTable.AddColumn();
            headerTable.AddColumn(_document.DefaultPageSetup.PageWidth / 2);
            headerTable.AddColumn();

            // Add a row for header content
            var headerRow = headerTable.AddRow();
            headerRow.VerticalAlignment = VerticalAlignment.Center;

            // Left image cell
            if (!string.IsNullOrEmpty(reportData.LogisoilLogoPath))
            {
                var leftImg = headerRow.Cells[0].AddImage(reportData.LogisoilLogoPath);
                leftImg.LockAspectRatio = true;
                leftImg.Width = imageColumnWidth;
            }

            // Title cell
            var titleParagraph = headerRow.Cells[1].AddParagraph("Relatório de ocorrências");
            titleParagraph.Style = _headerStyle;
            titleParagraph.Format.Alignment = ParagraphAlignment.Center;

            // Right image cell
            if (!string.IsNullOrEmpty(reportData.ClientLogoPath))
            {
                var rightImg = headerRow.Cells[2].AddImage(reportData.ClientLogoPath);
                rightImg.LockAspectRatio = true;
                rightImg.Width = imageColumnWidth;
            }

            section.AddParagraph().Format.SpaceAfter = Unit.FromCentimeter(1);
        }

        private void SetupDetailsTable(MigraDoc.DocumentObjectModel.Section section, OccurrencesByClient reportData, SearchOccurrenceUnpaginatedRequest query)
        {
            var columnsData = new List<KeyValuePair<string, string>>();

            if (query.StartDate.HasValue || query.EndDate.HasValue)
            {
                var periodo = "";
                if (query.StartDate.HasValue)
                    periodo = "De " + query.StartDate.Value.ToString("dd/MM/yyyy");
                if (query.EndDate.HasValue)
                    periodo += (query.StartDate.HasValue ? " até " : "") + query.EndDate.Value.ToString("dd/MM/yyyy");

                columnsData.Add(new KeyValuePair<string, string>("Período", periodo));
            }

            if (query.Status.HasValue)
            {
                var status = query.Status.Value == OccurrenceStatus.Completed ? "Concluído" : "Pendente";
                columnsData.Add(new KeyValuePair<string, string>("Status", status));
            }

            if (query.DaysRemaining.HasValue)
            {
                columnsData.Add(new KeyValuePair<string, string>("Prazo", $"{query.DaysRemaining.Value} dias"));
            }

            if (query.InspectionSheetType.HasValue)
            {
                columnsData.Add(new KeyValuePair<string, string>("Tipo de Ficha", query.InspectionSheetType.Value.ToString()));
            }

            if (query.WithActionPlan.HasValue)
            {
                columnsData.Add(new KeyValuePair<string, string>("Com Plano de Ação", query.WithActionPlan.Value ? "Sim" : "Não"));
            }

            if (query.DateFilter.HasValue)
            {
                var filtro = query.DateFilter.Value == OccurrenceDateFilter.DeadlineDate ? "Data de prazo" :
                                query.DateFilter.Value == OccurrenceDateFilter.DaysRemainingInDeadline ? "Dias restantes de prazo" :
                                query.DateFilter.Value == OccurrenceDateFilter.InspectionSheetDate ? "Data da inspeção" :
                                query.DateFilter.Value.ToString();
                columnsData.Add(new KeyValuePair<string, string>("Filtro de Data", filtro));
            }

            if (reportData.Structures.Any())
            {
                columnsData.Add(new KeyValuePair<string, string>("Estruturas", string.Join(", ", reportData.Structures.Select(x => x.StructureName))));
            }

            if (columnsData.Count == 0)
            {
                columnsData.Add(new KeyValuePair<string, string>("Filtros", "Nenhum filtro selecionado"));
            }

            var detailsTable = section.AddTable();
            detailsTable.ApplyGlobalTableFormatting();

            foreach (var col in columnsData)
            {
                var column = detailsTable.AddColumn(Unit.FromCentimeter(3));
                column.Format.Alignment = ParagraphAlignment.Center;
            }

            var detailsHeader = detailsTable.AddRow();
            detailsHeader.Style = _tableHeaderStyle;
            detailsHeader.Shading.Color = Color.FromRgb(229, 233, 239);
            detailsHeader.Cells[0].MergeRight = columnsData.Count - 1;
            detailsHeader.Cells[0].AddParagraph("Detalhes do relatório").Format.Alignment = ParagraphAlignment.Center;

            var subHeader = detailsTable.AddRow();
            subHeader.Style = _tableCellStyle;
            for (int i = 0; i < columnsData.Count; i++)
            {
                subHeader.Cells[i].AddParagraph(columnsData[i].Key);
                subHeader.Cells[i].Format.Alignment = ParagraphAlignment.Center;
                subHeader.Cells[i].Format.Font.Bold = true;
            }

            var detailData = detailsTable.AddRow();
            detailData.Style = _tableCellStyle;
            for (int i = 0; i < columnsData.Count; i++)
            {
                detailData.Cells[i].AddParagraph(columnsData[i].Value);
                detailData.Cells[i].Format.Alignment = ParagraphAlignment.Center;
            }

            section.AddParagraph().Format.SpaceAfter = Unit.FromCentimeter(1.5);
        }

        public byte[] Generate(OccurrencesByClient reportData, SearchOccurrenceUnpaginatedRequest query)
        {
            var section = _document.AddSection();

            SetupMargins(section);
            SetupHeader(section, reportData);
            SetupDetailsTable(section, reportData, query);

            // --- Loop through Structures ---
            foreach (var item in reportData.Structures.Select((structure, idx) => new { structure, idx }))
            {
                var structure = item.structure;
                var idx = item.idx;

                var structureSection = _document.LastSection;
                structureSection.PageSetup = section.PageSetup.Clone();

                if (idx != 0)
                {
                    structureSection = _document.AddSection();
                }

                var structTitle = structureSection.AddParagraph(structure.StructureName);
                structTitle.Format.Font.Size = 14;
                structTitle.Format.Font.Bold = true;
                structTitle.Format.SpaceBefore = Unit.FromCentimeter(1);
                structTitle.Format.SpaceAfter = Unit.FromCentimeter(0.5);
                structTitle.Format.Alignment = ParagraphAlignment.Center;

                // Structure image, centered
                if (!string.IsNullOrEmpty(structure.StructureImagePath))
                {
                    var structureImg = structureSection.AddImage(structure.StructureImagePath);
                    structureImg.Width = Unit.FromPoint(500);
                    structureImg.LockAspectRatio = true;
                    structureImg.Left = ShapePosition.Center;
                }

                structureSection.AddParagraph().Format.SpaceAfter = Unit.FromCentimeter(0.5);

                // --- Create Caption Table ---
                var captionTable = structureSection.AddTable();
                captionTable.ApplyGlobalTableFormatting();

                captionTable.AddColumn(Unit.FromCentimeter(3));
                captionTable.AddColumn(Unit.FromCentimeter(3));
                captionTable.AddColumn(Unit.FromCentimeter(5));
                captionTable.AddColumn(Unit.FromCentimeter(5));

                // Header Row: Merged cell with the text "Caption"
                var captionHeaderRow = captionTable.AddRow();
                captionHeaderRow.Style = _tableHeaderStyle;
                captionHeaderRow.Shading.Color = Color.FromRgb(229, 233, 239);
                captionHeaderRow.Cells[0].MergeRight = 3;
                var headerParagraph = captionHeaderRow.Cells[0].AddParagraph("Legenda");
                headerParagraph.Format.Alignment = ParagraphAlignment.Center;

                // Detail Row: Information about the status indicator circles.
                var captionRow = captionTable.AddRow();
                captionRow.Style = _tableCellStyle;

                // Cell 1: Gray circle for Completed
                var completedParagraph = captionRow.Cells[0].AddParagraph();
                var completedCircle = completedParagraph.AddFormattedText("● ", TextFormat.NotBold);
                completedCircle.Font.Color = Colors.Gray;
                completedParagraph.AddText("Concluído");

                // Cell 2: Red circle for Late
                var latePara = captionRow.Cells[1].AddParagraph();
                var lateCircle = latePara.AddFormattedText("● ", TextFormat.NotBold);
                lateCircle.Font.Color = Colors.Red;
                latePara.AddText("Atrasado");

                // Cell 3: Dark green circle for "Remaining deadline > 3 days"
                var remPara = captionRow.Cells[2].AddParagraph();
                var remCircle = remPara.AddFormattedText("● ", TextFormat.NotBold);
                remCircle.Font.Color = Colors.DarkGreen;
                remPara.AddText(" Prazo restante > 3 dias");

                // Cell 4: Light green circle for "Remaining deadline ≤ 3 days"
                var rem2Para = captionRow.Cells[3].AddParagraph();
                var rem2Circle = rem2Para.AddFormattedText("● ", TextFormat.NotBold);
                rem2Circle.Font.Color = Colors.LightGreen;
                rem2Para.AddText("Prazo restante ≤ 3 dias");

                structureSection.AddParagraph().Format.SpaceAfter = Unit.FromCentimeter(1.5);

                // --- Occurrence Table for This Structure ---
                var occTable = structureSection.AddTable();
                occTable.ApplyGlobalTableFormatting();

                occTable.AddColumn(Unit.FromCentimeter(1)); // Id
                occTable.AddColumn(Unit.FromCentimeter(3)); // Status
                occTable.AddColumn(Unit.FromCentimeter(3));   // Descrição
                occTable.AddColumn(Unit.FromCentimeter(1.5)); // Ficha
                occTable.AddColumn(Unit.FromCentimeter(2)); // Data abertura
                occTable.AddColumn(Unit.FromCentimeter(2.5));   // Coord. (SIRGAS 2000)
                occTable.AddColumn(Unit.FromCentimeter(2)); // Prazo
                occTable.AddColumn(Unit.FromCentimeter(2));   // Data conclusão

                // Occurrence table header row
                var occHeader = occTable.AddRow();
                occHeader.Style = _tableHeaderStyle;
                occHeader.Shading.Color = Color.FromRgb(229, 233, 239);
                occHeader.Cells[0].AddParagraph("Id");
                occHeader.Cells[1].AddParagraph("Status");
                occHeader.Cells[2].AddParagraph("Descrição");
                occHeader.Cells[3].AddParagraph("Ficha");
                occHeader.Cells[4].AddParagraph("Data abertura");
                occHeader.Cells[5].AddParagraph("Coordenadas");
                occHeader.Cells[6].AddParagraph("Prazo");
                occHeader.Cells[7].AddParagraph("Data conclusão");

                // Data rows for each occurrence
                foreach (var occ in structure.Occurrences)
                {
                    var occRow = occTable.AddRow();
                    occRow.Style = _tableCellStyle;
                    occRow.Cells[0].AddParagraph(occ.SearchIdentifier.ToString());
                    occRow.Cells[1].AddParagraph(occ.OccurrenceAndActionPlanStatus.GetDescription());
                    occRow.Cells[2].AddParagraph(occ.Aspect.Description);
                    occRow.Cells[3].AddParagraph(occ.InspectionSheetSearchIdentifier.ToString());
                    occRow.Cells[4].AddParagraph(occ.CreatedDate.ToString("dd/MM/yyyy"));
                    occRow.Cells[5].AddParagraph(
                        occ.Easting.HasValue && occ.Northing.HasValue
                        ? $"E (m): {occ.Easting.Value:F4},\nN (m): {occ.Northing.Value:F4}"
                        : "");
                    occRow.Cells[6].AddParagraph(occ.ActionPlanExpirationDate.HasValue ? $"{occ.ActionPlanExpirationDate.Value.ToString("dd/MM/yyyy")}" : "");
                    occRow.Cells[7].AddParagraph(occ.ActionPlanCompletionDate.HasValue ? $"{occ.ActionPlanCompletionDate.Value.ToString("dd/MM/yyyy")}" : "");
                }

                occTable.Rows[0].HeadingFormat = true;

                structureSection.AddParagraph().Format.SpaceAfter = Unit.FromCentimeter(1.5);
            }

            var renderer = new PdfDocumentRenderer()
            {
                Document = _document
            };

            renderer.RenderDocument();

            using (var ms = new MemoryStream())
            {
                renderer.PdfDocument.Save(ms);

                return ms.ToArray();
            }
        }
    }
}
