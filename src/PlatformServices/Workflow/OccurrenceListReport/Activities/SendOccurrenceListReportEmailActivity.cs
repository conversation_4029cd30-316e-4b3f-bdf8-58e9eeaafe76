using Application.Email;
using Domain.Enums;
using Domain.Resources;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Microsoft.Extensions.Localization;
using Workflow.Extensions;
using Workflow.OccurrenceListReport.Activities.Classes;
using static Workflow.Constants.OccurrenceListReportWorkflow;
using Activity = Elsa.Services.Activity;

namespace Workflow.OccurrenceListReport.Activities
{
    public class SendOccurrenceListReportEmailActivity : Activity
    {
        private readonly IEmailService _emailService;
        private readonly IStringLocalizer<Texts> _localizer;

        public SendOccurrenceListReportEmailActivity(IEmailService emailService, IStringLocalizer<Texts> localizer)
        {
            _emailService = emailService;
            _localizer = localizer;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                Thread.CurrentThread.ChangeCulture(Locale.PtBr);

                var model = context.GetTransientVariable<OccurrenceListReportModel>(Variables.OccurrenceListReportModel);

                var message = model.ToMailMessage(_localizer);

                await _emailService.Send(message);

                return Done();
            }
            catch (Exception ex)
            {
                return Fault(ex);
            }
        }
    }
}
