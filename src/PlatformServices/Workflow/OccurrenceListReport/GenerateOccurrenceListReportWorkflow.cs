using Elsa.Activities.ControlFlow;
using Elsa.Activities.MassTransit;
using Elsa.Activities.Primitives;
using Elsa.Builders;
using Workflow.OccurrenceListReport.Activities;
using static Workflow.Constants.OccurrenceListReportWorkflow;

namespace Workflow.OccurrenceListReport;

public class GenerateOccurrenceListReportWorkflow : IWorkflow
{
    public void Build(IWorkflowBuilder builder)
    {
        builder
            .WithDeleteCompletedInstances(true)
            .WithDisplayName("Create occurrence list report")
            .ReceiveMassTransitMessage(
                activity => activity.Set(x => x.MessageType, x => typeof(Domain.Messages.Events.OccurrenceListReport.OccurrenceListReportCreated)))
            .WithDisplayName("Receive event occurrence list report created")
            .SetTransientVariable(Variables.Command, context => context.GetInput<Domain.Messages.Events.OccurrenceListReport.OccurrenceListReportCreated>())
            .Then<CreateOccurrenceListReportActivity>()
            .Then<SendOccurrenceListReportEmailActivity>()
            .Finish();
    }
}