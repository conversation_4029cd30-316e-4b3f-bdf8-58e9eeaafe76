using Application.Apis.Clients;
using Domain.Enums;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Serilog;
using static Workflow.Constants.StabilityAnalysisWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.StabilityAnalysis.Activities
{
    public class CreateStabilityAnalysisErrorNotificationActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public CreateStabilityAnalysisErrorNotificationActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var errorMessage = context.GetTransientVariable<string>(Variables.ErrorMessage);

                if (string.IsNullOrEmpty(errorMessage))
                {
                    return Done();
                }

                var structureId = context.GetTransientVariable<Guid>(Variables.StructureId);

                if (structureId == Guid.Empty)
                {
                    return Done();
                }

                var users = await _clientsApiService.GetUsersAsync(new()
                {
                    StructureId = structureId,
                    Active = true,
                    Roles = new()
                    {
                        (int)Role.SuperSupport,
                        (int)Role.Support,
                        (int)Role.SuperAdministrator,
                        (int)Role.Administrator,
                        (int)Role.Operator,
                        (int)Role.Auditor
                    }
                });

                if (users == null || !users.Any())
                {
                    return Done();
                }

                var result = await _clientsApiService.AddNotification(new()
                {
                    Users = users.Select(x => x.Id).ToList(),
                    NotificationMessage = errorMessage,
                    NotificationTheme = NotificationTheme.StabilityAnalysis
                });

                if (result == null || !result.IsSuccessStatusCode)
                {
                    Log.Error("Error creating notification: {0}", errorMessage);
                    return Fault("Error creating notification");
                }

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Error in CreateStabilityAnalysisErrorNotificationActivity");
                return Fault(e);
            }
        }
    }
}
