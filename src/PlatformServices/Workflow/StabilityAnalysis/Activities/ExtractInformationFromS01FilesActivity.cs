using System.Text;
using System.Text.RegularExpressions;
using Domain.Enums;
using Domain.Extensions;
using Dxf.Core.Classes;
using Dxf.Core.Extensions;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Geometry.Core;
using IxMilia.Converters;
using IxMilia.Dxf;
using IxMilia.Dxf.Blocks;
using IxMilia.Dxf.Entities;
using Serilog;
using SkiaSharp;
using Slide.Core.Enums;
using Svg.Skia;
using Workflow.StabilityAnalysis.Classes;
using static Workflow.Constants.StabilityAnalysisWorkFlow;
using static Workflow.StabilityAnalysis.Constants;
using Activity = Elsa.Services.Activity;

namespace Workflow.StabilityAnalysis.Activities
{
    public class ExtractInformationFromS01FilesActivity : Activity
    {
        const long MaxBitmapMemoryThreshold100MB = 100 * 1024 * 1024;
        const double DesiredMinPx = 2048;

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var sliFiles = context.GetTransientVariable<List<SliDetailAggregator>>(Variables.SliFiles);

                foreach (var sliFile in sliFiles)
                {
                    if (!string.IsNullOrEmpty(sliFile.S01))
                    {
                        sliFile.Values = await ParseValues(sliFile);
                    }
                }

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Error in ExtractInformationFromS01FileActivity");
                context.SetTransientVariable(Variables.ErrorMessage, $"Houve um problema ao obter os resultados das análises de estabilidade: {e.Message}");
                return Outcome(OutcomeNames.Cancel);
            }
        }

        private async Task<List<SafetyFactorValue>> ParseValues(SliDetailAggregator sliFile)
        {
            var results = new List<SafetyFactorValue>();

            var startPattern = "* Global Minimum FS (xc,yc,r,x1,y1,x2,y2,fs,name)";
            var endPattern = "* Global Minimum Text";

            var start = sliFile.S01.IndexOf(startPattern);
            var end = sliFile.S01.IndexOf(endPattern);

            if (start == -1 || end == -1)
            {
                throw new Exception("Could not find start or end pattern");
            }

            start += startPattern.Length;

            var sub = sliFile.S01.Substring(start, end - start).Trim();
            var lines = sub.Split('\n');

            foreach (var line in lines)
            {
                var match = Regex.Match(line, @"(?<xc>\d+.\d+)\s(?<yc>\d+.\d+)\s(?<r>\d+.\d+)\s(?<x1>\d+.\d+)\s(?<y1>\d+.\d+)\s(?<x2>\d+.\d+)\s(?<y2>\d+.\d+)\s(?<fs>\d+(\.\d+)?)\s?(?<calculationMethod>.*)\r?$", RegexOptions.IgnoreCase);

                if (!match.Success) 
                {
                    match = Regex.Match(line, @"(?<xc>\d+)\s(?<yc>\d+)\s(?<r>\d+)\s(?<x1>\d+)\s(?<y1>\d+)\s(?<x2>\d+)\s(?<y2>\d+)\s(?<fs>-?\d+)(?:\s(?<calculationMethod>.*))?", RegexOptions.IgnoreCase);
                }

                if (match.Success)
                {
                    var globalMinimumFs = new GlobalMinimumFs
                    {
                        Xc = double.Parse(match.Groups["xc"].Value),
                        Yc = double.Parse(match.Groups["yc"].Value),
                        R = double.Parse(match.Groups["r"].Value),
                        X1 = double.Parse(match.Groups["x1"].Value),
                        Y1 = double.Parse(match.Groups["y1"].Value),
                        X2 = double.Parse(match.Groups["x2"].Value),
                        Y2 = double.Parse(match.Groups["y2"].Value),
                        Fs = Math.Round(double.Parse(match.Groups["fs"].Value), 2),
                        CalculationMethod = match.Groups["calculationMethod"].Value.Replace("\r", "").Replace("\n", "")
                    };

                    CalculationMethod method;

                    switch (globalMinimumFs.CalculationMethod.ToLower())
                    {
                        case "ordinary/fellenius":
                            method = CalculationMethod.OrdinaryOrFellenius;
                            break;
                        case "bishop simplified":
                            method = CalculationMethod.BishopSimplified;
                            break;
                        case "janbu simplified":
                            method = CalculationMethod.JanbuSimplified;
                            break;
                        case "spencer":
                            method = CalculationMethod.Spencer;
                            break;
                        case "corp of eng#1":
                            method = CalculationMethod.CorpsOfEngineers1;
                            break;
                        case "corp of eng#2":
                            method = CalculationMethod.CorpsOfEngineers2;
                            break;
                        case "lowe-karafiath":
                            method = CalculationMethod.LoweKarafiath;
                            break;
                        case "gle/morgenstern-price":
                            method = CalculationMethod.GLEOrMorgensternPrice;
                            break;
                        case "janbu corrected":
                            method = CalculationMethod.JanbuCorrected;
                            break;
                        case "sarma":
                            method = CalculationMethod.Sarma;
                            break;
                        default:
                            continue;
                    }

                    var images = await CreateImages(sliFile, globalMinimumFs);

                    results.Add(new SafetyFactorValue
                    {
                        CalculationMethod = method,
                        Value = globalMinimumFs.Fs,
                        DxfBase64 = images.Item1,
                        PngBase64 = images.Item2,
                        GlobalMinimumFs = globalMinimumFs
                    });
                }
            }

            return results;
        }

        private async Task<(string, string)> CreateImages(SliDetailAggregator sliFile, GlobalMinimumFs globalMinimumFs)
        {
            var newDxf = new DxfFile();
            newDxf.Header.Version = DxfAcadVersion.R2018;
            newDxf.Header.CreationDate = DateTime.UtcNow;

            var externalEntities = sliFile.DxfFile.Entities.Where(x => x.Layer.ToLower() == "external");

            foreach (var entity in externalEntities)
            {
                newDxf.Entities.Add(entity);
            }

            var materialEntities = sliFile.DxfFile.Entities.Where(x => x.Layer.ToLower() == "material");

            foreach (var entity in materialEntities)
            {
                newDxf.Entities.Add(entity);
            }
            var solids = new List<DxfSolid>();

            foreach (var triangle in sliFile.SliFile.Cells)
            {
                var vertice1 = sliFile.SliFile.Vertices.First(x => x.Index == triangle.Vertice1);
                var vertice2 = sliFile.SliFile.Vertices.First(x => x.Index == triangle.Vertice2);
                var vertice3 = sliFile.SliFile.Vertices.First(x => x.Index == triangle.Vertice3);

                var materialIndex = int.Parse(Regex.Match(triangle.Material, @"\d+").Value);
                var materialProperties = sliFile.SliFile.MaterialProperties[materialIndex - 1];

                if (newDxf.Layers.All(x => x.Name != $" {materialProperties.Name}"))
                {
                    newDxf.Layers.Add(new DxfLayer($" {materialProperties.Name}", DxfFileExtensions.FromRgb(materialProperties.Red, materialProperties.Green, materialProperties.Blue)));
                }

                var solid = new DxfSolid
                {
                    Color = DxfFileExtensions.FromRgb(materialProperties.Red, materialProperties.Green, materialProperties.Blue),
                    Layer = $" {materialProperties.Name}",
                    FirstCorner = new DxfPoint(vertice1.X, vertice1.Y, 0),
                    SecondCorner = new DxfPoint(vertice2.X, vertice2.Y, 0),
                    ThirdCorner = new DxfPoint(vertice3.X, vertice3.Y, 0),
                    FourthCorner = new DxfPoint(vertice1.X, vertice1.Y, 0),
                    ExtrusionDirection = new DxfVector(0, 0, 1)
                };

                solids.Add(solid);
            }

            var solidsGrouped = solids.GroupBy(x => x.Layer);

            foreach (var group in solidsGrouped)
            {
                var block = new DxfBlock()
                {
                    Name = group.Key,
                    IsAnonymous = true,
                    BasePoint = new DxfPoint(0, 0, 0)
                };

                foreach (var solid in group)
                {
                    block.Entities.Add(solid);
                }

                newDxf.Blocks.Add(block);
                newDxf.BlockRecords.Add(new DxfBlockRecord(block.Name));

                var insert = new DxfInsert
                {
                    Layer = group.Key,
                    Name = group.Key,
                    Location = new DxfPoint(0, 0, 0)
                };

                newDxf.Entities.Add(insert);
            }

            var allPoints = newDxf.GetAllPoints();

            var maxPoint = new PointD(allPoints.Max(x => x.X), allPoints.Max(x => x.Y));

            var scaleFactor = Math.Min(maxPoint.X, maxPoint.Y) / 100.0;
            if (scaleFactor <= 0)
            {
                scaleFactor = 1.0;
            }

            var textHeight = 1.5 * scaleFactor;

            #region Create the piezos and water table

            if (sliFile.SliFile.Piezos.Any())
            {
                newDxf.Layers.Add(new DxfLayer(Piezometric, DxfColor.FromIndex(143)));
            }

            foreach (var piezo in sliFile.SliFile.Piezos)
            {
                var vertices = piezo.Vertices.Select(x => sliFile.SliFile.Vertices.First(y => y.Index == x)).ToList();
                var dxfVertexes = vertices.Select(x => new DxfVertex() { Location = new(x.X, x.Y, 0) }).ToList();

                var polyline = new DxfPolyline(dxfVertexes)
                {
                    Layer = Piezometric,
                    IsClosed = false,
                    Color = DxfColor.FromIndex(143)
                };

                newDxf.Entities.Add(polyline);

                var piezoText = new DxfText
                {
                    Layer = Piezometric,
                    Color = DxfColor.FromIndex(143),
                    Value = Piezometric,
                    TextHeight = textHeight,
                    Location = new DxfPoint(vertices.First().X, vertices.First().Y, 0)
                };

                newDxf.Entities.Add(piezoText);
            }

            if (sliFile.SliFile.WaterTable.Vertices.Any())
            {
                var waterTableVertices = sliFile.SliFile.WaterTable.Vertices.Select(x => sliFile.SliFile.Vertices.First(y => y.Index == x)).ToList();
                var waterTableDxfVertexes = waterTableVertices.Select(x => new DxfVertex() { Location = new(x.X, x.Y, 0) }).ToList();

                newDxf.Layers.Add(new DxfLayer(Freatic, DxfColor.FromIndex(5)));

                var waterTablePolyline = new DxfPolyline(waterTableDxfVertexes)
                {
                    Layer = Freatic,
                    IsClosed = false,
                    Color = DxfColor.FromIndex(5)
                };

                newDxf.Entities.Add(waterTablePolyline);

                var waterTableText = new DxfText
                {
                    Layer = Freatic,
                    Color = DxfColor.FromIndex(5),
                    Value = Freatic,
                    TextHeight = textHeight,
                    Location = new DxfPoint(waterTableDxfVertexes.First().Location.X, waterTableDxfVertexes.First().Location.Y, 0)
                };

                newDxf.Entities.Add(waterTableText);
            }

            #endregion

            #region Create the instruments

            foreach (var tool in sliFile.SltmFile.ToolsData.Tools)
            {
                if (tool.ToolType == ToolType.TextBoxes)
                {
                    var color = tool.FillColor;

                    var instrumentIdentifier = new DxfText
                    {
                        Layer = Instruments,
                        Color = DxfFileExtensions.FromRgb(color.R, color.G, color.B),
                        Value = tool.Text1,
                        TextHeight = textHeight,
                        Location = new DxfPoint(tool.Start.X, tool.Start.Y, 0),
                        ShadowMode = DxfShadowMode.ReceivesShadows
                    };

                    newDxf.Entities.Add(instrumentIdentifier);

                    var line = new DxfPolyline(new List<DxfVertex>
                    {
                        new DxfVertex(new DxfPoint(tool.Start.X, tool.Start.Y, 0)),
                        new DxfVertex(new DxfPoint(tool.Start.X + tool.Text1.Length * scaleFactor, tool.Start.Y, 0)),
                    })
                    {
                        Layer = Instruments,
                        Color = DxfFileExtensions.FromRgb(color.R, color.G, color.B),
                        IsClosed = false
                    };

                    newDxf.Entities.Add(line);
                }

                if (tool.ToolType == ToolType.Polylines)
                {
                    var vertices = tool.VerticesStart.DPoints.Select(x => new DxfVertex() { Location = new(x.X, x.Y, 0) }).ToList();
                    var color = tool.FillColor;

                    var polyline = new DxfPolyline(vertices)
                    {
                        Layer = Instruments,
                        Color = DxfFileExtensions.FromRgb(color.R, color.G, color.B),
                        LineweightEnumValue = DxfLineWeight.ByLayer.Value,
                        Thickness = 10
                    };

                    newDxf.Entities.Add(polyline);
                }
            }

            #endregion

            #region Create quota text

            var mergedInstruments = sliFile.Instruments.WaterLevelIndicators.Concat(sliFile.Instruments.Piezometers).ToList();

            foreach (var instrument in mergedInstruments)
            {
                var yString = Math.Round(instrument.PointD.Y, 2).ToString();
                var textWidth = yString.Length * 1.5;

                var instrumentIdentifier = new DxfText
                {
                    Layer = "Cota",
                    Value = yString,
                    TextHeight = textHeight,
                    Location = new DxfPoint(instrument.PointD.X, instrument.PointD.Y, 0),
                    ShadowMode = DxfShadowMode.ReceivesShadows,
                };

                var waterTableVertices = sliFile.SliFile.WaterTable.Vertices.Select(x => sliFile.SliFile.Vertices.First(y => y.Index == x)).ToList();
                var waterTableDxfVertexes = waterTableVertices.Select(x => new PointD(x.X, x.Y)).ToList();

                if (waterTableDxfVertexes.Any(z => z == instrument.PointD))
                {
                    newDxf.Entities.Add(instrumentIdentifier);
                    continue;
                }

                if (sliFile.SliFile.Piezos.Any())
                {
                    foreach (var piezo in sliFile.SliFile.Piezos)
                    {
                        var vertices = piezo.Vertices.Select(x => sliFile.SliFile.Vertices.First(y => y.Index == x)).ToList();
                        var dxfVertexes = vertices.Select(x => new PointD(x.X, x.Y)).ToList();

                        if (dxfVertexes.Any(z => z == instrument.PointD))
                        {
                            newDxf.Entities.Add(instrumentIdentifier);
                            continue;
                        }
                    }
                }
            }

            #endregion

            #region Create the rupture wedge

            newDxf.Layers.Add(new DxfLayer(RuptureWedge, DxfColor.FromIndex(94)));
            var radius = Math.Sqrt(Math.Pow(globalMinimumFs.X1 - globalMinimumFs.Xc, 2) + Math.Pow(globalMinimumFs.Y1 - globalMinimumFs.Yc, 2));

            var arc = new DxfArc
            {
                Layer = RuptureWedge,
                Color = DxfColor.FromIndex(94),
                Center = new DxfPoint(globalMinimumFs.Xc, globalMinimumFs.Yc, 0),
                Radius = radius,
                StartAngle = Math.Atan2(globalMinimumFs.Y1 - globalMinimumFs.Yc, globalMinimumFs.X1 - globalMinimumFs.Xc) * 180 / Math.PI,
                EndAngle = Math.Atan2(globalMinimumFs.Y2 - globalMinimumFs.Yc, globalMinimumFs.X2 - globalMinimumFs.Xc) * 180 / Math.PI
            };

            newDxf.Entities.Add(arc);

            var line1 = new DxfLine
            {
                Layer = RuptureWedge,
                Color = DxfColor.FromIndex(94),
                P1 = new DxfPoint(globalMinimumFs.Xc, globalMinimumFs.Yc, 0),
                P2 = new DxfPoint(globalMinimumFs.X1, globalMinimumFs.Y1, 0)
            };

            var line2 = new DxfLine
            {
                Layer = RuptureWedge,
                Color = DxfColor.FromIndex(94),
                P1 = new DxfPoint(globalMinimumFs.Xc, globalMinimumFs.Yc, 0),
                P2 = new DxfPoint(globalMinimumFs.X2, globalMinimumFs.Y2, 0)
            };

            newDxf.Entities.Add(line1);
            newDxf.Entities.Add(line2);

            var text = new DxfText
            {
                Layer = RuptureWedge,
                Color = DxfColor.FromIndex(94),
                Value = globalMinimumFs.Fs.ToString(),
                TextHeight = textHeight,
                Location = new DxfPoint(globalMinimumFs.Xc, globalMinimumFs.Yc, 0)
            };

            newDxf.Entities.Add(text);

            #endregion

            #region Create a table in the dxf file with the material properties

            var table = new DxfTable(newDxf, 9, sliFile.SliFile.MaterialTypes.Count + 1, "Tabela de materiais");

            table.Insert(0, 0, "Material");
            table.Insert(1, 0, "Cor");
            table.Insert(2, 0, "Peso específico (kN/m3)");
            table.Insert(3, 0, "Modelo constitutivo");
            table.Insert(4, 0, "Coesão (kPa)");
            table.Insert(5, 0, "Ângulo de atrito (º)");
            table.Insert(6, 0, "Superfície da água");
            table.Insert(7, 0, "Tipo Hu");
            table.Insert(8, 0, "Hu");

            var rowIndex = 1;

            var referentialMaterialIndexes = sliFile.SliFile.MaterialTypes
                .Where(material => material.UseAlternateStrength)
                .Select(material => material.AlternateMaterialIndex);

            var referentialMaterials = referentialMaterialIndexes
                .Select(index => sliFile.SliFile.MaterialTypes.ElementAtOrDefault(index))
                .ToList();

            foreach (var materialType in sliFile.SliFile.MaterialTypes)
            {
                var materialProperties = sliFile.SliFile.MaterialProperties[materialType.Index - 1];
                
                if (sliFile.SliFileType.MapSoilConditionType() != SoilConditionType.Drained 
                    && referentialMaterials.Any(x => x.MaterialValueId == materialType.MaterialValueId))
                {
                    materialProperties.Name += " (Drenado)";
                }

                table.Insert(0, rowIndex, materialProperties.Name);
                table.Insert(1, rowIndex, string.Empty, DxfFileExtensions.FromRgb(materialProperties.Red, materialProperties.Green, materialProperties.Blue));
                table.Insert(2, rowIndex, materialType.Uw.ToString());
                table.Insert(3, rowIndex, materialType.Type.ToString());
                table.Insert(4, rowIndex, materialType.C.ToString());
                table.Insert(5, rowIndex, materialType.Phi.ToString());
                table.Insert(6, rowIndex, materialType.WTable ? "Water Table" : "Piezo");
                table.Insert(7, rowIndex, materialType.HuType != null && (bool)materialType.HuType ? "Auto" : "Custom");
                table.Insert(8, rowIndex, materialType.Water.ToString());

                rowIndex++;
            }

            #endregion

            #region Create slope limits

            newDxf.Layers.Add(new DxfLayer(Limits, DxfColor.FromIndex(0)));

            newDxf.Entities.Add(new DxfCircle()
            {
                Layer = Limits,
                Color = DxfColor.FromIndex(0),
                Center = new DxfPoint(sliFile.SliFile.SlopeLimits.X1, sliFile.SliFile.SlopeLimits.Y1, 0),
                Radius = scaleFactor
            });

            newDxf.Entities.Add(new DxfCircle()
            {
                Layer = Limits,
                Color = DxfColor.FromIndex(0),
                Center = new DxfPoint(sliFile.SliFile.SlopeLimits.X2, sliFile.SliFile.SlopeLimits.Y2, 0),
                Radius = scaleFactor
            });

            if (sliFile.SliFile.SlopeLimits.X3.HasValue && sliFile.SliFile.SlopeLimits.Y3.HasValue)
            {
                newDxf.Entities.Add(new DxfCircle()
                {
                    Layer = Limits,
                    Color = DxfColor.FromIndex(0),
                    Center = new DxfPoint(sliFile.SliFile.SlopeLimits.X3.Value, sliFile.SliFile.SlopeLimits.Y3.Value, 0),
                    Radius = scaleFactor
                });
            }   

            if (sliFile.SliFile.SlopeLimits.X4.HasValue && sliFile.SliFile.SlopeLimits.Y4.HasValue)
            {
                newDxf.Entities.Add(new DxfCircle()
                {
                    Layer = Limits,
                    Color = DxfColor.FromIndex(0),
                    Center = new DxfPoint(sliFile.SliFile.SlopeLimits.X4.Value, sliFile.SliFile.SlopeLimits.Y4.Value, 0),
                    Radius = scaleFactor
                });
            }

            #endregion

            newDxf.Normalize();

            allPoints = newDxf.GetAllPoints();

            var pngHeight = allPoints.Max(x => x.Y) - allPoints.Min(x => x.Y);
            var pngWidth = allPoints.Max(x => x.X) - allPoints.Min(x => x.X);

            var minX = allPoints.Min(x => x.X);
            var maxX = allPoints.Max(x => x.X);
            var minY = allPoints.Min(x => x.Y);
            var maxY = allPoints.Max(x => x.Y);

            // Increase each variable with 10% of its value to make the image with some margin
            minX -= (maxX - minX) * 0.1;
            maxX += (maxX - minX) * 0.1;
            minY -= (maxY - minY) * 0.1;
            maxY += (maxY - minY) * 0.1;

            var dxfRectWidth = (maxX - minX) * 10;
            var dxfRectHeight = (maxY - minY) * 10;

            var dxfRect = new ConverterDxfRect(minX, maxX, minY, maxY);
            var svgRect = new ConverterSvgRect(dxfRectWidth, dxfRectHeight);

            var convertOptions = new DxfToSvgConverterOptions(dxfRect, svgRect);
            var converter = new DxfToSvgConverter();

            var effectiveStrokeWidth = maxPoint.X / 1000;

            var svg = await converter.Convert(newDxf, convertOptions, "white", null, effectiveStrokeWidth);
            var pngBase64 = FlattenImage(svg.ToString());

            using var ms = new MemoryStream();
            newDxf.Save(ms);

            var dxfBase64 = Convert.ToBase64String(ms.ToArray());

            return (dxfBase64, pngBase64);
        }
        
        private static string FlattenImage(string svg)
        {
            using var svgDocument = new SKSvg();
            using var svgStream = new MemoryStream(Encoding.UTF8.GetBytes(svg));
            svgDocument.Load(svgStream);

            var picture = svgDocument.Picture;
            if (picture == null)
            {
                throw new InvalidOperationException("Failed to load the SVG content.");
            }

            var cullRect = picture.CullRect;
            var intrinsicWidth = cullRect.Width;
            var intrinsicHeight = cullRect.Height;

            var scaleFactor = 1.0;
            if (intrinsicWidth < DesiredMinPx || intrinsicHeight < DesiredMinPx)
            {
                scaleFactor = Math.Max(DesiredMinPx / intrinsicWidth, DesiredMinPx / intrinsicHeight);
            }

            var width = (int)Math.Ceiling(intrinsicWidth * scaleFactor);
            var height = (int)Math.Ceiling(intrinsicHeight * scaleFactor);

            var memoryRequirement = (long)width * height * 4; // 4 bytes per pixel for ARGB
            if (memoryRequirement > MaxBitmapMemoryThreshold100MB)
            {
                var reductionFactor = Math.Sqrt((double)MaxBitmapMemoryThreshold100MB / memoryRequirement);
                width = (int)Math.Ceiling(width * reductionFactor);
                height = (int)Math.Ceiling(height * reductionFactor);
                scaleFactor *= reductionFactor;
            }

            var bitmap = new SKBitmap(width, height);
            using (var canvas = new SKCanvas(bitmap))
            {
                canvas.Clear(SKColors.White);
                canvas.Scale((float)scaleFactor);

                canvas.DrawPicture(picture);
                canvas.Flush();
            }

            using var ms = new MemoryStream();
            bitmap.Encode(ms, SKEncodedImageFormat.Png, 100);
            return Convert.ToBase64String(ms.ToArray());
        }
    }
}
