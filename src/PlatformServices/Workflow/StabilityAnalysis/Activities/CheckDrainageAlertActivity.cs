using Elsa;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Geometry.Core;
using IxMilia.Dxf.Entities;
using Serilog;
using Slide.Core.Objects.Sli;
using Workflow.StabilityAnalysis.Classes;
using static Workflow.Constants.StabilityAnalysisWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.StabilityAnalysis.Activities
{
    public class CheckDrainageAlertActivity : Activity
    {
        protected override IActivityExecutionResult OnExecute(ActivityExecutionContext context)
        {
            try
            {
                var sliFiles = context.GetTransientVariable<List<SliDetailAggregator>>(Variables.SliFiles);

                foreach (var file in sliFiles)
                {
                    var drainageEntities = file.DxfFile
                       .Entities
                       .Where(x => x.Layer.ToLower().Contains("verif_drenagem")
                       || x.Layer.ToLower().Contains("verificar_drenagem")
                       || x.Layer.ToLower().Contains("verif-drenagem")
                       || x.Layer.ToLower().Contains("verificar-drenagem")
                       || x.Layer.ToLower().Contains("verif drenagem")
                       || x.Layer.ToLower().Contains("verificar drenagem"));

                    if (drainageEntities != null && drainageEntities.Any())
                    {
                        var points = new List<PointD>();

                        foreach (var entity in drainageEntities)
                        {
                            if (entity is DxfVertex vertex)
                            {
                                points.Add(new PointD(vertex.Location.X, vertex.Location.Y));
                            }

                            if (entity is DxfCircle circle)
                            {
                                points.Add(new PointD(circle.Center.X, circle.Center.Y));
                            }
                        }

                        if (points.Any())
                        {
                            var point = points.OrderByDescending(x => x.Y).First();

                            var sliFile = file.SliFile;

                            if (sliFile.WaterTable.Vertices.Any())
                            {
                                var firstPoint = new Vertice();
                                var secondPoint = new Vertice();

                                var firstPointHaveAnyChanges = false;

                                foreach (var verticeIndex in sliFile.WaterTable.Vertices)
                                {
                                    var vertice = sliFile.Vertices.First(x => x.Index == verticeIndex);

                                    if (vertice == null)
                                    {
                                        continue;
                                    }

                                    if (vertice.X <= point.X)
                                    {
                                        firstPoint = vertice;
                                        firstPointHaveAnyChanges = true;
                                    }
                                    else if (vertice.X >= point.X)
                                    {
                                        if (!firstPointHaveAnyChanges && firstPoint.Index != null)
                                        {
                                            break;
                                        }

                                        secondPoint = vertice;
                                        firstPointHaveAnyChanges = false;
                                    }
                                }

                                var slope = (secondPoint.Y - firstPoint.Y) / (secondPoint.X - firstPoint.X);
                                var intercept = firstPoint.Y - slope * firstPoint.X;

                                var lineYAtPointX = slope * point.X + intercept;

                                if (lineYAtPointX > point.Y)
                                {
                                    file.PipingAlertGenerated = true;
                                }
                            }

                            if (sliFile.Piezos.Any())
                            {
                                foreach (var piezo in sliFile.Piezos)
                                {
                                    if (!piezo.Vertices.Any())
                                    {
                                        continue;
                                    }

                                    var firstPoint = new Vertice();
                                    var secondPoint = new Vertice();

                                    var firstPointHaveAnyChanges = false;

                                    foreach (var verticeIndex in piezo.Vertices)
                                    {
                                        var vertice = sliFile.Vertices.First(x => x.Index == verticeIndex);

                                        if (vertice == null)
                                        {
                                            continue;
                                        }

                                        if (vertice.X <= point.X)
                                        {
                                            firstPoint = vertice;
                                        }
                                        else if (vertice.X >= point.X)
                                        {
                                            if (!firstPointHaveAnyChanges && firstPoint.Index != null)
                                            {
                                                break;
                                            }

                                            secondPoint = vertice;
                                            firstPointHaveAnyChanges = false;
                                        }
                                    }

                                    var slope = (secondPoint.Y - firstPoint.Y) / (secondPoint.X - firstPoint.X);
                                    var intercept = firstPoint.Y - slope * firstPoint.X;

                                    var lineYAtPointX = slope * point.X + intercept;

                                    if (lineYAtPointX > point.Y)
                                    {
                                        file.PipingAlertGenerated = true;
                                    }
                                }
                            }
                        }
                    }
                }

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Error in GenerateDrainageAlertActivity");
                context.SetTransientVariable(Variables.ErrorMessage, "Não foi possível prosseguir com a análise de estabilidade pois ocorreu um erro ao verificar a drenagem.");
                return Outcome(OutcomeNames.Cancel);
            }
        }
    }
}
