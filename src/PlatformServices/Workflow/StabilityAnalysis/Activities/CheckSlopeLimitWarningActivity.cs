using Domain.Extensions;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Humanizer;
using Serilog;
using Slide.Core.Objects.Sli;
using Workflow.Extensions;
using Workflow.StabilityAnalysis.Classes;
using static Workflow.Constants.StabilityAnalysisWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.StabilityAnalysis.Activities
{
    public class CheckSlopeLimitWarningActivity : Activity
    {
        private const int MinDistSlopeAlert = 1;

        protected override IActivityExecutionResult OnExecute(
            ActivityExecutionContext context)
        {
            try
            {
                var sliFiles = context
                    .GetTransientVariable<List<SliDetailAggregator>>(
                        Variables.SliFiles);

                foreach (var sliFile in sliFiles)
                {
                    var slopeLimit = sliFile.SliFile.SlopeLimits;

                    foreach (var value in sliFile.Values)
                    {
                        if (!slopeLimit.IsWithinSlopeAlert(
                            value.GlobalMinimumFs, 
                            MinDistSlopeAlert))
                        {
                            continue;
                        }

                        var soilCondition = sliFile.SliFileType
                            .MapSoilConditionType().Humanize();

                        var surfaceType = sliFile.SliFileType
                            .MapSurfaceType().Humanize();

                        var calculationMethod =
                            value.CalculationMethod.Humanize();

                        sliFile.Warnings.Add(
                            $"Os limites da superfície de ruptura estão muito próximos dos limites de busca da seção. Método de cálculo: {calculationMethod}. Condição do solo: {soilCondition}. Tipo de superfície: {surfaceType}. Favor, reavaliar os limites de busca.");
                    }
                }

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Error in CheckSlopeLimitWarningActivity");
                
                context.SetTransientVariable(Variables.ErrorMessage,
                    $"Não conseguimos continuar com a análise de estabilidade porque houve um erro ao verificar os limites de busca: {e.Message}");
                
                return Outcome(OutcomeNames.Cancel);
            }
        }
    }
}