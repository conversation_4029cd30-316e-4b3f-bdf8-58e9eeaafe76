using Application.Apis.Clients.Response._Shared;
using Application.Apis.Clients.Response.Section;
using Application.Apis.Clients.Response.StaticMaterial.GetBySearchId;
using Domain.Enums;
using Domain.Extensions;
using Dxf.Core.Classes;
using Geometry.Core;
using IxMilia.Dxf;
using IxMilia.Dxf.Entities;
using Slide.Core;
using Slide.Core.Enums;
using Slide.Core.Objects.Sli;
using Dxf.Core.Extensions;
using Workflow.Extensions;
using Workflow.StabilityAnalysis.Classes;
using Workflow.StabilityAnalysis.Patterns.Strategy.MaterialType;
using Workflow.StabilityAnalysis.Patterns.Strategy.MaterialType.Factory;

namespace Workflow.StabilityAnalysis.Patterns.Builder
{
    public class SliFileBuilder
    {
        private SliFile _sliFileWithCircularParameterDrained;
        private SliFile _sliFileWithCircularParameterUndrained;
        private SliFile _sliFileWithCircularParameterPseudoStatic;

        private SliFile _sliFileWithNonCircularParameterDrained;
        private SliFile _sliFileWithNonCircularParameterUndrained;
        private SliFile _sliFileWithNonCircularParameterPseudoStatic;

        private readonly double _maximumDistanceBetweenInstruments = 2.5;

        #region File Load. Entrypoint: Load()
        
        public SliFileBuilder Load(string base64File, StructureInfo structure, List<GetStaticMaterialBySearchIdResponse> materials)
        {
            var sliFile = SliFile.Load(base64File);

            if (sliFile == null)
            {
                throw new Exception("Não foi possível carregar o arquivo SLI inicial.");
            }

            if (structure.Slide2Configuration != null)
            {
                ProcessConfiguration(sliFile, structure, materials);
            }

            return this;
        }
        
        #endregion

        #region Circular and NonCircular Parameters. Entrypoint: SetParameters()
        
        public SliFileBuilder SetParameters(
            CircularParameters circularParameters,
            NonCircularParameters nonCircularParameters,
            double? safetyFactorTarget,
            SectionInfo section,
            DxfFile dxfFile)
        {
            var minimumDrainedDepth = section.MinimumDrainedDepth ?? 0;
            var minimumUndrainedDepth = section.MinimumUndrainedDepth ?? 0;
            var minimumPseudoStaticDepth = section.MinimumPseudoStaticDepth ?? 0;

            SetCircularParameter(_sliFileWithCircularParameterDrained, circularParameters, minimumDrainedDepth, safetyFactorTarget);
            SetNonCircularParameter(_sliFileWithNonCircularParameterDrained, nonCircularParameters, minimumDrainedDepth, dxfFile, safetyFactorTarget);
            SetCircularParameter(_sliFileWithCircularParameterUndrained, circularParameters, minimumUndrainedDepth, safetyFactorTarget);
            SetNonCircularParameter(_sliFileWithNonCircularParameterUndrained, nonCircularParameters, minimumUndrainedDepth, dxfFile, safetyFactorTarget);
            SetCircularParameter(_sliFileWithCircularParameterPseudoStatic, circularParameters, minimumPseudoStaticDepth, safetyFactorTarget);
            SetNonCircularParameter(_sliFileWithNonCircularParameterPseudoStatic, nonCircularParameters, minimumPseudoStaticDepth, dxfFile, safetyFactorTarget);

            return this;
        }

        private void SetCircularParameter(SliFile sli,
            CircularParameters circularParameters,
            double minimumDepth,
            double? safetyFactorTarget)
        {
            if (sli == null)
            {
                return;
            }

            if (safetyFactorTarget.HasValue)
            {
                sli.ModelDescription.SeismicAdvanced = true;
                sli.ModelDescription.SeismicTargetFs = (double)safetyFactorTarget;
            }

            sli.ModelDescription.Mode = Mode.Circular;
            sli.ModelDescription.Methods = circularParameters.CalculationMethods.Select(x => x.GetNumber()).Sum();
            sli.ModelDescription.CircularSearch = (CircularSearch)Enum.Parse(typeof(CircularSearch), circularParameters.CircularSearchMethod.ToString());

            sli.MaxCoverageSearch = new()
            {
                CirclesPerDiv = circularParameters.CirclesPerDivision ?? 12,
                Depth = minimumDepth,
                DivisionsAlong = circularParameters.DivisionsAlongSlope ?? 30,
                Iterations = circularParameters.NumberOfIterations ?? 12,
                PercentKept = circularParameters.DivisionsNextIteration ?? 50,
            };

            sli.GridSearch = new()
            {
                Depth = minimumDepth,
                RInc = circularParameters.RadiusIncrement ?? 10,
            };

            sli.ThreepointSearch = new()
            {
                Depth = minimumDepth,
                Samples = circularParameters.NumberOfSurfaces ?? 5000,
            };
        }

        private void SetNonCircularParameter(
            SliFile sli,
            NonCircularParameters nonCircularParameters,
            double minimumDepth,
            DxfFile dxfFile,
            double? safetyFactorTarget)
        {
            if (sli == null)
            {
                return;
            }

            if (safetyFactorTarget.HasValue)
            {
                sli.ModelDescription.SeismicAdvanced = true;
                sli.ModelDescription.SeismicTargetFs = (double)safetyFactorTarget;
            }

            sli.ModelDescription.Mode = Mode.NonCircular;
            sli.ModelDescription.Methods = nonCircularParameters.CalculationMethods.Select(x => x.GetNumber()).Sum();
            sli.ModelDescription.Search = (NonCircularSearch)Enum.Parse(typeof(NonCircularSearch), nonCircularParameters.NonCircularSearchMethod.ToString());

            sli.AutoslopeSearch = new()
            {
                DivisionsAlong = nonCircularParameters.DivisionsAlongSlope ?? 30,
                CirclesPerDiv = nonCircularParameters.SurfacesPerDivision ?? 12,
                Iterations = nonCircularParameters.NumberOfIterations ?? 12,
                PercentKept = nonCircularParameters.DivisionsNextIteration ?? 50,
                NumVertices = nonCircularParameters.NumberOfVerticesAlongSurface ?? 12,
                Depth = minimumDepth
            };

            sli.CuckooSearch = new()
            {
                NVertices = nonCircularParameters.NumberOfVerticesAlongSurface ?? 12,
                MaxIterations = nonCircularParameters.MaximumIterations ?? 500,
                NumNests = nonCircularParameters.NumberOfNests ?? 50,
                Depth = minimumDepth
            };

            sli.SimulatedAnnealingSearch = new()
            {
                NVertices = nonCircularParameters.InitialNumberOfSurfaceVertices ?? 8,
                NGen = nonCircularParameters.InitialNumberOfIterations ?? 2000,
                MaxSteps = nonCircularParameters.MaximumNumberOfSteps ?? 20,
                Nepsilon = nonCircularParameters.NumberOfFactorsSafetyComparedBeforeStopping ?? 5,
                FTol = nonCircularParameters.ToleranceForStoppingCriterion ?? 0.0001,
                Depth = minimumDepth
            };

            sli.PsoSearch = new()
            {
                NVertices = nonCircularParameters.InitialNumberOfSurfaceVertices ?? 8,
                MaxIterations = nonCircularParameters.MaximumIterations ?? 500,
                NumParticles = nonCircularParameters.NumberOfParticles ?? 50,
                Depth = minimumDepth
            };

            sli.BlockSearch = new()
            {
                Samples = nonCircularParameters.NumberOfSurfaces ?? 5000,
                Depth = minimumDepth
            };

            AddBlockSearchRegions(dxfFile, sli);

            sli.PathSearch = new()
            {
                Samples = nonCircularParameters.NumberOfSurfaces ?? 5000,
                Depth = minimumDepth
            };
        }

        private static void AddBlockSearchRegions(DxfFile dxfFile, SliFile sliFile)
        {
            var blockSearchTypes = new[] { "window", "point", "polyline", "line" };

            foreach (var type in blockSearchTypes)
            {
                var blockSearchEntities = dxfFile.GetBlockSearchEntitiesByType(type);

                if (!blockSearchEntities.Any())
                {
                    continue;
                }

                var blockSearchType = Enum.Parse<BlockSearchType>(type, true);

                ProcessBlockSearchType(blockSearchType, blockSearchEntities, sliFile);
            }
        }

        private static void ProcessBlockSearchType(BlockSearchType blockSearchType, List<DxfEntity> entities, SliFile sliFile)
        {
            switch (blockSearchType)
            {
                case BlockSearchType.Window:
                    ProcessWindowType(entities, sliFile);
                    break;
                case BlockSearchType.Point:
                    ProcessPointType(entities, sliFile);
                    break;
                case BlockSearchType.Polyline:
                    ProcessPolylineType(entities, sliFile);
                    break;
                case BlockSearchType.Line:
                    ProcessLineType(entities, sliFile);
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(blockSearchType), blockSearchType, null);
            }
        }

        private static void ProcessWindowType(List<DxfEntity> entities, SliFile sliFile)
        {
            var blockSearchRegion = new BlockSearchRegion() { Type = BlockSearchType.Window };
            ProcessEntities(entities, blockSearchRegion);
            sliFile.BlockSearch.AddBlockSearchRegion(blockSearchRegion);
        }

        private static void ProcessPointType(List<DxfEntity> entities, SliFile sliFile)
        {
            foreach (var entity in entities)
            {
                var blockSearchRegion = new BlockSearchRegion() { Type = BlockSearchType.Point };
                AddVerticesForPointType(entity, blockSearchRegion);
                sliFile.BlockSearch.AddBlockSearchRegion(blockSearchRegion);
            }
        }

        private static void ProcessPolylineType(List<DxfEntity> entities, SliFile sliFile)
        {
            var (leftPointOption, rightPointOption) = entities.FirstOrDefault().DeterminePolylineOptions();
            var blockSearchRegion = new BlockSearchRegion()
            {
                Type = BlockSearchType.Polyline,
                RightPoint = rightPointOption,
                LeftPoint = leftPointOption
            };
            ProcessEntities(entities, blockSearchRegion);
            sliFile.BlockSearch.AddBlockSearchRegion(blockSearchRegion);
        }

        private static void ProcessLineType(List<DxfEntity> entities, SliFile sliFile)
        {
            var blockSearchRegion = new BlockSearchRegion() { Type = BlockSearchType.Line };
            foreach (var entity in entities)
            {
                if (entity is DxfLine line)
                {
                    blockSearchRegion.AddVertice(new() { X = line.P1.X, Y = line.P1.Y });
                    blockSearchRegion.AddVertice(new() { X = line.P2.X, Y = line.P2.Y });
                }
            }
            sliFile.BlockSearch.AddBlockSearchRegion(blockSearchRegion);
        }
        
        private static void ProcessEntities(List<DxfEntity> entities, BlockSearchRegion blockSearchRegion)
        {
            foreach (var entity in entities)
            {
                AddVertices(entity, blockSearchRegion);
            }
        }

        private static void AddVerticesForPointType(DxfEntity entity, BlockSearchRegion blockSearchRegion)
        {
            switch (entity)
            {
                case DxfCircle circle:
                    blockSearchRegion.AddVertice(new() { X = circle.Center.X, Y = circle.Center.Y });
                    break;
                case DxfVertex vertex:
                    blockSearchRegion.AddVertice(new() { X = vertex.Location.X, Y = vertex.Location.Y });
                    break;
            }
        }

        private static void AddVertices(DxfEntity entity, BlockSearchRegion blockSearchRegion)
        {
            switch (entity)
            {
                case DxfPolyline polyline:
                    foreach (var vertex in polyline.Vertices)
                        blockSearchRegion.AddVertice(new() { X = vertex.Location.X, Y = vertex.Location.Y });
                    break;
                case DxfLwPolyline lwPolyline:
                    foreach (var vertex in lwPolyline.Vertices)
                        blockSearchRegion.AddVertice(new() { X = vertex.X, Y = vertex.Y });
                    break;
            }
        }

        #endregion
        
        #region Material Properties. Entrypoint: SetMaterialProperties()
        public SliFileBuilder SetMaterialProperties(
            List<GetStaticMaterialBySearchIdResponse> materials)
        {
            foreach (var material in materials)
            {
                SetMaterial(_sliFileWithCircularParameterDrained, material, material.DrainedStaticMaterialValue);
                SetMaterial(_sliFileWithNonCircularParameterDrained, material, material.DrainedStaticMaterialValue);
                SetMaterial(_sliFileWithCircularParameterUndrained, material, material.UndrainedStaticMaterialValue);
                SetMaterial(_sliFileWithNonCircularParameterUndrained, material, material.UndrainedStaticMaterialValue);
                SetMaterial(_sliFileWithCircularParameterPseudoStatic, material, material.PseudoStaticStaticMaterialValue);
                SetMaterial(_sliFileWithNonCircularParameterPseudoStatic, material, material.PseudoStaticStaticMaterialValue);
            }

            return this;
        }

        private static void SetMaterial(
            SliFile sliFile, 
            GetStaticMaterialBySearchIdResponse material, 
            GetStaticMaterialValueBySearchId materialValue)
        {
            if (sliFile == null || materialValue == null)
            {
                return;
            }
            
            MaterialTypeStrategy materialTypeStrategy;

            if (materialValue.UseDrainedResistanceOverWaterSurface
                && sliFile.MaterialTypes.All(m =>
                    m.MaterialValueId != materialValue.Id))
            {
                var drainedMaterialValue = material.DrainedStaticMaterialValue;
                
                materialTypeStrategy = new MaterialTypeStrategy(
                    drainedMaterialValue.ConstitutiveModel.GetStrategy());
                
                materialTypeStrategy.AddMaterial(
                    sliFile, 
                    drainedMaterialValue,
                    material);
            }

            materialTypeStrategy = new MaterialTypeStrategy(materialValue.ConstitutiveModel.GetStrategy());
            materialTypeStrategy.AddMaterial(sliFile, materialValue, material);
        }
        
        #endregion

        #region Cell Materials. Entrypoint: SetCellMaterials()

        public SliFileBuilder SetCellMaterials(List<GetStaticMaterialBySearchIdResponse> materials, List<PolygonProperties> polygons)
        {
            HandleMaterialInSliFile(_sliFileWithCircularParameterDrained, polygons);
            HandleMaterialInSliFile(_sliFileWithCircularParameterUndrained, polygons);
            HandleMaterialInSliFile(_sliFileWithCircularParameterPseudoStatic, polygons);
            HandleMaterialInSliFile(_sliFileWithNonCircularParameterDrained, polygons);
            HandleMaterialInSliFile(_sliFileWithNonCircularParameterUndrained, polygons);
            HandleMaterialInSliFile(_sliFileWithNonCircularParameterPseudoStatic, polygons);

            return this;
        }
        
        private void HandleMaterialInSliFile(SliFile file, List<PolygonProperties> polygons)
        {
            if (file == null)
            {
                return;
            }

            foreach (var cell in file.Cells)
            {
                var triangle = file
                    .Vertices
                    .Where(x => x.Index == cell.Vertice1 || x.Index == cell.Vertice2 || x.Index == cell.Vertice3)
                    .ToList();

                var centroid = new PointD(triangle.Average(x => x.X), triangle.Average(x => x.Y));

                var isInsideTriangle = polygons.Any(polygon => IsCellInsidePolygon(centroid, polygon, file, cell));

                if (!isInsideTriangle)
                {
                    SetCellMaterialToClosestPolygon(centroid, polygons, file, cell);
                }
            }
        }
        
        private bool IsCellInsidePolygon(PointD centroid, PolygonProperties polygon, SliFile file, Cell cell)
        {
            if (Helper.IsPointInPolygon(
                    polygon.Vertices.Select(x => new PointD(x.Location.X, x.Location.Y)).ToList(),
                    new PointD(centroid.X, centroid.Y)))
            {
                SetCellMaterial(polygon.Layer, file, cell);
                return true;
            }

            return false;
        }

        private void SetCellMaterialToClosestPolygon(PointD centroid, List<PolygonProperties> polygons, SliFile file, Cell cell)
        {
            var closestPolygon = polygons
                .Select(x => new
                {
                    Hatch = x,
                    Distance = Math.Sqrt(Math.Pow(x.CenterX - centroid.X, 2) + Math.Pow(x.CenterY - centroid.Y, 2))
                })
                .OrderBy(x => x.Distance)
                .FirstOrDefault();

            if (closestPolygon == null)
            {
                throw new Exception("Não foi possível encontrar os polígonos próximos para montagem do SLI.");
            }

            SetCellMaterial(closestPolygon.Hatch.Layer, file, cell);
        }

        private void SetCellMaterial(string layer, SliFile file, Cell cell)
        {
            var soil = file.MaterialTypes.FirstOrDefault(x => x.MaterialSearchId.ToString() == layer);

            if (soil == null)
            {
                throw new Exception("Não foi possível encontrar o tipo de material para os solos.");
            }

            cell.Material = $"soil{soil.Index}";
        }

        #endregion

        #region Water Line. Entrypoint: SetWaterLine()
        
        private List<Instrument> RemoveNearbyInstruments(List<Instrument> instruments)
        {
            if (!instruments.Any()) return instruments;
            var pointsToRemove = new List<Instrument>();
            for (int i = 0; i < instruments.Count; i++)
            {
                for (int j = i + 1; j < instruments.Count; j++)
                {
                    var distance = Math.Abs(instruments[i].PointD.X - instruments[j].PointD.X);

                    if (distance > _maximumDistanceBetweenInstruments) continue;
                    pointsToRemove.Add(instruments[i].PointD.Y < instruments[j].PointD.Y ? instruments[i] : instruments[j]);
                }
            }

            return instruments.Except(pointsToRemove).OrderBy(p => p.PointD.X).ToList();
        }

        public SliFileBuilder SetWaterLine(
            List<PointD> externalPoints,
            List<PointD> fixedPointsCoordinates,
            InstrumentPoints instrumentPoints,
            SectionReview sectionReview,
            double? beachLength,
            decimal? upstreamLinimetricRulerQuota,
            decimal? downstreamLinimetricRulerQuota,
            bool dxfHasWaterline)
        {
            var separateInstruments = sectionReview.StructureType.Activities.Any(x => x.Activity == Activity.SeparateInstruments);
            var createReservoir = sectionReview.StructureType.Activities.Any(x => x.Activity == Activity.CreateReservoir);

            var crest = externalPoints
                .Where(x => x.Y == externalPoints.Max(y => y.Y))
                .Where(x => x.X == externalPoints.Where(y => y.Y == externalPoints.Max(z => z.Y)).Min(z => z.X))
                .First();

            var instruments = FilterAndRemoveNearbyInstruments(instrumentPoints, sectionReview);

            if (dxfHasWaterline)
            {
                instruments = instrumentPoints;
                
                var waterTablePoints = instruments.WaterLevelIndicators.Select(p => p.PointD).ToList();
                var piezometricPoints = instruments.Piezometers.Select(p => p.PointD).ToList();

                if (beachLength.HasValue)
                {
                    SetWaterLineWithBeach(externalPoints, crest, beachLength.Value, instruments, fixedPointsCoordinates, separateInstruments,
                        createReservoir, (double?)downstreamLinimetricRulerQuota);

                    return this;
                }

                if (downstreamLinimetricRulerQuota.HasValue)
                {
                    var lastPoint = instrumentPoints.WaterLevelIndicators.Last();
                    lastPoint.PointD = new PointD(lastPoint.PointD.X, (double)downstreamLinimetricRulerQuota);
                }
                else 
                {
                    var startLine = waterTablePoints[^2];
                    var lastExternalPoint = externalPoints.OrderByDescending(x => x.X).First();
                    
                    var endLine = new PointD()
                    {
                        X = lastExternalPoint.X,
                        Y = waterTablePoints.Last().Y
                    };
                    
                    if (Helper.CheckIfLineIntersectsPolygon(startLine, endLine,
                            externalPoints))
                    {
                        var lastPoint = instrumentPoints.WaterLevelIndicators.Last();
                        instrumentPoints.WaterLevelIndicators.Remove(lastPoint);
                        waterTablePoints.Remove(lastPoint.PointD);
                        
                        JoinWaterTablePointsWithPiezometricLine(waterTablePoints, piezometricPoints, instruments);
                        AddFinalVerticesToWaterLineInstruments(lastPoint.PointD.Y, waterTablePoints, externalPoints, crest);
                    }
                }

                if (upstreamLinimetricRulerQuota.HasValue)
                {
                    var firstPoint = instrumentPoints.WaterLevelIndicators.First();
                    firstPoint.PointD = new PointD(firstPoint.PointD.X, (double)upstreamLinimetricRulerQuota);

                    SetWaterLineWithRulerQuota(externalPoints, crest, (double)upstreamLinimetricRulerQuota.Value, instruments, fixedPointsCoordinates, separateInstruments,
                        createReservoir, (double?)downstreamLinimetricRulerQuota);

                    return this;
                }
                
                SetPreDefinedWaterLine(piezometricPoints, waterTablePoints);
                
                return this;
            }

            if (!instruments.Piezometers.Any() && !instruments.WaterLevelIndicators.Any())
            {
                throw new Exception("Não há instrumentos para configurar a linha d'água. Verifique o tipo seco dos instrumentos e suas leituras.");
            }

            if (beachLength.HasValue)
            {
                SetWaterLineWithBeach(externalPoints, crest, beachLength.Value, instruments, fixedPointsCoordinates, separateInstruments,
                    createReservoir, (double?)downstreamLinimetricRulerQuota);
            }
            else if (upstreamLinimetricRulerQuota.HasValue)
            {
                SetWaterLineWithRulerQuota(externalPoints, crest, (double)upstreamLinimetricRulerQuota.Value, instruments, fixedPointsCoordinates, separateInstruments,
                    createReservoir, (double?)downstreamLinimetricRulerQuota);
            }
            else
            {
                SetWaterLineWithoutFirstPoint(externalPoints, crest, instruments, fixedPointsCoordinates, separateInstruments,
                    createReservoir, (double?)downstreamLinimetricRulerQuota);
            }

            return this;
        }
        
        private void SetWaterLineWithBeach(List<PointD> externalPoints, PointD crest, double beachLength, InstrumentPoints instruments, List<PointD> fixedPointsCoordinates, bool separateInstruments,
            bool createWaterTableInReservoir, double? downstreamLinimetricRulerQuota)
        {
            var crestMinusBeachLength = (double)(crest.X - beachLength);
            var pointWithSmallestX = externalPoints.OrderBy(x => x.X).ThenByDescending(x => x.Y).First();

            if (crestMinusBeachLength >= pointWithSmallestX.X)
            {
                var secondPoint = new PointD()
                {
                    X = crestMinusBeachLength,
                    Y = Helper.FindYOnPolygonEdge(crestMinusBeachLength, externalPoints)
                };

                var firstPoint = new PointD()
                {
                    X = pointWithSmallestX.X,
                    Y = secondPoint.Y
                };

                MountWaterLineVertices(externalPoints, firstPoint, secondPoint, crest, instruments, fixedPointsCoordinates, createWaterTableInReservoir, separateInstruments, downstreamLinimetricRulerQuota);
            }
            else
            {
                var y = externalPoints
                    .Where(x => x.X == externalPoints.Where(y => y.X == externalPoints.Min(z => z.X)).Max(z => z.X))
                    .Max(x => x.Y);

                var auxPoint = new PointD()
                {
                    X = crestMinusBeachLength,
                    Y = y,
                };

                var line = new List<PointD>();
                line.AddRange(instruments.Piezometers.Select(x => x.PointD));
                line.AddRange(instruments.WaterLevelIndicators.Select(x => x.PointD));

                if (fixedPointsCoordinates.Any())
                {
                    fixedPointsCoordinates = fixedPointsCoordinates
                        .OrderBy(x => Math.Sqrt(Math.Pow(x.X - auxPoint.X, 2) + Math.Pow(x.Y - auxPoint.Y, 2)))
                        .ToList();

                    foreach (var @fixed in fixedPointsCoordinates)
                    {
                        line.Add(@fixed);
                    }
                }

                line = line
                    .OrderBy(x => x.X)
                    .ToList();

                var firstVertice = line.First();
                var slope = (firstVertice.Y - auxPoint.Y) / (firstVertice.X - auxPoint.X);
                var intercept = auxPoint.Y - slope * auxPoint.X;
                var firstPoint = new PointD(pointWithSmallestX.X, slope * pointWithSmallestX.X + intercept);

                MountWaterLineVertices(externalPoints, firstPoint, firstPoint, crest, instruments, fixedPointsCoordinates, createWaterTableInReservoir, separateInstruments, downstreamLinimetricRulerQuota);
            }
        }

        private InstrumentPoints FilterAndRemoveNearbyInstruments(InstrumentPoints instrumentPoints, SectionReview sectionReview)
        {
            var filteredInstruments = new InstrumentPoints
            {
                Piezometers = instrumentPoints.Piezometers
                    .Where(instrument => instrument.YCoordinateIsValid()).ToList(),
                WaterLevelIndicators = instrumentPoints.WaterLevelIndicators
                    .Where(instrument => instrument.YCoordinateIsValid()).ToList()
            };

            var separateInstruments = sectionReview.StructureType.Activities
                .Any(x => x.Activity == Activity.SeparateInstruments);

            return separateInstruments
                ? RemoveNearbyInstrumentsForSeparated(filteredInstruments)
                : RemoveNearbyInstrumentsForCombined(filteredInstruments);
        }

        private InstrumentPoints RemoveNearbyInstrumentsForSeparated(InstrumentPoints instruments)
        {
            instruments.Piezometers = RemoveNearbyInstruments(instruments.Piezometers);
            instruments.WaterLevelIndicators = RemoveNearbyInstruments(instruments.WaterLevelIndicators);
            return instruments;
        }

        private InstrumentPoints RemoveNearbyInstrumentsForCombined(InstrumentPoints instruments)
        {
            var allInstruments = RemoveNearbyInstruments(
                instruments.Piezometers.Concat(instruments.WaterLevelIndicators).ToList());

            instruments.Piezometers = instruments.Piezometers.Intersect(allInstruments).ToList();
            instruments.WaterLevelIndicators = instruments.WaterLevelIndicators.Intersect(allInstruments).ToList();

            return instruments;
        }

        private void ProcessConfiguration(SliFile sliFile, StructureInfo structure, List<GetStaticMaterialBySearchIdResponse> materials)
        {
            var configuration = structure.Slide2Configuration;

            if (structure.ShouldEvaluateDrainedCondition && materials.All(x => x.DrainedStaticMaterialValue != null))
            {
                CreateDrainedSliFiles(sliFile, configuration);
            }

            if (structure.ShouldEvaluateUndrainedCondition && materials.All(x => x.UndrainedStaticMaterialValue != null))
            {
                CreateUndrainedSliFiles(sliFile, configuration);
            }

            if (structure.ShouldEvaluatePseudoStaticCondition && materials.All(x => x.PseudoStaticStaticMaterialValue != null))
            {
                CreatePseudoStaticSliFiles(sliFile, configuration);
            }
        }

        private void CreateDrainedSliFiles(SliFile sliFile, Slide2Configuration configuration)
        {
            if (configuration.CircularParameters != null)
            {
                _sliFileWithCircularParameterDrained = new SliFile(sliFile);
            }

            if (configuration.NonCircularParameters != null)
            {
                _sliFileWithNonCircularParameterDrained = new SliFile(sliFile);
            }
        }

        private void CreateUndrainedSliFiles(SliFile sliFile, Slide2Configuration configuration)
        {
            if (configuration.CircularParameters != null)
            {
                _sliFileWithCircularParameterUndrained = new SliFile(sliFile);
            }

            if (configuration.NonCircularParameters != null)
            {
                _sliFileWithNonCircularParameterUndrained = new SliFile(sliFile);
            }
        }

        private void CreatePseudoStaticSliFiles(SliFile sliFile, Slide2Configuration configuration)
        {
            if (configuration.CircularParameters != null)
            {
                _sliFileWithCircularParameterPseudoStatic = new SliFile(sliFile);
            }

            if (configuration.NonCircularParameters != null)
            {
                _sliFileWithNonCircularParameterPseudoStatic = new SliFile(sliFile);
            }
        }

        private void SetPreDefinedWaterLine(IEnumerable<PointD> piezometricLine, IEnumerable<PointD> waterLine)
        {
            AddVerticesAndWaterTableToFiles(false, piezometricLine.ToList(), waterLine.ToList(), new[]
            {
                _sliFileWithCircularParameterDrained,
                _sliFileWithCircularParameterUndrained,
                _sliFileWithCircularParameterPseudoStatic,
                _sliFileWithNonCircularParameterDrained,
                _sliFileWithNonCircularParameterUndrained,
                _sliFileWithNonCircularParameterPseudoStatic
            });
        }

        private void SetWaterLineWithoutFirstPoint(List<PointD> externalPoints, PointD crest, InstrumentPoints instruments, List<PointD> fixedPointsCoordinates, bool separateInstruments,
            bool createReservoir, double? downstreamRulerQuota)
        {
            // TODO: Mount water line without first point. The code snippet below is temporary. More info in: #4972
            var firstPoint = externalPoints.GetLeftmostPointExcludingVerticalLines();
            var secondPoint = new PointD(firstPoint.X, firstPoint.Y);

            MountWaterLineVertices(externalPoints, firstPoint, secondPoint, crest, instruments, fixedPointsCoordinates, createReservoir, separateInstruments, downstreamRulerQuota);
        }

        private void SetWaterLineWithRulerQuota(List<PointD> externalPoints, PointD crest, double upstreamRulerQuota, InstrumentPoints instruments, List<PointD> fixedPointsCoordinates, bool separateInstruments,
            bool createReservoir, double? downstreamRulerQuota)
        {
            var firstPoint = CreateInitialPointD(externalPoints, upstreamRulerQuota);
            ValidateInitialPointD(firstPoint.Y, crest.Y);

            var possibleSecondPoints = CalculatePossibleSecondPoints(externalPoints, firstPoint.Y);

            if (!possibleSecondPoints.Any())
            {
                possibleSecondPoints.Add(firstPoint);
            }

            var secondPoint = RetrieveSecondPointD(possibleSecondPoints, firstPoint, crest.X);

            var instrumentPoints = instruments.Piezometers.Concat(instruments.WaterLevelIndicators).ToList();

            var filteredPoints = instrumentPoints
                .Where(x => x.PointD.X >= secondPoint.X)
                .ToList();

            instruments.Piezometers = instruments.Piezometers
                .Where(x => filteredPoints.Any(y => y.PointD == x.PointD))
                .ToList();

            instruments.WaterLevelIndicators = instruments.WaterLevelIndicators
                .Where(x => filteredPoints.Any(y => y.PointD == x.PointD))
                .ToList();

            MountWaterLineVertices(externalPoints, firstPoint, secondPoint, crest, instruments, fixedPointsCoordinates, createReservoir, separateInstruments, downstreamRulerQuota);
        }

        private PointD CreateInitialPointD(List<PointD> externalPoints, double upstreamRulerQuota)
        {
            return new PointD()
            {
                X = externalPoints.Min(x => x.X),
                Y = upstreamRulerQuota
            };
        }

        private void ValidateInitialPointD(double firstY, double crestY)
        {
            if (firstY > crestY - 1)
                firstY = crestY - 1;
        }

        private List<PointD> CalculatePossibleSecondPoints(List<PointD> externalPoints, double firstY)
        {
            var possibleSecondPoints = new List<PointD>();

            for (int i = 0; i < externalPoints.Count; i++)
            {
                var currentPoint = externalPoints[i];
                var nextPoint = externalPoints[(i + 1) % externalPoints.Count];

                if (firstY >= Math.Min(currentPoint.Y, nextPoint.Y) && firstY <= Math.Max(currentPoint.Y, nextPoint.Y))
                {
                    var x = currentPoint.X + (nextPoint.X - currentPoint.X) * (firstY - currentPoint.Y) / (nextPoint.Y - currentPoint.Y);
                    possibleSecondPoints.Add(new(x, firstY));
                }
            }

            return possibleSecondPoints;
        }

        private PointD RetrieveSecondPointD(List<PointD> possibleSecondPoints, PointD firstPoint, double crestX)
        {
            PointD? secondPoint = possibleSecondPoints
                .Where(point => point.X < crestX && point.X >= firstPoint.X)
                .OrderByDescending(point => point.X)
                .FirstOrDefault();

            secondPoint ??= new(firstPoint.X, firstPoint.Y);

            return secondPoint.Value;
        }

        private static void AddPointsToWaterLine(List<PointD> instrumentPoints, PointD point, IList<PointD> piezo, IList<PointD> waterTable, bool createWaterTableInReservoir, bool separateInstruments)
        {
            if (instrumentPoints.Any())
            {
                piezo.Add(point);
            }

            if (createWaterTableInReservoir || !instrumentPoints.Any() || !separateInstruments)
            {
                waterTable.Add(point);
            }
        }

        private static void AddFixedPointsToInstruments(List<PointD> fixedPointsCoordinates, InstrumentPoints instruments, PointD secondPoint, List<PointD> piezoPoints, List<PointD> waterTablePoints)
        {
            if (fixedPointsCoordinates.Any())
            {
                fixedPointsCoordinates = fixedPointsCoordinates
                    .OrderBy(x => Math.Sqrt(Math.Pow(x.X - secondPoint.X, 2) + Math.Pow(x.Y - secondPoint.Y, 2)))
                    .ToList();

                foreach (var @fixed in fixedPointsCoordinates)
                {
                    if (instruments.Piezometers.Any())
                    {
                        piezoPoints.Add(@fixed);
                    }

                    if (instruments.WaterLevelIndicators.Any())
                    {
                        waterTablePoints.Add(@fixed);
                    }
                }
            }
        }

        private static void HandleWaterPointsWithoutDownstreamLinimetricRuler(List<PointD> waterPoints, List<PointD> externalPoints)
        {
            var startLine = waterPoints.Last();
            var lastExternalPoint = externalPoints.OrderByDescending(x => x.X).First();

            var endLine = new PointD()
            {
                X = lastExternalPoint.X,
                Y = waterPoints.Last().Y
            };

            if (!Helper.CheckIfLineIntersectsPolygon(startLine, endLine, externalPoints))
            {
                waterPoints.Add(endLine);
            }
            else
            {
                HandleIntersectingPolygon(waterPoints, externalPoints, startLine);
            }
        }

        private static void HandleIntersectingPolygon(List<PointD> waterPoints, List<PointD> externalPoints, PointD startLine)
        {
            var maxX = externalPoints.Max(p => p.X);

            var lastExternalPointWithGreatestX =externalPoints
                .GetRightmostPointExcludingVerticalLines();
            
            lastExternalPointWithGreatestX.X = maxX;

            var auxVertice = new PointD()
            {
                X = lastExternalPointWithGreatestX.X,
                Y = lastExternalPointWithGreatestX.Y
            };

            var firstExternalVertice = externalPoints.OrderBy(x => x.X).First();

            var polygonWidth = Math.Abs(lastExternalPointWithGreatestX.X - firstExternalVertice.X);
            var xDistance = polygonWidth * 0.01;

            while (auxVertice.X > firstExternalVertice.X
                && (Helper.CheckIfLineIntersectsPolygon(startLine, auxVertice, externalPoints)
                || Helper.CheckIfLineIntersectsPolygon(auxVertice, lastExternalPointWithGreatestX, externalPoints)))
            {
                auxVertice.X -= xDistance;
            }

            if (Helper.CheckIfLineIntersectsPolygon(startLine, auxVertice, externalPoints)
                || Helper.CheckIfLineIntersectsPolygon(auxVertice, lastExternalPointWithGreatestX, externalPoints))
            {
                var minY = externalPoints.Min(x => x.Y);
                var polygonHeight = externalPoints.Max(x => x.Y) - minY;
                var yDistance = polygonHeight * 0.01;

                auxVertice.X = lastExternalPointWithGreatestX.X;

                while ((auxVertice.X > firstExternalVertice.X || auxVertice.Y > minY)
                    && (Helper.CheckIfLineIntersectsPolygon(startLine, auxVertice, externalPoints)
                    || Helper.CheckIfLineIntersectsPolygon(auxVertice, lastExternalPointWithGreatestX, externalPoints)))
                {
                    auxVertice.X -= xDistance;
                    auxVertice.Y -= yDistance;
                }

                if (Helper.CheckIfLineIntersectsPolygon(startLine, auxVertice, externalPoints)
                    || Helper.CheckIfLineIntersectsPolygon(auxVertice, lastExternalPointWithGreatestX, externalPoints))
                {
                    yDistance = polygonHeight * 0.1;

                    auxVertice.X = lastExternalPointWithGreatestX.X;
                    auxVertice.Y = lastExternalPointWithGreatestX.Y;

                    while ((auxVertice.X > firstExternalVertice.X || auxVertice.Y > minY)
                           && (Helper.CheckIfLineIntersectsPolygon(startLine, auxVertice, externalPoints)
                               || Helper.CheckIfLineIntersectsPolygon(auxVertice, lastExternalPointWithGreatestX, externalPoints)))
                    {
                        auxVertice.X -= xDistance;
                        auxVertice.Y -= yDistance;
                    }

                    if (Helper.CheckIfLineIntersectsPolygon(
                            startLine,
                            auxVertice,
                            externalPoints)
                        || Helper.CheckIfLineIntersectsPolygon(
                            auxVertice,
                            lastExternalPointWithGreatestX,
                            externalPoints))
                    {
                        auxVertice.X = lastExternalPointWithGreatestX.X;
                        auxVertice.Y = lastExternalPointWithGreatestX.Y;
                    }
                }

                // The final water point should align with the auxiliary vertex in the same Y-coordinate.
                lastExternalPointWithGreatestX.Y = auxVertice.Y;
            }

            var waterPointMaxX = waterPoints.Max(x => x.X);

            if (waterPointMaxX <= auxVertice.X)
            {
                waterPoints.Add(auxVertice);
            }

            if (waterPointMaxX <= lastExternalPointWithGreatestX.X)
            {
                waterPoints.Add(lastExternalPointWithGreatestX);
            }
        }

        private static void AdjustWaterPointsWhenRulerAboveLastExternalPoint(List<PointD> waterPoints, List<PointD> externalPoints, PointD crest, PointD downstreamLinimetricRuler)
        {
            if (downstreamLinimetricRuler.Y > externalPoints.OrderByDescending(x => x.Y).First().Y)
            {
                waterPoints.Add(downstreamLinimetricRuler);
                return;
            }

            var possibilitiesXDownstream = Helper.FindXDoesIntersectPolygon(downstreamLinimetricRuler.Y, externalPoints);

            var chosenXDownstream = possibilitiesXDownstream
                    .Where(x => x >= crest.X)
                    .OrderBy(x => x)
                    .FirstOrDefault();

            var downstreamLinimetricRulerStartPoint = new PointD(chosenXDownstream, downstreamLinimetricRuler.Y);

            RemoveInstrumentsGreaterThanX(waterPoints, downstreamLinimetricRulerStartPoint);

            waterPoints.Add(downstreamLinimetricRulerStartPoint);
            waterPoints.Add(downstreamLinimetricRuler);
        }

        private static void RemoveInstrumentsGreaterThanX(List<PointD> waterPoints, PointD downstreamLinimetricRulerStartPoint)
        {
            var verticesToRemove = waterPoints
                .Where(x => x.X > downstreamLinimetricRulerStartPoint.X)
                .ToList();

            foreach (var vertex in verticesToRemove)
            {
                waterPoints.Remove(vertex);
            }
        }

        private static void AdjustWaterPointsInNormalConditions(List<PointD> waterPoints, List<PointD> externalPoints, PointD downstreamLinimetricRuler)
        {
            var startLine = waterPoints.Last();

            var intersectsPolygon = Helper.CheckIfLineIntersectsPolygon(startLine, downstreamLinimetricRuler, externalPoints);

            if (!intersectsPolygon)
            {
                waterPoints.Add(downstreamLinimetricRuler);
                return;
            }

            var auxVertice = new PointD()
            {
                X = downstreamLinimetricRuler.X,
                Y = downstreamLinimetricRuler.Y
            };

            var firstExternalVertice = externalPoints.OrderBy(x => x.X).First();

            var polygonWidth = Math.Abs(downstreamLinimetricRuler.X - firstExternalVertice.X);
            var xDistance = polygonWidth * 0.01;

            while (auxVertice.X > firstExternalVertice.X
                && (Helper.CheckIfLineIntersectsPolygon(startLine, auxVertice, externalPoints)
                || Helper.CheckIfLineIntersectsPolygon(auxVertice, downstreamLinimetricRuler, externalPoints)))
            {
                auxVertice.X -= xDistance;
            }

            if (Helper.CheckIfLineIntersectsPolygon(startLine, auxVertice, externalPoints)
                || Helper.CheckIfLineIntersectsPolygon(auxVertice, downstreamLinimetricRuler, externalPoints))
            {
                var minY = externalPoints.Min(x => x.Y);
                var polygonHeight = externalPoints.Max(x => x.Y) - minY;
                var yDistance = polygonHeight * 0.01;

                auxVertice.X = downstreamLinimetricRuler.X;

                while ((auxVertice.X > firstExternalVertice.X || auxVertice.Y > minY)
                && (Helper.CheckIfLineIntersectsPolygon(startLine, auxVertice, externalPoints)
                    || Helper.CheckIfLineIntersectsPolygon(auxVertice, downstreamLinimetricRuler, externalPoints)))
                {
                    auxVertice.X -= xDistance;
                    auxVertice.Y -= yDistance;
                }

                if (Helper.CheckIfLineIntersectsPolygon(startLine, auxVertice, externalPoints)
                    || Helper.CheckIfLineIntersectsPolygon(auxVertice, downstreamLinimetricRuler, externalPoints))
                {
                    auxVertice.X = downstreamLinimetricRuler.X;
                    auxVertice.Y = downstreamLinimetricRuler.Y;
                }
            }

            waterPoints.Add(auxVertice);
            waterPoints.Add(downstreamLinimetricRuler);
        }

        private static void HandleWaterPointsWithDownstreamLinimetricRuler(List<PointD> waterPoints, List<PointD> externalPoints, PointD crest, double downstreamLinimetricRulerQuota)
        {
            var lastExternalPoint = externalPoints
                .Where(p => p.X == externalPoints.Max(x => x.X))
                .OrderByDescending(p => p.Y)
                .FirstOrDefault();

            var smallestYPoint = externalPoints.OrderBy(x => x.Y).First();
            var lastWaterPoint = waterPoints.Last();

            var downstreamLinimetricRuler = new PointD(lastExternalPoint.X, (double)downstreamLinimetricRulerQuota);

            var lastPointIsOutOfLimits = lastWaterPoint.Y > crest.Y || lastWaterPoint.Y < smallestYPoint.Y;

            if (lastPointIsOutOfLimits)
            {
                waterPoints.Add(downstreamLinimetricRuler);
            }
            else if (downstreamLinimetricRuler.Y > lastExternalPoint.Y)
            {
                AdjustWaterPointsWhenRulerAboveLastExternalPoint(waterPoints, externalPoints, crest, downstreamLinimetricRuler);
            }
            else
            {
                AdjustWaterPointsInNormalConditions(waterPoints, externalPoints, downstreamLinimetricRuler);
            }
        }

        private static void AddFinalVerticesToWaterLineInstruments(double? downstreamLinimetricRulerQuota, List<PointD> waterPoints, List<PointD> externalPoints, PointD crest)
        {
            if (!downstreamLinimetricRulerQuota.HasValue)
            {
                HandleWaterPointsWithoutDownstreamLinimetricRuler(waterPoints, externalPoints);
                return;
            }

            HandleWaterPointsWithDownstreamLinimetricRuler(waterPoints, externalPoints, crest, (double)downstreamLinimetricRulerQuota);
        }

        private void AddVerticesAndWaterTableToFiles(bool separateInstruments, List<PointD> piezo, List<PointD> waterTable, SliFile[] files)
        {
            foreach (var file in files.Where(f => f != null))
            {
                file.AddWaterTable(file.AddVertices(waterTable));

                if (separateInstruments)
                {
                    file.AddPiezo(file.AddVertices(piezo));
                }
            }
        }

        private void JoinWaterTablePointsWithPiezometricLine(List<PointD> waterTablePoints, List<PointD> piezoPoints, InstrumentPoints instrumentPoints)
        {
            if (!instrumentPoints.WaterLevelIndicators.Any())
            {
                waterTablePoints.Clear();
                waterTablePoints.AddRange(piezoPoints);

                return;
            }

            if (!instrumentPoints.Piezometers.Any())
            {
                return;
            }

            var waterLevelIndicators = waterTablePoints
                    .Where(x => !piezoPoints.Any(z => z == x))
                    .OrderBy(x => x.X)
                    .ToList();

            var piezometers = piezoPoints
                    .Where(x => !waterTablePoints.Any(z => z == x))
                    .OrderBy(x => x.X)
                    .ToList();

            var index = 0;

            for (int i = 0; i < waterTablePoints.Count; i++)
            {
                var vertice = waterTablePoints[i];

                if (!waterLevelIndicators.Any(z => z == vertice)
                    && !piezometers.Any(z => z == vertice))
                {
                    index = i;
                    continue;
                }

                break;
            }

            index++;

            if (index == 0)
            {
                index = waterTablePoints.Count;
            }

            var firstPart = waterTablePoints.Take(index + 1).ToList();
            var secondPart = waterTablePoints.Skip(index).ToList();

            firstPart.RemoveAll(x => waterLevelIndicators.Any(z => z == x) || piezometers.Any(z => z == x));
            secondPart.RemoveAll(x => waterLevelIndicators.Any(z => z == x) || piezometers.Any(z => z == x));

            waterTablePoints.Clear();

            waterTablePoints.AddRange(firstPart);

            var instruments = waterLevelIndicators.Concat(piezometers).ToList().OrderBy(x => x.X);

            waterTablePoints.AddRange(instruments);

            waterTablePoints.AddRange(secondPart);
        }

        private void MountWaterLineVertices(
            List<PointD> externalPoints,
            PointD firstPoint,
            PointD secondPoint,
            PointD crest,
            InstrumentPoints instruments,
            List<PointD> fixedPointsCoordinates,
            bool createWaterTableInReservoir,
            bool separateInstruments,
            double? downstreamLinimetricRulerQuota)
        {
            var piezoPoints = new List<PointD>();
            var waterTablePoints = new List<PointD>();

            var piezometersPoints = instruments.Piezometers.Select(x => x.PointD).OrderBy(x => x.X).ToList();
            var waterLevelIndicatorsPoints = instruments.WaterLevelIndicators.Select(x => x.PointD).OrderBy(x => x.X).ToList();

            AddPointsToWaterLine(piezometersPoints, firstPoint, piezoPoints, waterTablePoints, createWaterTableInReservoir, separateInstruments);
            AddPointsToWaterLine(piezometersPoints, secondPoint, piezoPoints, waterTablePoints, createWaterTableInReservoir, separateInstruments);
            AddFixedPointsToInstruments(fixedPointsCoordinates, instruments, secondPoint, piezoPoints, waterTablePoints);
            piezoPoints.AddRange(piezometersPoints);
            waterTablePoints.AddRange(waterLevelIndicatorsPoints);

            if (separateInstruments)
            {
                if (waterTablePoints.Any())
                {
                    AddFinalVerticesToWaterLineInstruments(downstreamLinimetricRulerQuota, waterTablePoints, externalPoints, crest);
                }

                if (piezoPoints.Any())
                {
                    AddFinalVerticesToWaterLineInstruments(downstreamLinimetricRulerQuota, piezoPoints, externalPoints, crest);
                }

            }
            else
            {
                JoinWaterTablePointsWithPiezometricLine(waterTablePoints, piezoPoints, instruments);
                AddFinalVerticesToWaterLineInstruments(downstreamLinimetricRulerQuota, waterTablePoints, externalPoints, crest);
            }

            AddVerticesAndWaterTableToFiles(separateInstruments, piezoPoints, waterTablePoints, new[]
            {
                _sliFileWithCircularParameterDrained,
                _sliFileWithCircularParameterUndrained,
                _sliFileWithCircularParameterPseudoStatic,
                _sliFileWithNonCircularParameterDrained,
                _sliFileWithNonCircularParameterUndrained,
                _sliFileWithNonCircularParameterPseudoStatic
            });
        }
        
        #endregion
        
        #region Seismic Load. Entrypoint: SetSeismicLoad()

        public SliFileBuilder SetSeismicLoad(StructureInfo structure)
        {
            if (structure.SeismicCoefficient == null)
            {
                return this;
            }

            if (_sliFileWithCircularParameterPseudoStatic != null)
            {
                _sliFileWithCircularParameterPseudoStatic.ModelDescription.Seismic = structure.SeismicCoefficient.Horizontal;
                _sliFileWithCircularParameterPseudoStatic.ModelDescription.SeismicV = structure.SeismicCoefficient.Vertical;
            }

            if (_sliFileWithNonCircularParameterPseudoStatic != null)
            {
                _sliFileWithNonCircularParameterPseudoStatic.ModelDescription.Seismic = structure.SeismicCoefficient.Horizontal;
                _sliFileWithNonCircularParameterPseudoStatic.ModelDescription.SeismicV = structure.SeismicCoefficient.Vertical;
            }

            return this;
        }
        
        #endregion

        #region Slope Limits. Entrypoint: SetSlopeLimits()
        public SliFileBuilder SetSlopeLimits(List<PointD> slopeLimits, List<PointD> externalPoints)
        {
            if (!slopeLimits.Any())
            {
                var leftmostPoint = externalPoints
                    .GetLeftmostPointExcludingVerticalLines();
                
                slopeLimits.Add(leftmostPoint);
                
                var rightmostPoint = externalPoints
                    .GetRightmostPointExcludingVerticalLines();
                
                slopeLimits.Add(rightmostPoint);
            }

            SetSlopeLimits(_sliFileWithCircularParameterDrained, slopeLimits, externalPoints);
            SetSlopeLimits(_sliFileWithNonCircularParameterDrained, slopeLimits, externalPoints);
            SetSlopeLimits(_sliFileWithCircularParameterUndrained, slopeLimits, externalPoints);
            SetSlopeLimits(_sliFileWithNonCircularParameterUndrained, slopeLimits, externalPoints);
            SetSlopeLimits(_sliFileWithCircularParameterPseudoStatic, slopeLimits, externalPoints);
            SetSlopeLimits(_sliFileWithNonCircularParameterPseudoStatic, slopeLimits, externalPoints);

            return this;
        }

        private static void SetSlopeLimits(SliFile file, List<PointD> slopeLimits, List<PointD> externalPoints)
        {
            if (file is null)
            {
                return;
            }

            if (slopeLimits.ElementAtOrDefault(0) != default 
                && slopeLimits.ElementAtOrDefault(1) != default)
            {
                if (!externalPoints.SlopeLimitsAreValid(
                    slopeLimits[0],
                    slopeLimits[1]))
                {
                    return;
                }
                
                var firstSlopeLimit = externalPoints
                    .FindSlopeLimitPointOnExternal(slopeLimits[0]);
                
                file.SlopeLimits.X1 = firstSlopeLimit.X;
                file.SlopeLimits.Y1 = firstSlopeLimit.Y;
                
                var secondSlopeLimit = externalPoints
                    .FindSlopeLimitPointOnExternal(slopeLimits[1]);
                
                file.SlopeLimits.X2 = secondSlopeLimit.X;
                file.SlopeLimits.Y2 = secondSlopeLimit.Y;
            }

            if (slopeLimits.ElementAtOrDefault(2) != default 
                && slopeLimits.ElementAtOrDefault(3) != default)
            {
                if (!externalPoints.SlopeLimitsAreValid(
                        slopeLimits[2],
                        slopeLimits[3]))
                {
                    return;
                }

                var thirdSlopeLimit = externalPoints
                    .FindSlopeLimitPointOnExternal(slopeLimits[2]);
                
                file.SlopeLimits.X3 = thirdSlopeLimit.X;
                file.SlopeLimits.Y3 = thirdSlopeLimit.Y;
                
                var fourthSlopeLimit = externalPoints
                    .FindSlopeLimitPointOnExternal(slopeLimits[3]);
                
                file.SlopeLimits.X4 = fourthSlopeLimit.X;
                file.SlopeLimits.Y4 = fourthSlopeLimit.Y;
            }
        }
        #endregion
        
        public List<(SliFile, SliFileType)> Build()
        {
            return new List<(SliFile, SliFileType)>
            {
                (_sliFileWithCircularParameterDrained, SliFileType.CircularDrained),
                (_sliFileWithCircularParameterUndrained, SliFileType.CircularUndrained),
                (_sliFileWithCircularParameterPseudoStatic, SliFileType.CircularPseudoStatic),
                (_sliFileWithNonCircularParameterDrained, SliFileType.NonCircularDrained),
                (_sliFileWithNonCircularParameterUndrained, SliFileType.NonCircularUndrained),
                (_sliFileWithNonCircularParameterPseudoStatic, SliFileType.NonCircularPseudoStatic)
            }.Where(x => x.Item1 != null).ToList();
        }
    }
}
