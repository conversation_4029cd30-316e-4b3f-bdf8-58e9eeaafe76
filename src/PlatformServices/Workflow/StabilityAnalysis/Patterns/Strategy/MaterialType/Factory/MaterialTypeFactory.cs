using Domain.Enums;
using Workflow.StabilityAnalysis.Patterns.Strategy.MaterialType.Behavior;

namespace Workflow.StabilityAnalysis.Patterns.Strategy.MaterialType.Factory
{
    public static class MaterialTypeFactory
    {
        public static IMaterialTypeStrategy GetStrategy(this ConstitutiveModel model)
        {
            return model switch
            {
                ConstitutiveModel.MohrCoulomb => new MohrCoulombStrategy(),
                ConstitutiveModel.Undrained => new UndrainedStrategy(),
                ConstitutiveModel.NoStrength => new NoStrengthStrategy(),
                ConstitutiveModel.InfiniteStrength => new InfiniteStrengthStrategy(),
                ConstitutiveModel.ShearOrNormalFunction => new ShearOrNormalFunctionStrategy(),
                ConstitutiveModel.HoekBrown => new HoekBrownStrategy(),
                ConstitutiveModel.GeneralizedHoekBrown => new GeneralizedHoekBrownStrategy(),
                ConstitutiveModel.VerticalStressRatio => new VerticalStressRatioStrategy(),
                ConstitutiveModel.Shansep => new ShansepStrategy(),
                _ => throw new NotImplementedException()
            };
        }
    }
}
