using Application.Apis.Clients.Response.StaticMaterial.GetBySearchId;
using Domain.Enums;
using Slide.Core;
using Slide.Core.Objects.Sli;
using System.Drawing;

namespace Workflow.StabilityAnalysis.Patterns.Strategy.MaterialType.Behavior
{
    public class ShearOrNormalFunctionStrategy : IMaterialTypeStrategy
    {
        public void AddMaterial(SliFile sli, GetStaticMaterialValueBySearchId materialValue, GetStaticMaterialBySearchIdResponse material)
        {
            var materialIndex = sli.MaterialTypes.Count + 1;
            var refMaterialIndex = materialValue.UseDrainedResistanceOverWaterSurface
                ? sli.MaterialTypes.FindIndex(m => m.MaterialValueId == material.DrainedStaticMaterialValue.Id)
                : 0;

            var functionName = $"fn{materialIndex}";

            var materialType = new Slide.Core.Objects.Sli.MaterialType()
            {
                MaterialValueId = (Guid)materialValue.Id,
                MaterialSearchId = material.SearchIdentifier,
                Index = materialIndex,
                Type = Slide.Core.Enums.MaterialType.ShearOrNormalFunction,
                Water = materialValue.CustomHuValue ?? 0,
                Piezo = materialValue.WaterSurface == WaterSurface.PiezometricLine1,
                WTable = materialValue.WaterSurface == WaterSurface.WaterTable,
                HuType = materialValue.Hu == Hu.Auto,
                Uw = materialValue.NaturalSpecificWeight,
                Uwbwt = materialValue.SaturatedSpecificWeight,
                WithRu = materialValue.CustomHuValue ?? 0,
                UseAlternateStrength = materialValue.UseDrainedResistanceOverWaterSurface,
                AlternateMaterialIndex = refMaterialIndex,
                FunctionName = functionName
            };

            var color = ColorTranslator.FromHtml(materialValue.Color);

            var materialProperty = new MaterialProperty()
            {
                Name = material.Name,
                Red = color.R,
                Green = color.G,
                Blue = color.B,
                HasHatch = false,
                Hatch2 = 0,
                HatchClr = 0,
                Guid = Guid.NewGuid()
            };

            sli.AddMaterial(materialType, materialProperty);

            if (materialValue.PointValues?.Count > 0)
            {
                sli.AddStrengthFunction(new()
                {
                    Index = sli.StrengthFunctions.Count + 1,
                    Name = functionName,
                    Functions = materialValue.PointValues.Select(x => new StrengthFunction() { N = x.Value1, S = x.Value2 }).ToList()
                });
            }
        }
    }
}
