using Application.Apis.Clients.Response.StaticMaterial.GetBySearchId;
using Slide.Core;
using Slide.Core.Objects.Sli;
using System.Drawing;

namespace Workflow.StabilityAnalysis.Patterns.Strategy.MaterialType.Behavior
{
    public class UndrainedStrategy : IMaterialTypeStrategy
    {
        public void AddMaterial(SliFile sli, GetStaticMaterialValueBySearchId materialValue, GetStaticMaterialBySearchIdResponse material)
        {
            var materialIndex = sli.MaterialTypes.Count + 1;
            var refMaterialIndex = materialValue.UseDrainedResistanceOverWaterSurface
                ? sli.MaterialTypes.FindIndex(m => m.MaterialValueId == material.DrainedStaticMaterialValue.Id)
                : 0;

            var cohesionType = (Slide.Core.Enums.CohesionType)Enum.Parse(typeof(Slide.Core.Enums.CohesionType), materialValue.CohesionType.ToString());

            var materialType = new Slide.Core.Objects.Sli.MaterialType()
            {
                MaterialValueId = (Guid)materialValue.Id,
                MaterialSearchId = material.SearchIdentifier,
                Index = materialIndex,
                Type = Slide.Core.Enums.MaterialType.Undrained,
                Water = materialValue.CustomHuValue ?? 0,
                Piezo = materialValue.WaterSurface == Domain.Enums.WaterSurface.PiezometricLine1,
                WTable = materialValue.WaterSurface == Domain.Enums.WaterSurface.WaterTable,
                CType = cohesionType,
                C = materialValue.Cohesion ?? 0,
                Dc = materialValue.CohesionVariation,
                Ccut = materialValue.Maximum,
                CDatum = materialValue.Datum,
                MatTensile = materialValue.TensileStrength,
                Uw = materialValue.NaturalSpecificWeight,
                Uwbwt = materialValue.SaturatedSpecificWeight,
                WithRu = materialValue.CustomHuValue ?? 0,
                UseAlternateStrength = materialValue.UseDrainedResistanceOverWaterSurface,
                AlternateMaterialIndex = refMaterialIndex,
            };

            var color = ColorTranslator.FromHtml(materialValue.Color);

            var materialProperty = new MaterialProperty()
            {
                Name = material.Name,
                Red = color.R,
                Green = color.G,
                Blue = color.B,
                HasHatch = false,
                Hatch2 = 0,
                HatchClr = 0,
                Guid = Guid.NewGuid()
            };

            sli.AddMaterial(materialType, materialProperty);
        }
    }
}
