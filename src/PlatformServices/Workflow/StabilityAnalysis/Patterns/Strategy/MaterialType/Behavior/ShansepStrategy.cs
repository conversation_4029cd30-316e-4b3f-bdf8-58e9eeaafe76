using Application.Apis.Clients.Response.StaticMaterial.GetBySearchId;
using Domain.Enums;
using Slide.Core;
using Slide.Core.Objects.Sli;
using System.Drawing;

namespace Workflow.StabilityAnalysis.Patterns.Strategy.MaterialType.Behavior
{
    public class ShansepStrategy : IMaterialTypeStrategy
    {
        public void AddMaterial(SliFile sli, GetStaticMaterialValueBySearchId materialValue, GetStaticMaterialBySearchIdResponse material)
        {
            var materialIndex = sli.MaterialTypes.Count + 1;
            var refMaterialIndex = materialValue.UseDrainedResistanceOverWaterSurface
                ? sli.MaterialTypes.FindIndex(m => m.MaterialValueId == material.DrainedStaticMaterialValue.Id)
                : 0;

            var materialType = new Slide.Core.Objects.Sli.MaterialType()
            {
                MaterialValueId = (Guid)materialValue.Id,
                MaterialSearchId = material.SearchIdentifier,
                Index = materialIndex,
                Type = Slide.Core.Enums.MaterialType.Shansep,
                Water = materialValue.CustomHuValue ?? 0,
                Piezo = materialValue.WaterSurface == WaterSurface.PiezometricLine1,
                WTable = materialValue.WaterSurface == WaterSurface.WaterTable,
                HuType = materialValue.Hu == Hu.Auto,
                ShansepA = materialValue.A ?? 0,
                ShansepM = materialValue.M ?? 0,
                ShansepS = materialValue.S ?? 0,
                HisType = materialValue.StressHistoryType == StressHistoryType.PreconsolidationPressure,
                ShansepMd = 0,
                ShansepHt = materialValue.StressHistoryMethod == StressHistoryMethod.Constant ? 0 : materialValue.StressHistoryMethod == StressHistoryMethod.ByDepthFromUpperMaterialBoundary ? 1 : 2,
                ShansepCon = materialValue.Constant ?? 1,
                Uw = materialValue.NaturalSpecificWeight,
                Uwbwt = materialValue.SaturatedSpecificWeight,
                WithRu = materialValue.CustomHuValue ?? 0,
                UseAlternateStrength = materialValue.UseDrainedResistanceOverWaterSurface,
                AlternateMaterialIndex = refMaterialIndex,
            };

            var color = ColorTranslator.FromHtml(materialValue.Color);

            var materialProperty = new MaterialProperty()
            {
                Name = material.Name,
                Red = color.R,
                Green = color.G,
                Blue = color.B,
                HasHatch = false,
                Hatch2 = 0,
                HatchClr = 0,
                Guid = Guid.NewGuid()
            };

            sli.AddMaterial(materialType, materialProperty);

            if (materialValue.StressHistoryMethod != StressHistoryMethod.Constant && materialValue.PointValues?.Count > 0)
            {
                sli.AddShansepVariableFunctions(new()
                {
                    Soil = materialIndex,
                    Functions = materialValue.PointValues.Select((item, index) => new ShansepFunction()
                    {
                        Index = index,
                        X = item.Value1,
                        Y = item.Value2
                    }).ToList()
                });
            }
        }
    }
}
