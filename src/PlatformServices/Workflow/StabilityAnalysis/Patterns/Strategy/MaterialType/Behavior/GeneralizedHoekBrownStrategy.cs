using Application.Apis.Clients.Response.StaticMaterial.GetBySearchId;
using Slide.Core;
using Slide.Core.Objects.Sli;
using System.Drawing;

namespace Workflow.StabilityAnalysis.Patterns.Strategy.MaterialType.Behavior
{
    public class GeneralizedHoekBrownStrategy : IMaterialTypeStrategy
    {
        public void AddMaterial(
            SliFile sli, 
            GetStaticMaterialValueBySearchId materialValue, 
            GetStaticMaterialBySearchIdResponse material)
        {
            var materialIndex = sli.MaterialTypes.Count + 1;
            var refMaterialIndex = materialValue.UseDrainedResistanceOverWaterSurface
                ? sli.MaterialTypes.FindIndex(m => m.MaterialValueId == material.DrainedStaticMaterialValue.Id)
                : 0;

            var mb = materialValue.Mb ?? 0;
            var s = materialValue.S ?? 0;
            var a = materialValue.A ?? 0;

            if (materialValue.StrengthDefinition == Domain.Enums.StrengthDefinition.GsiMiD)
            {
                var gsi = materialValue.Gsi ?? 0;
                var mi = materialValue.Mi ?? 0;
                var d = materialValue.D ?? 0;

                mb = mi * Math.Exp((gsi - 100.0) / (28.0 - 14.0 * d));
                s = Math.Exp((gsi - 100.0) / (9.0 - 3.0 * d));
                a = 1 / 2.0 + 1 / 6.0 * (Math.Exp(-gsi / 15.0) - Math.Exp(-20 / 3.0));
            }

            var materialType = new Slide.Core.Objects.Sli.MaterialType()
            {
                MaterialValueId = (Guid)materialValue.Id,
                MaterialSearchId = material.SearchIdentifier,
                Index = materialIndex,
                Type = Slide.Core.Enums.MaterialType.GeneralizedHoekBrown,
                Water = materialValue.CustomHuValue ?? 0,
                Piezo = materialValue.WaterSurface == Domain.Enums.WaterSurface.PiezometricLine1,
                WTable = materialValue.WaterSurface == Domain.Enums.WaterSurface.WaterTable,
                HuType = materialValue.Hu == Domain.Enums.Hu.Auto,
                Mb = mb,
                S = s,
                A = a,
                SigC = materialValue.UcsIntact ?? 0,
                Uw = materialValue.NaturalSpecificWeight,
                Uwbwt = materialValue.SaturatedSpecificWeight,
                WithRu = materialValue.CustomHuValue ?? 0,
                UseAlternateStrength = materialValue.UseDrainedResistanceOverWaterSurface,
                AlternateMaterialIndex = refMaterialIndex,
            };

            var color = ColorTranslator.FromHtml(materialValue.Color);

            var materialProperty = new MaterialProperty()
            {
                Name = material.Name,
                Red = color.R,
                Green = color.G,
                Blue = color.B,
                HasHatch = false,
                Hatch2 = 0,
                HatchClr = 0,
                Guid = Guid.NewGuid()
            };

            sli.AddMaterial(materialType, materialProperty);
        }
    }
}
