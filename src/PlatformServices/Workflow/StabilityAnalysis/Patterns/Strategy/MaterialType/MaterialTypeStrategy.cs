using Application.Apis.Clients.Response.StaticMaterial.GetBySearchId;
using Slide.Core;

namespace Workflow.StabilityAnalysis.Patterns.Strategy.MaterialType
{
    internal class MaterialTypeStrategy
    {
        private readonly IMaterialTypeStrategy _materialTypeStrategy;

        public MaterialTypeStrategy(IMaterialTypeStrategy materialTypeStrategy)
        {
            _materialTypeStrategy = materialTypeStrategy;
        }

        public void AddMaterial(SliFile sli, GetStaticMaterialValueBySearchId materialValue, GetStaticMaterialBySearchIdResponse material)
        {
            _materialTypeStrategy.AddMaterial(sli, materialValue, material);
        }
    }
}
