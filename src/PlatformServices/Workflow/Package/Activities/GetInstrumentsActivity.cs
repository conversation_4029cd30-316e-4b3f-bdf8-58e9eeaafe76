using Application.Apis.Clients;
using Application.Apis.Clients.Response.Package.GetById;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services;
using Elsa.Services.Models;
using Serilog;
using static Workflow.Constants.StabilityAnalysisWorkFlow;
using PackageVariables = Workflow.Constants.PackageWorkFlow.Variables;

namespace Workflow.Package.Activities
{
    public class GetInstrumentsActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;
        private GetPackageByIdResponse _package;

        public GetInstrumentsActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                _package = context.GetTransientVariable<Application.Apis.Clients.Response.Package.GetById.GetPackageByIdResponse>(PackageVariables.Package);
                var instruments = new List<Application.Apis.Clients.Response.Instrument.GetInstrumentByIdResponse>();

                foreach (var section in _package.Sections)
                {
                    foreach (var instrument in section.Instruments)
                    {
                        if (!instruments.Any(x => x.Id == instrument.Id))
                        {
                            var instrumentResponse = await _clientsApiService.GetInstrumentById(instrument.Id);

                            if (instrumentResponse == null)
                            {
                                context.SetTransientVariable(Variables.ErrorMessage, $"Não foi possível prosseguir com a análise de estabilidade do pacote com data {_package.Date:dd/MM/yyyy HH:mm} pois ocorreu um erro ao buscar informações dos instrumentos.");
                                return Outcome(OutcomeNames.Cancel);
                            }

                            instruments.Add(instrumentResponse);
                        }
                    }
                }

                if (_package.UpstreamLinimetricRuler?.Id != null)
                {
                    var instrumentResponse = await _clientsApiService.GetInstrumentById((Guid)_package.UpstreamLinimetricRuler.Id);

                    if (instrumentResponse == null)
                    {
                        context.SetTransientVariable(Variables.ErrorMessage, $"Não foi possível prosseguir com a análise de estabilidade do pacote com data {_package.Date:dd/MM/yyyy HH:mm} pois ocorreu um erro ao buscar informações da régua linimétrica montante.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    instruments.Add(instrumentResponse);
                }

                if (_package.DownstreamLinimetricRuler?.Id != null)
                {
                    var instrumentResponse = await _clientsApiService.GetInstrumentById((Guid)_package.DownstreamLinimetricRuler.Id);

                    if (instrumentResponse == null)
                    {
                        context.SetTransientVariable(Variables.ErrorMessage, $"Não foi possível prosseguir com a análise de estabilidade do pacote com data {_package.Date:dd/MM/yyyy HH:mm} pois ocorreu um erro ao buscar informações da régua linimétrica jusante.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    instruments.Add(instrumentResponse);
                }

                context.SetTransientVariable(PackageVariables.Instruments, instruments);

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Error in GetInstrumentsActivity");
                context.SetTransientVariable(Variables.ErrorMessage, $"Não foi possível prosseguir com a análise de estabilidade do pacote com data {_package.Date:dd/MM/yyyy HH:mm} pois ocorreu um erro inesperado ao buscar informações dos instrumentos.");
                return Outcome(OutcomeNames.Cancel);
            }
        }
    }
}
