using Application.Apis.Clients;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services;
using Elsa.Services.Models;
using Serilog;
using static Workflow.Constants.PackageWorkFlow;

namespace Workflow.Package.Activities
{
    public class GetPackageActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public GetPackageActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var packageId = (context.GetTransientVariable<Domain.Messages.Commands.Package.CalculateStability>(Variables.Command)).PackageId;

                var package = await _clientsApiService.GetPackageById(packageId);

                if (package == null)
                {
                    Log.Error($"Package with id {packageId} not found");
                    return Outcome(OutcomeNames.Cancel);
                }

                context.SetTransientVariable(Variables.Package, package);

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Error in GetPackageActivity");
                return Outcome(OutcomeNames.Cancel);
            }
        }
    }
}
