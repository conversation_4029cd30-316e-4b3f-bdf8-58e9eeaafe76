using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Application.Apis.Clients.Response.Package.GetById;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Serilog;
using System.Text;
using Workflow.StabilityAnalysis.Classes;
using static Workflow.Constants.StabilityAnalysisWorkFlow;
using Activity = Elsa.Services.Activity;
using PackageVariables = Workflow.Constants.PackageWorkFlow.Variables;

namespace Workflow.Package.Activities
{
    public class CreateStabilityAnalysisActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;
        private GetPackageByIdResponse _package;

        public CreateStabilityAnalysisActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                _package = context.GetTransientVariable<GetPackageByIdResponse>(PackageVariables.Package);
                var sliFiles = context.GetTransientVariable<List<SliDetailAggregator>>(Variables.SliFiles);
                var groupedFiles = sliFiles.GroupBy(x => x.Section.SectionId).ToList();

                foreach (var group in groupedFiles)
                {
                    var files = group.ToList();

                    var payload = new AddStabilityAnalysisRequest()
                    {
                        Piping = files.Any(x => x.PipingAlertGenerated),
                        SectionId = group.Key,
                        StructureId = files.First().Structure.StructureId,
                        ReadingCreatedDate = _package.Date,
                        SafetyFactorResults = new List<AddSafetyFactorRequest>(),
                        SectionReviewId = files.First().Section.SectionReviewData.SectionReview.Id,
                        CalculatedById = _package.CalculatedById,
                        ConstructionStageId = files.First().Section.SectionReviewData.ConstructionStage?.Id,
                        Warnings = files.First().Warnings
                    };

                    foreach (var file in files)
                    {
                        var result = new AddSafetyFactorRequest()
                        {
                            SliFile = new()
                            {
                                Base64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(file.SliFile.Save())),
                                Name = "sli_used.sli"
                            },
                            SltmFile = new()
                            {
                                Base64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(file.SltmFile.Save())),
                                Name = "sltm_used.sltm"
                            },
                            SliFileType = file.SliFileType,
                            Values = new()
                        };

                        foreach (var fs in file.Values)
                        {
                            result.Values.Add(new()
                            {
                                CalculationMethod = fs.CalculationMethod,
                                DxfFile = new()
                                {
                                    Base64 = fs.DxfBase64,
                                    Name = "analysis_result_dxf_file.dxf"
                                },
                                PngFile = new()
                                {
                                    Base64 = fs.PngBase64,
                                    Name = "analysis_result_png_file.png"
                                },
                                Value = fs.Value
                            });
                        }


                        payload.SafetyFactorResults.Add(result);
                    }

                    var res = await _clientsApiService.CreateStabilityAnalysis(payload);

                    if (res == Guid.Empty)
                    {
                        context.SetTransientVariable(Variables.ErrorMessage, $"Não foi possível prosseguir com a análise de estabilidade do pacote com data {_package.Date:dd/MM/yyyy HH:mm} pois ocorreu um erro ao criar a entidade análise de estabilidade.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    await _clientsApiService.DeletePackage(_package.Id, payload.SectionId);
                }

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Error in CreateStabilityAnalysisActivity");
                context.SetTransientVariable(Variables.ErrorMessage, $"Não foi possível prosseguir com a análise de estabilidade do pacote com data {_package.Date:dd/MM/yyyy HH:mm} pois ocorreu um erro inesperado ao criar a entidade análise de estabilidade.");
                return Outcome(OutcomeNames.Cancel);
            }
        }
    }
}
