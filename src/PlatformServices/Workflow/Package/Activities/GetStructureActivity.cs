using Application.Apis.Clients;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services;
using Elsa.Services.Models;
using Serilog;
using static Workflow.Constants.StabilityAnalysisWorkFlow;
using PackageVariables = Workflow.Constants.PackageWorkFlow.Variables;

namespace Workflow.Package.Activities
{
    public class GetStructureActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public GetStructureActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }
        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var sections = context.GetTransientVariable<List<Application.Apis.Clients.Response.Section.GetById.GetSectionByIdResponse>>(PackageVariables.Sections);
                var section = sections.First();

                var structure = await _clientsApiService.GetStructureStabilityAnalysisInfo(section.Structure.Id);

                if (structure == null)
                {
                    Log.Error($"Structure with id {section.Structure.Id} not found");
                    return Outcome(OutcomeNames.Cancel);
                }

                context.SetTransientVariable(PackageVariables.Structure, structure);
                context.SetTransientVariable(Variables.StructureId, structure.Id);

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Error in GetStructureActivity");
                return Outcome(OutcomeNames.Cancel);
            }
        }
    }
}
