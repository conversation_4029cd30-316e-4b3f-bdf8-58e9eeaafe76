using Application.Apis.Clients;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services;
using Elsa.Services.Models;
using Serilog;
using static Workflow.Constants.PackageWorkFlow;

namespace Workflow.Package.Activities
{
    public class GetSectionsActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public GetSectionsActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var sectionIds = (context.GetTransientVariable<Domain.Messages.Commands.Package.CalculateStability>(Variables.Command)).SectionIds;
                var sections = new List<Application.Apis.Clients.Response.Section.GetById.GetSectionByIdResponse>();

                foreach (var sectionId in sectionIds)
                {
                    var section = await _clientsApiService.GetSectionById(sectionId);

                    if (section == null)
                    {
                        Log.Error($"Section with id {sectionId} not found");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    sections.Add(section);
                }

                context.SetTransientVariable(Variables.Sections, sections);

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Error in GetSectionsActivity");
                return Outcome(OutcomeNames.Cancel);
            }
        }
    }
}
