using Application.Apis.Clients;
using Elsa.ActivityResults;
using Elsa.Services;
using Elsa.Services.Models;
using Serilog;
using static Workflow.Constants.PackageWorkFlow;

namespace Workflow.Package.Activities
{
    public class ChangePackageStatusActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public ChangePackageStatusActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var packageId = (context.GetTransientVariable<Domain.Messages.Commands.Package.CalculateStability>(Variables.Command)).PackageId;

                var entityHasBeenUpdated = await _clientsApiService.UpdatePackageStatus(new()
                {
                    Id = packageId,
                    Status = Domain.Enums.PackageStatus.Failed
                });

                if (!entityHasBeenUpdated)
                {
                    return Fault($"There was a problem updating package {packageId} status");
                }

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Error in ChangePackageStatusActivity");
                return Fault(e);
            }
        }
    }
}
