using Elsa;
using Elsa.Activities.ControlFlow;
using Elsa.Activities.MassTransit;
using Elsa.Activities.Primitives;
using Elsa.Activities.Workflows;
using Elsa.Builders;
using Workflow.Reading.GenerateAlerts;
using Workflow.Reading.RecalculateStability;
using static Elsa.Activities.Workflows.RunWorkflow;
using static Workflow.Constants.ReadingWorkFlow;

namespace Workflow.Reading.ReadingUpdated;

public class ReadingUpdatedWorkflow : IWorkflow
{
    public void Build(IWorkflowBuilder builder)
    {
        builder
            .WithDeleteCompletedInstances(true)
            .WithDisplayName("Reading updated workflow")
            .ReceiveMassTransitMessage(
                activity => activity.Set(x => x.MessageType, x => typeof(Domain.Messages.Events.Reading.ReadingUpdated)))
            .WithDisplayName("Receive reading updated message")
            .SetTransientVariable(Variables.ReadingEvent, context => context.GetInput<Domain.Messages.Events.Reading.ReadingUpdated>())
            .WithDisplayName("Save received message")
            .RunWorkflow<GenerateAlertsWorkflow>(
                mode: RunWorkflowMode.FireAndForget,
                input: context => context.GetTransientVariable<Domain.Messages.Events.Reading.ReadingUpdated>(Variables.ReadingEvent))
            .WithDisplayName("Generate Alerts")
            .If(context => context.GetTransientVariable<Domain.Messages.Events.Reading.ReadingUpdated>(Variables.ReadingEvent).ShouldRecalculateStability,
                @if =>
                {
                    @if
                        .When(OutcomeNames.True)
                        .RunWorkflow<RecalculateStabilityWorkflow>(
                            mode: RunWorkflowMode.FireAndForget,
                            input: context => context.GetTransientVariable<Domain.Messages.Events.Reading.ReadingUpdated>(Variables.ReadingEvent))
                        .WithDisplayName("Recalculate Stability")
                        .Finish();

                    @if
                        .When(OutcomeNames.False)
                        .Finish();
                })
            .WithDisplayName("Check if recalculate stability is needed");
    }
}