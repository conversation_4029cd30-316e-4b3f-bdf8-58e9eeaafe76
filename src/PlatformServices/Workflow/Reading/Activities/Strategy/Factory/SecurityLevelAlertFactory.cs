using Domain.Enums;
using Workflow.Reading.Activities.Strategy.Behavior;

namespace Workflow.Reading.Activities.Strategy.Factory
{
    public static class SecurityLevelAlertFactory
    {
        public static ISecurityLevelAlertStrategy GetStrategy(
            this InstrumentType instrumentType
            )
        {
            return instrumentType switch
            {
                InstrumentType.WaterLevelIndicator => new WaterLevelIndicatorSecurityLevelAlertStrategy(),
                InstrumentType.OpenStandpipePiezometer => new OpenStandpipePiezometerSecurityLevelAlertStrategy(),
                InstrumentType.ElectricPiezometer => new ElectricPiezometerSecurityLevelAlertStrategy(),
                InstrumentType.SurfaceLandmark => new SurfaceLandmarkSecurityLevelAlertStrategy(),
                InstrumentType.Prism => new PrismSecurityLevelAlertStrategy(),
                InstrumentType.Pluviograph => new PluviographSecurityLevelAlertStrategy(),
                InstrumentType.Pluviometer => new PluviometerSecurityLevelAlertStrategy(),
                _ => null
            };
        }
    }
}
