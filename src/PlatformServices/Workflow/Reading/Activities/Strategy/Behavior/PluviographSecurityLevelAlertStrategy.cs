using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Application.Apis.Clients.Response.Instrument;
using Application.Apis.Clients.Response.Reading;

namespace Workflow.Reading.Activities.Strategy.Behavior
{
    public class PluviographSecurityLevelAlertStrategy : ISecurityLevelAlertStrategy
    {
        public async Task Create(GetReadingByIdResponse reading, GetInstrumentByIdResponse instrument, IClientsApiService clientsApiService)
        {
            var rv = reading.Values.First();

            if (rv.SecurityLevels == null || !rv.SecurityLevels.Any())
            {
                return;
            }

            var securityLevel = rv.SecurityLevels.First();

            var securityLevelAlertsRequest = new List<SecurityLevelAlertRequest>();

            if (rv.Intensity >= securityLevel.RainIntensity)
            {
                var securityLevelAlert = new SecurityLevelAlertRequest()
                {
                    Difference = (decimal)(rv.Intensity - securityLevel.RainIntensity),
                    InstrumentId = instrument.Id,
                    ReadingValueDate = rv.Date,
                    SecurityLevel = Domain.Enums.SecurityLevelType.RainIntensity
                };

                securityLevelAlertsRequest.Add(securityLevelAlert);
            }

            var sumOfTotalRainfall = await clientsApiService.GetTotalRainfall(instrument.Id, rv.Date);

            if (sumOfTotalRainfall >= securityLevel.MaximumDailyRainfall)
            {
                var securityLevelAlert = new SecurityLevelAlertRequest()
                {
                    Difference = (decimal)(sumOfTotalRainfall - securityLevel.MaximumDailyRainfall),
                    InstrumentId = instrument.Id,
                    ReadingValueDate = rv.Date,
                    SecurityLevel = Domain.Enums.SecurityLevelType.MaximumDailyRainfall
                };

                securityLevelAlertsRequest.Add(securityLevelAlert);
            }

            if (!securityLevelAlertsRequest.Any())
            {
                return;
            }

            var request = new AddInstrumentSecurityLevelAlertsRequest
            {
                SecurityLevelAlerts = securityLevelAlertsRequest,
            };

            await clientsApiService.AddSecurityLevelAlert(instrument.Id, request);
        }
    }
}
