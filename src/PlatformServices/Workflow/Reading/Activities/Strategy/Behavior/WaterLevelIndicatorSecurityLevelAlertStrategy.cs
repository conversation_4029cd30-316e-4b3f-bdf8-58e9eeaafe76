using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Application.Apis.Clients.Response.Instrument;
using Application.Apis.Clients.Response.Reading;
using Workflow.Extensions;

namespace Workflow.Reading.Activities.Strategy.Behavior
{
    public class WaterLevelIndicatorSecurityLevelAlertStrategy : ISecurityLevelAlertStrategy
    {
        public async Task Create(GetReadingByIdResponse reading, GetInstrumentByIdResponse instrument, IClientsApiService clientsApiService)
        {
            var rv = reading.Values.First();

            if (rv.SecurityLevels == null || !rv.SecurityLevels.Any())
            {
                return;
            }

            var securityLevel = rv.SecurityLevels.First();

            var adjacentReadingValues = await clientsApiService
                .GetInstrumentAdjacentReadingValues(reading.Instrument.Id, null, rv.Date);

            var currentReadingValue = rv.Quota ?? reading.InstrumentBaseQuota ?? 0;
            var previousReadingValue = adjacentReadingValues.Previous?.Quota ?? adjacentReadingValues.Previous?.InstrumentBaseQuota ?? 0;
            var nextReadingValue = adjacentReadingValues.Next?.Quota ?? adjacentReadingValues.Next?.InstrumentBaseQuota ?? 0;

            var diffToPrevious = CalculateDiffToPrevious(previousReadingValue, currentReadingValue);
            var diffToNext = CalculateDiffToNext(nextReadingValue, currentReadingValue);

            var securityLevelAlerts = securityLevel
                .GetSecurityLevelAlertsFromDiff(diffToPrevious)
                .Select(securityLevel => new SecurityLevelAlertRequest
                {
                    Difference = diffToPrevious,
                    InstrumentId = instrument.Id,
                    ReadingValueDate = rv.Date,
                    SecurityLevel = securityLevel
                });

            securityLevelAlerts = securityLevelAlerts.Concat(securityLevel
                .GetSecurityLevelAlertsFromDiff(diffToNext)
                .Select(securityLevel => new SecurityLevelAlertRequest
                {
                    Difference = diffToNext,
                    InstrumentId = instrument.Id,
                    ReadingValueDate = rv.Date,
                    SecurityLevel = securityLevel
                }));

            if (!securityLevelAlerts.Any())
            {
                return;
            }

            var request = new AddInstrumentSecurityLevelAlertsRequest
            {
                SecurityLevelAlerts = securityLevelAlerts,
            };

            await clientsApiService.AddSecurityLevelAlert(instrument.Id, request);
        }

        private static decimal CalculateDiffToPrevious(
            decimal previousReadingValue,
            decimal currentReadingValue)
        {
            return Math.Abs(previousReadingValue - currentReadingValue);
        }

        private static decimal CalculateDiffToNext(
            decimal nextReadingValue,
            decimal currentReadingValue)
        {
            return Math.Abs(currentReadingValue - nextReadingValue);
        }
    }
}
