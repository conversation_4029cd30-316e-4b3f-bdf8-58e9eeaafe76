using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Application.Apis.Clients.Response.Instrument;
using Application.Apis.Clients.Response.Reading;

namespace Workflow.Reading.Activities.Strategy.Behavior
{
    public class PluviometerSecurityLevelAlertStrategy : ISecurityLevelAlertStrategy
    {
        public async Task Create(GetReadingByIdResponse reading, GetInstrumentByIdResponse instrument, IClientsApiService clientsApiService)
        {
            var rv = reading.Values.First();

            if (rv.SecurityLevels == null || !rv.SecurityLevels.Any())
            {
                return;
            }

            var securityLevel = rv.SecurityLevels.First();

            if (rv.Pluviometry >= securityLevel.MaximumDailyRainfall)
            {
                var securityLevelAlert = new SecurityLevelAlertRequest()
                {
                    Difference = (decimal)(rv.Pluviometry - securityLevel.MaximumDailyRainfall),
                    InstrumentId = instrument.Id,
                    ReadingValueDate = rv.Date,
                    SecurityLevel = Domain.Enums.SecurityLevelType.MaximumDailyRainfall
                };

                var request = new AddInstrumentSecurityLevelAlertsRequest
                {
                    SecurityLevelAlerts = new List<SecurityLevelAlertRequest>()
                    {
                        securityLevelAlert
                    }
                };

                await clientsApiService.AddSecurityLevelAlert(instrument.Id, request);
            }
        }
    }
}
