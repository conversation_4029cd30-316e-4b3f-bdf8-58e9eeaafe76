using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Application.Apis.Clients.Response.Instrument;
using Application.Apis.Clients.Response.Reading;
using Domain.Enums;
using Workflow.Extensions;

namespace Workflow.Reading.Activities.Strategy.Behavior
{
    public class SurfaceLandmarkSecurityLevelAlertStrategy : ISecurityLevelAlertStrategy
    {
        public async Task Create(GetReadingByIdResponse reading, GetInstrumentByIdResponse instrument, IClientsApiService clientsApiService)
        {
            var currentReadingValue = reading.Values.First();

            if (currentReadingValue.SecurityLevels == null || !currentReadingValue.SecurityLevels.Any())
            {
                return;
            }

            var adjacentReadingValues = await clientsApiService
                .GetInstrumentAdjacentReadingValues(reading.Instrument.Id, null, currentReadingValue.Date);

            var diffsToPrevious = CalculateDiffToPrevious(adjacentReadingValues.Previous, currentReadingValue);
            var diffsToNext = CalculateDiffToNext(adjacentReadingValues.Next, currentReadingValue);

            var securityLevelAlerts = diffsToPrevious
                .Concat(diffsToNext)
                .SelectMany(diff =>
                {
                    var securityLevel = currentReadingValue.SecurityLevels.FirstOrDefault(x => x.Axis == diff.Axis);

                    if (securityLevel == null)
                    {
                        return null;
                    }

                    var alerts = securityLevel.GetSecurityLevelAlertsFromDiff(diff.Value);

                    return alerts
                        .Select(alert => new SecurityLevelAlertRequest()
                        {
                            Difference = diff.Value,
                            InstrumentId = instrument.Id,
                            ReadingValueDate = currentReadingValue.Date,
                            DisplacedAxis = diff.Axis,
                            SecurityLevel = alert
                        });
                })
                .Where(request => request != null);

            if (!securityLevelAlerts.Any())
            {
                return;
            }

            var request = new AddInstrumentSecurityLevelAlertsRequest
            {
                SecurityLevelAlerts = securityLevelAlerts,
            };

            await clientsApiService.AddSecurityLevelAlert(instrument.Id, request);
        }

        private static IEnumerable<(decimal Value, Axis Axis)> CalculateDiffToPrevious(
            GetInstrumentReadingValuesResponse previousReadingValue,
            ReadingValueResponse currentReadingValue)
        {
            var diffs = new List<(decimal Value, Axis Axis)>();

            if (previousReadingValue == null)
            {
                return diffs;
            }

            if (previousReadingValue.EastDisplacement != null && currentReadingValue.EastDisplacement != null)
            {
                var eastDiff = Math.Abs(previousReadingValue.EastDisplacement.Value - currentReadingValue.EastDisplacement.Value);
                diffs.Add((eastDiff, Axis.E));
            }
            if (previousReadingValue.NorthDisplacement != null && currentReadingValue.NorthDisplacement != null)
            {
                var northDiff = Math.Abs(previousReadingValue.NorthDisplacement.Value - currentReadingValue.NorthDisplacement.Value);
                diffs.Add((northDiff, Axis.N));
            }
            if (previousReadingValue.ADisplacement != null && currentReadingValue.ADisplacement != null)
            {
                var aDiff = Math.Abs(previousReadingValue.ADisplacement.Value - currentReadingValue.ADisplacement.Value);
                diffs.Add((aDiff, Axis.A));
            }
            if (previousReadingValue.BDisplacement != null && currentReadingValue.BDisplacement != null)
            {
                var bDiff = Math.Abs(previousReadingValue.BDisplacement.Value - currentReadingValue.BDisplacement.Value);
                diffs.Add((bDiff, Axis.B));
            }
            if (previousReadingValue.ZDisplacement != null && currentReadingValue.ZDisplacement != null)
            {
                var zDiff = Math.Abs(previousReadingValue.ZDisplacement.Value - currentReadingValue.ZDisplacement.Value);
                diffs.Add((zDiff, Axis.Z));
            }
            if (previousReadingValue.TotalPlanimetricDisplacement != null && currentReadingValue.TotalPlanimetricDisplacement != null)
            {
                var planimetricDiff = Math.Abs(previousReadingValue.TotalPlanimetricDisplacement.Value - currentReadingValue.TotalPlanimetricDisplacement.Value);
                diffs.Add((planimetricDiff, Axis.Planimetric));
            }

            return diffs;
        }

        private static IEnumerable<(decimal Value, Axis Axis)> CalculateDiffToNext(
            GetInstrumentReadingValuesResponse nextReadingValue,
            ReadingValueResponse currentReadingValue)
        {
            var diffs = new List<(decimal Value, Axis Axis)>();

            if (nextReadingValue == null)
            {
                return diffs;
            }

            if (nextReadingValue.EastDisplacement != null && currentReadingValue.EastDisplacement != null)
            {
                var eastDiff = Math.Abs(currentReadingValue.EastDisplacement.Value - nextReadingValue.EastDisplacement.Value);
                diffs.Add((eastDiff, Axis.E));
            }
            if (nextReadingValue.NorthDisplacement != null && currentReadingValue.NorthDisplacement != null)
            {
                var northDiff = Math.Abs(currentReadingValue.NorthDisplacement.Value - nextReadingValue.NorthDisplacement.Value);
                diffs.Add((northDiff, Axis.N));
            }
            if (nextReadingValue.ADisplacement != null && currentReadingValue.ADisplacement != null)
            {
                var aDiff = Math.Abs(currentReadingValue.ADisplacement.Value - nextReadingValue.ADisplacement.Value);
                diffs.Add((aDiff, Axis.A));
            }
            if (nextReadingValue.BDisplacement != null && currentReadingValue.BDisplacement != null)
            {
                var bDiff = Math.Abs(currentReadingValue.BDisplacement.Value - nextReadingValue.BDisplacement.Value);
                diffs.Add((bDiff, Axis.B));
            }
            if (nextReadingValue.ZDisplacement != null && currentReadingValue.ZDisplacement != null)
            {
                var zDiff = Math.Abs(currentReadingValue.ZDisplacement.Value - nextReadingValue.ZDisplacement.Value);
                diffs.Add((zDiff, Axis.Z));
            }
            if (nextReadingValue.TotalPlanimetricDisplacement != null && currentReadingValue.TotalPlanimetricDisplacement != null)
            {
                var planimetricDiff = Math.Abs(currentReadingValue.TotalPlanimetricDisplacement.Value - nextReadingValue.TotalPlanimetricDisplacement.Value);
                diffs.Add((planimetricDiff, Axis.Planimetric));
            }

            return diffs;
        }
    }
}
