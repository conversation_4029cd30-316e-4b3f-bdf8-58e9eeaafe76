using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Application.Apis.Clients.Response.Instrument;
using Application.Apis.Clients.Response.Reading;
using Workflow.Extensions;

namespace Workflow.Reading.Activities.Strategy.Behavior
{
    public class ElectricPiezometerSecurityLevelAlertStrategy : ISecurityLevelAlertStrategy
    {
        public async Task Create(GetReadingByIdResponse reading, GetInstrumentByIdResponse instrument, IClientsApiService clientsApiService)
        {
            var securityLevelAlertsRequest = new List<SecurityLevelAlertRequest>();

            foreach (var readingValue in reading.Values)
            {
                var measurement = instrument.Measurements.FirstOrDefault(m => m.Id == readingValue.Measurement.Id);

                if (measurement == null)
                {
                    continue;
                }

                if (readingValue.SecurityLevels == null || !readingValue.SecurityLevels.Any())
                {
                    continue;
                }

                var securityLevel = readingValue.SecurityLevels.First();

                var adjacentReadingValues = await clientsApiService
                    .GetInstrumentAdjacentReadingValues(reading.Instrument.Id, readingValue.Measurement.Id, readingValue.Date);

                var previousReadingValue = adjacentReadingValues.Previous?.Quota ?? adjacentReadingValues.Previous?.MeasurementQuota ?? 0;
                var currentReadingValue = readingValue.Quota ?? readingValue.MeasurementQuota ?? 0;
                var nextReadingValue = adjacentReadingValues.Next?.Quota ?? adjacentReadingValues.Next?.MeasurementQuota ?? 0;

                var diffToPrevious = CalculateDiffToPrevious(previousReadingValue, currentReadingValue);
                var diffToNext = CalculateDiffToNext(nextReadingValue, currentReadingValue);

                var securityLevelAlerts = securityLevel
                .GetSecurityLevelAlertsFromDiff(diffToPrevious)
                .Select(securityLevel => new SecurityLevelAlertRequest()
                {
                    Difference = diffToPrevious,
                    InstrumentId = instrument.Id,
                    ReadingValueDate = readingValue.Date,
                    MeasurementId = readingValue.Measurement.Id
                });

                securityLevelAlerts = securityLevelAlerts.Concat(securityLevel
                    .GetSecurityLevelAlertsFromDiff(diffToNext)
                    .Select(securityLevel => new SecurityLevelAlertRequest()
                    {
                        Difference = diffToNext,
                        InstrumentId = instrument.Id,
                        ReadingValueDate = readingValue.Date,
                        MeasurementId = readingValue.Measurement.Id,
                        SecurityLevel = securityLevel
                    }));

                securityLevelAlertsRequest.AddRange(securityLevelAlerts);
            }

            if (!securityLevelAlertsRequest.Any())
            {
                return;
            }

            var request = new AddInstrumentSecurityLevelAlertsRequest
            {
                SecurityLevelAlerts = securityLevelAlertsRequest,
            };

            await clientsApiService.AddSecurityLevelAlert(instrument.Id, request);
        }

        private static decimal CalculateDiffToPrevious(
            decimal previousReadingValue,
            decimal currentReadingValue)
        {
            return Math.Abs(previousReadingValue - currentReadingValue);
        }

        private static decimal CalculateDiffToNext(
            decimal nextReadingValue,
            decimal currentReadingValue)
        {
            return Math.Abs(currentReadingValue - nextReadingValue);
        }
    }
}
