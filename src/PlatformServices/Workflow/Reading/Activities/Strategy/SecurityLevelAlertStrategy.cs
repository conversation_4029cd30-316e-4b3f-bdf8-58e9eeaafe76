using Application.Apis.Clients;
using Application.Apis.Clients.Response.Instrument;
using Application.Apis.Clients.Response.Reading;

namespace Workflow.Reading.Activities.Strategy
{
    internal class SecurityLevelAlertStrategy
    {
        private readonly ISecurityLevelAlertStrategy _securityLevelAlertStrategy;

        public SecurityLevelAlertStrategy(ISecurityLevelAlertStrategy securityLevelAlertStrategy)
        {
            _securityLevelAlertStrategy = securityLevelAlertStrategy;
        }

        public async Task CreateAsync(GetReadingByIdResponse reading, GetInstrumentByIdResponse getInstrumentByIdResponse, IClientsApiService clientsApiService)
        {
            await _securityLevelAlertStrategy.Create(reading, getInstrumentByIdResponse, clientsApiService);
        }
    }
}
