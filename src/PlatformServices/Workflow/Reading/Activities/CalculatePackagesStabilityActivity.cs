using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services.Models;
using Refit;
using static Workflow.Constants.ReadingWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.Reading.Activities
{
    [Activity(
        Category = "Reading",
        DisplayName = "Calculate Packages Stability",
        Description = "Will calculate the stability from the created packages.",
        Outcomes = new[] { OutcomeNames.Done }
    )]
    public sealed class CalculatePackagesStabilityActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public CalculatePackagesStabilityActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
            ActivityExecutionContext context)
        {
            try
            {
                var readingEvent = context.GetTransientVariable<Domain.Messages.Events.Reading.ReadingUpdated>(Variables.ReadingEvent);
                if (!readingEvent.ShouldRecalculateStability)
                {
                    context.JournalData.SetItem(Variables.Output, "This reading updated event is not set to recalculate stability.");
                    return Outcome(OutcomeNames.Cancel);
                }

                var packageIds = context.GetTransientVariable<IEnumerable<Guid>>(Variables.PackageIds);

                var requests = packageIds
                    .Select(id => new CalculatePackageStabilityRequest(id, readingEvent.UserId.Value))
                    .Select(request => _clientsApiService.CalculatePackageStability(request));

                var responseIds = await Task.WhenAll(requests);

                if (packageIds.Count() != responseIds.Count())
                {
                    var missingPackageIds = packageIds.Except(responseIds);

                    return Fault($"Could not calculate stability for all the packages. Missing packages: {string.Join(',', missingPackageIds)}");
                }

                context.JournalData.Add(Variables.CalculatedPackages, responseIds);

                return Outcome(OutcomeNames.Done);
            }
            catch (ApiException e)
            {
                return Fault(e.Content);
            }
            catch (Exception e)
            {
                return Fault(e);
            }
        }
    }
}
