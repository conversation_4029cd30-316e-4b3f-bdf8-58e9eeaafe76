using Application.Apis.Clients;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services;
using Elsa.Services.Models;
using Refit;
using Workflow.Reading.Activities.Strategy;
using Workflow.Reading.Activities.Strategy.Factory;
using static Workflow.Constants.ReadingWorkFlow;

namespace Workflow.Reading.Activities
{
    public class GenerateSecurityLevelAlertActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public GenerateSecurityLevelAlertActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                Guid readingId = context.GetVariable<dynamic>(Variables.ReadingEvent).Id;

                var reading = await _clientsApiService.GetReadingById(readingId);

                if (reading == null)
                {
                    return Fault("Reading not found.");
                }

                var instrument = await _clientsApiService.GetInstrumentById(reading.Instrument.Id);

                if (instrument == null)
                {
                    return Fault("Instrument not found.");
                }

                var areThereSecurityLevels =
                    instrument.Measurements.Any(x => x.SecurityLevels != null)
                    || (instrument.SecurityLevels != null && instrument.SecurityLevels.Any());

                if (!areThereSecurityLevels)
                {
                    context.JournalData.SetItem(Variables.Output, "This instrument does not have any Security Level.");
                    return Done();
                }

                var strategy = instrument.Type.GetStrategy();

                if (strategy == null)
                {
                    context.JournalData.SetItem(Variables.Output, "Could not find any suitable strategy implementation for this instrument type.");
                    return Done();
                }

                var alertStrategy = new SecurityLevelAlertStrategy(strategy);

                await alertStrategy.CreateAsync(reading, instrument, _clientsApiService);

                return Done();
            }
            catch (ApiException e)
            {
                return Fault(e.Content);
            }
            catch (Exception e)
            {
                return Fault(e);
            }
        }
    }
}
