using Elsa;
using Elsa.Activities.ControlFlow;
using Elsa.Activities.Primitives;
using Elsa.Builders;
using Workflow.Reading.Activities;
using static Workflow.Constants.ReadingWorkFlow;

namespace Workflow.Reading.RecalculateStability;

public sealed class RecalculateStabilityWorkflow : IWorkflow
{
    public void Build(IWorkflowBuilder builder)
    {
        builder
            .WithDeleteCompletedInstances(true)
            .AsSingleton()
            .WithDisplayName("Recalculate package stability from an updated reading")
            .SetTransientVariable(Variables.ReadingEvent, context => context.GetInput<Domain.Messages.Events.Reading.ReadingUpdated>())
            .Then<GetRelatedReadingValuesActivity>(activityBuilder =>
            {
                activityBuilder
                    .When(OutcomeNames.Cancel)
                    .Finish();

                activityBuilder
                    .When(OutcomeNames.Done)
                    .Then<CreatePackagesActivity>()
                    .Then<CalculatePackagesStabilityActivity>()
                    .Finish();
            });
    }
}