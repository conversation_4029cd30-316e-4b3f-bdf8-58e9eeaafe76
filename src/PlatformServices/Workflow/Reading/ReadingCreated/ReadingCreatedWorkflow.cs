using Elsa.Activities.ControlFlow;
using Elsa.Activities.MassTransit;
using Elsa.Activities.Primitives;
using Elsa.Builders;
using Workflow.Reading.Activities;
using static Workflow.Constants.ReadingWorkFlow;

namespace Workflow.Reading.ReadingCreated;

public class ReadingCreatedWorkflow : IWorkflow
{
    public void Build(IWorkflowBuilder builder)
    {
        builder
            .WithDeleteCompletedInstances(true)
            .WithDisplayName("Reading created workflow")
            .ReceiveMassTransitMessage(
                activity => activity.Set(x => x.MessageType, x => typeof(Domain.Messages.Events.Reading.ReadingCreated)))
            .WithDisplayName("Receive reading created message")
            .WithDescription("When receive the message, will check if is necessary create security levels alerts.")
            .SetTransientVariable(Variables.ReadingEvent, context => context.GetInput<Domain.Messages.Events.Reading.ReadingCreated>())
            .Then<GenerateSecurityLevelAlertActivity>()
            .WithDisplayName("Check security level")
            .WithDescription("This activity will check if is necessary create security levels alerts.")
            .Finish();
    }
}