using Elsa.Activities.ControlFlow;
using Elsa.Activities.Primitives;
using Elsa.Builders;
using Workflow.Reading.Activities;
using static Workflow.Constants.ReadingWorkFlow;

namespace Workflow.Reading.GenerateAlerts;

public sealed class GenerateAlertsWorkflow : IWorkflow
{
    public void Build(IWorkflowBuilder builder)
    {
        builder
            .WithDeleteCompletedInstances(true)
            .WithDisplayName("Generate reading alerts workflow")
            .WithDescription("When receive the message, will check if is necessary create security levels alerts.")
            .SetTransientVariable(Variables.ReadingEvent, context => context.GetInput<Domain.Messages.Events.Reading.ReadingUpdated>())
            .Then<GenerateSecurityLevelAlertActivity>()
            .WithDisplayName("Check security level")
            .WithDescription("This activity will check if is necessary create security levels alerts.")
            .Finish();
    }
}