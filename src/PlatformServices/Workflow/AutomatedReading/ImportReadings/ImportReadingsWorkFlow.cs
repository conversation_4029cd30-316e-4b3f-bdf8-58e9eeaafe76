using Application.Apis.Clients.Response.Clients;
using Application.AutomatedReading;
using CronExpressionDescriptor;
using Domain.Enums;
using Elsa;
using Elsa.Activities.ControlFlow;
using Elsa.Activities.Temporal;
using Elsa.Builders;
using Microsoft.Extensions.Options;
using Workflow.AutomatedReading.Activities;
using Workflow.AutomatedReading.Activities.Converters.AtlanticNikel;
using Workflow.AutomatedReading.Activities.Converters.Mosaic;
using static Workflow.Constants.AutomatedReadingsWorkFlow;

namespace Workflow.AutomatedReading.ImportReadings
{
    public sealed class ImportReadingsWorkFlow : IWorkflow
    {
        private readonly AutomatedReadingOptions _automatedReadingOptions;

        public ImportReadingsWorkFlow(
            IOptions<AutomatedReadingOptions> automatedReadingOptions)
        {
            _automatedReadingOptions = automatedReadingOptions.Value;
        }

        public void Build(IWorkflowBuilder builder)
        {
            var readableCronExpression = ExpressionDescriptor
                .GetDescription(_automatedReadingOptions.ImportReadingsCronExpression);

            builder
                .AsSingleton()
                .WithDisplayName("Gets readings via SFTP and sends them to the API Clients")
                .Cron(_automatedReadingOptions.ImportReadingsCronExpression)
                .WithDisplayName($"Runs {readableCronExpression}")
                .Then<GetClientConfigurationsActivity>(activityBuilder =>
                {
                    activityBuilder
                        .When(OutcomeNames.False)
                        .Finish();

                    activityBuilder
                        .When(OutcomeNames.True)
                        .ForEach(items: context => context
                            .GetTransientVariable<List<GetClientAutomatedReadingsConfigurationsResponse>>(
                                Variables.ClientConfigurations)!,
                            iterate: item =>
                            {
                                item.Then<GetPendingReadingFilesActivity>(activityBuilder =>
                                {
                                    activityBuilder
                                         .When(OutcomeNames.Cancel)
                                         .Then<CreateAutomatedReadingErrorNotificationActivity>();

                                    activityBuilder
                                        .When(OutcomeNames.True)
                                        .Switch(cases => cases
                                            .Add(context =>
                                                context.GetTransientVariable<AutomatedReadingTemplateType>(Variables.TemplateType)
                                                    == AutomatedReadingTemplateType.Mosaic,
                                                @case => @case
                                                    .Then<MosaicFileConverterActivity>()
                                                    .When(OutcomeNames.Cancel)
                                                    .Then<CreateAutomatedReadingErrorNotificationActivity>()
                                                    .Finish())
                                            .Add(context =>
                                                context.GetTransientVariable<AutomatedReadingTemplateType>(Variables.TemplateType)
                                                    == AutomatedReadingTemplateType.Vale,
                                                @case => @case
                                                    .Then<ValeFileConverterActivity>()
                                                    .When(OutcomeNames.Cancel)
                                                    .Then<CreateAutomatedReadingErrorNotificationActivity>()
                                                    .Finish())
                                            .Add(context =>
                                                context.GetTransientVariable<AutomatedReadingTemplateType>(Variables.TemplateType)
                                                    == AutomatedReadingTemplateType.AtlanticNikel,
                                                @case => @case
                                                    .Then<AtlanticNikelFileConverterActivity>()
                                                    .When(OutcomeNames.Cancel)
                                                    .Then<CreateAutomatedReadingErrorNotificationActivity>()
                                                    .Finish()))
                                        .Then<SendReadingsActivity>(activity =>
                                        {
                                            activity
                                                .When(OutcomeNames.Cancel)
                                                .Then<CreateAutomatedReadingErrorNotificationActivity>()
                                                .Finish();
                                        })
                                        .Then<MoveProcessedFilesActivity>(activity =>
                                        {
                                            activity
                                                .When(OutcomeNames.Cancel)
                                                .Then<CreateAutomatedReadingErrorNotificationActivity>()
                                                .Finish();
                                        });
                                });
                            }
                        )
                        .When(OutcomeNames.Done);
                });
        }
    }
}
