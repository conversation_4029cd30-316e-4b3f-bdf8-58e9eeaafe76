using Domain.AutomatedReading.Mosaic;

namespace Workflow.AutomatedReading.Extensions
{
    public static class MosaicCsvFileConverterExtensions
    {
        private const int CLIENT_UNIT_NAME_INDEX = 0;
        private const int STRUCTURE_NAME_INDEX = 1;
        private const int INSTRUMENT_IDENTIFIER_INDEX = 2;
        private const int READING_VALUE_DATE_INDEX = 3;
        private const int PIO_INDEX = 4;
        private const int QUOTA_INDEX = 5;
        private const int DRY_INDEX = 6;
        private const int STATUS_INDEX = 7;

        public static MosaicReading GetReading(this string[] rowValues)
        {
            return new MosaicReading
            {
                ClientUnitName = rowValues[CLIENT_UNIT_NAME_INDEX],
                StructureName = rowValues[STRUCTURE_NAME_INDEX],
                InstrumentIdentifier = rowValues[INSTRUMENT_IDENTIFIER_INDEX],
                ReadingValueDate = ParseDateTime(rowValues[READING_VALUE_DATE_INDEX]),
                Pio = ParseDecimal(rowValues[PIO_INDEX]),
                Quota = ParseDecimal(rowValues[QUOTA_INDEX]),
                Dry = ParseBoolean(rowValues[DRY_INDEX]),
                Status = rowValues[STATUS_INDEX]
            };
        }

        private static DateTime ParseDateTime(string dateString)
        {
            var dateFormat = "dd/MM/yyyy HH:mm";
            DateTime parsedDate;
            return DateTime.TryParseExact(dateString, dateFormat, null, System.Globalization.DateTimeStyles.None, out parsedDate)
                ? parsedDate
                : DateTime.UtcNow;
        }

        private static decimal ParseDecimal(string numberString)
        {
            decimal parsedNumber;
            return decimal.TryParse(numberString, out parsedNumber) ? parsedNumber : 0.0m;
        }

        private static bool ParseBoolean(string boolString)
        {
            if (bool.TryParse(boolString, out bool parsedBool))
            {
                return parsedBool;
            }

            if (boolString == "1")
            {
                return true;
            }

            if (boolString == "0")
            {
                return false;
            }

            return false;
        }
    }
}
