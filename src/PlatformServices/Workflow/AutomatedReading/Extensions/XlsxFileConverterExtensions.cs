using Application.Apis.Clients.Request;
using Application.Apis.Clients.Response.Instrument.GetAutomatedReadingsData;
using Domain.Enums;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace Workflow.AutomatedReading.Extensions
{
    public static class XlsxFileConverterExtensions
    {
        private const int PIEZOMETER_READING_DATE_COLUMN = 2; //Column index of the date in the piezometer readings worksheet
        private const int PLUVIOMETER_READING_DATE_COLUMN = 0; //Column index of the date in the pluviometer readings worksheet

        private const int PLUVIOMETER_READING_COLUMN = 1; //Column index of the reading in the pluviometer readings worksheet

        private const int INSTRUMENT_IDENTIFIERS_FILE_ROW = 0; //Row index of the instrument identifiers in the instruments readings worksheet
        private const int INSTRUMENT_IDENTIFIERS_STARTING_COLUMN = 3; //Column index of the first instrument identifier in the instruments readings worksheet

        private const int READING_IDENTIFIER_COLUMN = 1; //Column index of the cell used as worksheet type identifier (Piezometer Instruments or Pluviometer) 
        private const int READING_IDENTIFIER_ROW = 0;//Row index of the cell used as worksheet type identifier (Piezometer Instruments or Pluviometer) 

        private const string PLUVIOMETER_READING_IDENTIFIER = "Pluviometria"; //Text used to identify the worksheet with pluviometer readings
        private const string INSTRUMENTS_READING_IDENTIFIER = "Resp_Leitura"; //Text used to identify the worksheet with piozemeter instruments readings

        private const decimal PRESSURE_MULTIPLIER = 9.81M; //Multiplier used to calculate the pressure in the piezometer readings

        /// <summary>
        /// Method that gets the searched worksheet (instruments (piezometer) readings or pluviometer readings) by the text identifier
        /// </summary>
        /// <param name="workbook">Workbook</param>
        /// <param name="identifier">Text used to identify which worksheet it is</param>
        /// <returns></returns>
        public static ISheet GetReadingsWorksheetByIdentifier(
            this XSSFWorkbook workbook,
            string identifier)
        {
            var sheets = workbook.NumberOfSheets;

            for (int i = 0; i < sheets; i++)
            {
                var sheet = workbook.GetSheetAt(i);

                if (sheet.GetRow(READING_IDENTIFIER_ROW)?.GetCell(READING_IDENTIFIER_COLUMN)?.StringCellValue?.Trim() == identifier)
                {
                    return sheet;
                }
            }

            return null;
        }

        #region Instruments (piezometers)
        /// <summary>
        /// Method to search for the worksheet with the instruments/piezometer readings. Calls GetReadingsWorksheetByIdentifier method passing the text identifier of the instruments readings worksheet
        /// </summary>
        /// <param name="workbook">Workbook</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static ISheet GetInstrumentsReadingsWorksheet(
            this XSSFWorkbook workbook)
        {
            var instrumentsReadingsWorksheet = workbook
                .GetReadingsWorksheetByIdentifier(INSTRUMENTS_READING_IDENTIFIER);

            if (instrumentsReadingsWorksheet == null)
            {
                throw new Exception(
                    "Instruments readings worksheet not found");
            }

            return instrumentsReadingsWorksheet;
        }

        /// <summary>
        /// Gets the reading date of the instrument reading given the current row of the worksheet
        /// </summary>
        /// <param name="worksheet">Worksheet with instruments readings</param>
        /// <param name="currentRow">Current row being processed</param>
        /// <returns></returns>
        public static DateTime? GetInstrumentReadingDate(
            this ISheet worksheet,
            int currentRow)
        {
            var readingDate =
                    worksheet
                    .GetRow(currentRow)?.GetCell(PIEZOMETER_READING_DATE_COLUMN)
                    ?.DateCellValue;

            return readingDate;
        }

        /// <summary>
        /// Method to get the instruments identifiers (names) from the instruments readings worksheet
        /// </summary>
        /// <param name="worksheet">Worksheet with instruments readings</param>
        /// <returns></returns>
        public static List<(string, int)> GetInstrumentsIdentifiers(
            this ISheet worksheet)
        {
            var instrumentIdentifiers = new List<(string, int)>();

            var row = worksheet.GetRow(INSTRUMENT_IDENTIFIERS_FILE_ROW);

            foreach (var cell in row)
            {
                if (cell.ColumnIndex < INSTRUMENT_IDENTIFIERS_STARTING_COLUMN)
                {
                    continue;
                }

                if (string.IsNullOrWhiteSpace(cell.StringCellValue))
                {
                    break;
                }

                instrumentIdentifiers.Add((cell.StringCellValue.Replace(" ", ""), cell.ColumnIndex));
            }

            return instrumentIdentifiers;
        }

        /// <summary>
        /// Method to get the readings of each instrument in the current row being processed in the worksheet with instruments readings and generate the AddReadingRequest object.
        /// </summary>
        /// <param name="worksheet">Worksheet with instruments readings</param>
        /// <param name="readingDate">Date of the instrument reading</param>
        /// <param name="instrumentIdentifiers"></param>
        /// <param name="additionalInstrumentData"></param>
        /// <param name="currentRow"></param>
        /// <returns></returns>
        public static List<AddReadingRequest> GetInstrumentsReadings(
            this ISheet worksheet,
            DateTime readingDate,
            List<(string Identifier, int Column)> instrumentIdentifiers,
            IEnumerable<GetAutomatedReadingsDataResponse> additionalInstrumentData,
            int currentRow)
        {
            var newReadings = new List<AddReadingRequest>();

            var row = worksheet.GetRow(currentRow);

            foreach (var cell in row)
            {
                if (cell.ColumnIndex < INSTRUMENT_IDENTIFIERS_STARTING_COLUMN)
                {
                    continue;
                }
                var instrumentIdentifier = instrumentIdentifiers
                    .FirstOrDefault(x => x.Column == cell.ColumnIndex);

                var additionalData = additionalInstrumentData
                        .FirstOrDefault(i => i.Instrument.Identifier == instrumentIdentifier.Identifier);

                if (additionalData == null)
                {
                    continue;
                }

                var readingNew = additionalData.Instrument.GenerateAddReadingRequest();

                decimal? reading = null;
                try
                {
                    reading = Convert.ToDecimal(cell.NumericCellValue);
                }
                catch
                {
                }

                if (reading == null)
                {
                    continue;
                }

                var readingExtracted = additionalData
                    .ExtractReading(reading);

                readingNew.Values.Add(
                    additionalData.GetReadingValueByInstrumentType(
                        readingExtracted,
                        readingDate));

                newReadings.Add(
                    readingNew);
            }

            return newReadings;
        }

        #endregion

        #region Pluviometer
        /// <summary>
        /// Gets the pluviometer readings worksheet by the text identifier
        /// </summary>
        /// <param name="workbook">Workbook</param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static ISheet GetPluviometerReadingsWorksheet(
    this XSSFWorkbook workbook)
        {
            var pluviometerReadingsWorksheet = workbook
                .GetReadingsWorksheetByIdentifier(PLUVIOMETER_READING_IDENTIFIER);

            if (pluviometerReadingsWorksheet == null)
            {
                throw new Exception(
                    "Pluviometer readings worksheet not found");
            }

            return pluviometerReadingsWorksheet;
        }

        /// <summary>
        /// Method that gets the pluviometer readings in the worksheet, generates and returns the AddReadingRequest list and the last processed row.
        /// </summary>
        /// <param name="worksheet">Worksheet with pluviometer readings</param>
        /// <param name="startingRow">First row to be processed</param>
        /// <param name="instrumentAdditionalData">Pluviometer instrument additional data</param>
        /// <returns></returns>
        public static (List<AddReadingRequest>, int lastRowProcessed) GetPluviometerReadings(
            this ISheet worksheet,
            int startingRow,
            GetAutomatedReadingsDataResponse instrumentAdditionalData)
        {
            var readings = new List<AddReadingRequest>();

            int lastRowProcessed = startingRow;

            foreach (IRow row in worksheet)
            {
                if (row.RowNum < startingRow)
                    continue;

                try
                {
                    var readingValue = Convert.ToDecimal(
                        row.GetCell(PLUVIOMETER_READING_COLUMN)?.NumericCellValue
                        );

                    var readingDate =
                        row.GetCell(PLUVIOMETER_READING_DATE_COLUMN)
                        ?.DateCellValue;

                    if (readingDate == null)
                    {
                        continue;
                    }

                    var readingNew = instrumentAdditionalData.Instrument.GenerateAddReadingRequest();

                    var readingExtracted = instrumentAdditionalData
                        .ExtractReading(readingValue);

                    readingNew.Values.Add(
                        instrumentAdditionalData.GetReadingValueByInstrumentType(
                            readingExtracted,
                            readingDate.Value));

                    readings.Add(readingNew);

                    lastRowProcessed = row.RowNum;
                }
                catch
                {
                }
            }

            return (readings, lastRowProcessed);
        }

        #endregion

        /// <summary>
        /// Method to extract and format the quota reading based on the instrument type
        /// </summary>
        /// <param name="additionalData">Instrument additional data</param>
        /// <param name="quota">Quota read from the worksheet</param>
        /// <returns></returns>
        /// <exception cref="NotSupportedException"></exception>
        public static decimal? ExtractReading(
            this GetAutomatedReadingsDataResponse additionalData,
            decimal? quota)
        {
            return additionalData.Instrument.Type switch
            {
                InstrumentType.WaterLevelIndicator
                or InstrumentType.OpenStandpipePiezometer =>
                    quota > additionalData.Instrument.BaseQuota
                    ? quota
                    : null,
                InstrumentType.Pluviometer => quota,
                _ => throw new NotSupportedException("The selected instrument type is not supported.")
            };
        }

        /// <summary>
        /// Method to generate the ReadingValueRequest object based on the instrument type and reading
        /// </summary>
        /// <param name="additionalData">Instrument additional data</param>
        /// <param name="quota">Quota read from the worksheet</param>
        /// <param name="readingDate">Date of the reading</param>
        /// <returns></returns>
        /// <exception cref="NotSupportedException"></exception>
        public static ReadingValueRequest GetReadingValueByInstrumentType(
            this GetAutomatedReadingsDataResponse additionalData,
            decimal? quota,
            DateTime readingDate)
        {
            var isDry = quota == null;

            var readingValue = new ReadingValueRequest
            {
                Date = readingDate,
            };

            switch (additionalData.Instrument.Type)
            {
                case InstrumentType.WaterLevelIndicator:
                case InstrumentType.OpenStandpipePiezometer:
                    {
                        readingValue.Dry = isDry;
                        readingValue.Quota = quota;

                        if (!isDry)
                        {
                            readingValue.Depth = additionalData.Instrument.TopQuota - quota;
                            readingValue.Pressure = (quota - additionalData.Instrument.BaseQuota) * PRESSURE_MULTIPLIER;
                        }
                    }
                    break;
                case InstrumentType.Pluviometer:
                    {
                        readingValue.Pluviometry = quota;
                    }
                    break;
                default:
                    throw new NotSupportedException("The selected instrument type is not supported.");
            }

            return readingValue;
        }
    }
}
