using Application.Apis.Clients.Request;
using Coordinate.Core.Enums;
using Domain.AutomatedReading.AtlanticNikel;

namespace Workflow.AutomatedReading.Extensions
{
    public static class AtlanticNickelCsvFileConverterExtensions
    {
        private const int EVENT_TIME_INDEX = 0;
        private const int POINT_NAME_INDEX = 1;
        private const int NORTHING_INDEX = 2;
        private const int EASTING_INDEX = 3;
        private const int ELEVATION_INDEX = 4;
        private const int REFERENCE_NORTHING_INDEX = 5;
        private const int REFERENCE_EASTING_INDEX = 6;
        private const int REFERENCE_ELEVATION_INDEX = 7;
        private const int UNIQUE_POINT_NAME_INDEX = 8;

        private const decimal MULTIPLIER = 1000;
        private const double INTERNAL_POWER = 2;
        private const double EXTERNAL_POWER = 0.5;
        private const int DECIMALS = 6;

        public static SurfaceLandmarkReading GetSurfaceLandmarkReading(
            this string[] rowValues)
        {
            return new SurfaceLandmarkReading(
                ReadingDate: rowValues[EVENT_TIME_INDEX].GetReadingValue(),
                InstrumentIdentifier: rowValues[POINT_NAME_INDEX],
                NorthCoordinate: rowValues[NORTHING_INDEX].ToDecimal(),
                EastCoordinate: rowValues[EASTING_INDEX].ToDecimal(),
                Quota: rowValues[ELEVATION_INDEX].ToDecimal(),
                ReferentialNorthCoordinate: rowValues[REFERENCE_NORTHING_INDEX].ToDecimal(),
                ReferentialEastCoordinate: rowValues[REFERENCE_EASTING_INDEX].ToDecimal(),
                ReferentialQuota: rowValues[REFERENCE_ELEVATION_INDEX].ToDecimal(),
                UniqueInstrumentName: rowValues[UNIQUE_POINT_NAME_INDEX]);
        }

        public static DateTime GetReadingValue(
            this string eventTime)
        {
            if (eventTime == null)
            {
                throw new ArgumentNullException(
                    nameof(eventTime),
                    "Csv EventTime field value is null");
            }

            return DateTime.Parse(eventTime);
        }

        public static decimal ToDecimal(
            this string value)
        {
            if (value == null)
            {
                throw new ArgumentNullException(
                    nameof(value),
                    "Csv field value is null");
            }

            _ = decimal.TryParse(value, out decimal result);

            return result;
        }

        public static decimal CalculateDisplacement(
            decimal readingValue,
            decimal? referentialValue)
        {
            if (!referentialValue.HasValue)
            {
                return 0;
            }

            return (readingValue - referentialValue.Value) * MULTIPLIER;
        }

        public static decimal CalculateZDisplacement(
            decimal readingQuota,
            decimal? referentialQuota)
        {
            if (!referentialQuota.HasValue)
            {
                return 0;
            }

            return (readingQuota - referentialQuota.Value);
        }

        public static decimal CalculateTotalPlanimetricDisplacement(
            double northDisplacement,
            double eastDisplacement)
        {
            var sum = Math.Pow(northDisplacement, INTERNAL_POWER)
                + Math.Pow(eastDisplacement, INTERNAL_POWER);

            return (decimal)Math.Pow(sum, EXTERNAL_POWER);
        }

        public static decimal? CalculateADisplacement(
            decimal northDisplacement,
            decimal eastDisplacement,
            double? azimuth)
        {
            if (!azimuth.HasValue)
            {
                return default;
            }

            var radAzimuth = azimuth.Value.DegToRad();

            var sinAzimuth = (decimal)Math.Sin(radAzimuth);
            var cosAzimuth = (decimal)Math.Cos(radAzimuth);

            var result = (decimal)
                ((northDisplacement * cosAzimuth)
                + (eastDisplacement * sinAzimuth));

            return Math.Round(result, DECIMALS);
        }

        public static decimal? CalculateBDisplacement(
            decimal northDisplacement,
            decimal eastDisplacement,
            double? azimuth)
        {
            if (!azimuth.HasValue)
            {
                return default;
            }

            var radAzimuth = azimuth.Value.DegToRad();

            var sinAzimuth = (decimal)Math.Sin(radAzimuth);
            var cosAzimuth = (decimal)Math.Cos(radAzimuth);

            var result = (decimal)
                (-(northDisplacement * sinAzimuth)
                + (eastDisplacement * cosAzimuth));

            return Math.Round(result, DECIMALS);
        }

        public static double DegToRad(
            this double deg)
        {
            return deg * Math.PI / 180.0;
        }

        public static ReadingValueRequest GenerateReadingValue(
            this SurfaceLandmarkReading surfaceLandmark,
            double? azimuth)
        {
            var eastDisplacement = CalculateDisplacement(surfaceLandmark.EastCoordinate, surfaceLandmark.ReferentialEastCoordinate);
            var northDisplacement = CalculateDisplacement(surfaceLandmark.NorthCoordinate, surfaceLandmark.ReferentialNorthCoordinate);
            var totalPlanimetricDisplacement = CalculateTotalPlanimetricDisplacement((double)northDisplacement, (double)eastDisplacement);

            var zDisplacement = CalculateZDisplacement(surfaceLandmark.Quota, surfaceLandmark.ReferentialQuota);
            var aDisplacement = CalculateADisplacement(northDisplacement, eastDisplacement, azimuth);
            var bDisplacement = CalculateBDisplacement(northDisplacement, eastDisplacement, azimuth);

            return new ReadingValueRequest
            {
                Date = surfaceLandmark.ReadingDate,
                Datum = Datum.SIRGAS2000,
                Quota = surfaceLandmark.Quota,
                EastCoordinate = surfaceLandmark.EastCoordinate,
                NorthCoordinate = surfaceLandmark.NorthCoordinate,
                EastDisplacement = eastDisplacement,
                NorthDisplacement = northDisplacement,
                ZDisplacement = zDisplacement,
                TotalPlanimetricDisplacement = totalPlanimetricDisplacement,
                ADisplacement = aDisplacement,
                BDisplacement = bDisplacement
            };
        }
    }
}