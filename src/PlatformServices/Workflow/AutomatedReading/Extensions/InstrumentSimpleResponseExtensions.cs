using Application.Apis.Clients.Request;
using Application.Apis.Clients.Response.Instrument.GetAutomatedReadingsData;

namespace Workflow.AutomatedReading.Extensions
{
    public static class InstrumentSimpleResponseExtensions
    {
        public static AddReadingRequest GenerateAddReadingRequest(
            this InstrumentSimpleResponse instrument,
            bool? isReferencial = null)
        {
            return new AddReadingRequest
            {
                Instrument = new InstrumentRequest
                {
                    Id = instrument.Id,
                    Identifier = instrument.Identifier,
                    Type = instrument.Type
                },
                IsReferential = isReferencial,
                IsAutomated = true
            };
        }
    }
}
