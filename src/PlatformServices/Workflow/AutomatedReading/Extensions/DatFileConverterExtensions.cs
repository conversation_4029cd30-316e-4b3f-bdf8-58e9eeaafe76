using Application.Apis._Shared.Instrument;
using Application.Apis.Clients.Request;
using Application.Apis.Clients.Response.Instrument.GetAutomatedReadingsData;
using Application.Sftp.Model.Response;
using Domain.Enums;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace Workflow.AutomatedReading.Extensions
{
    public static class DatFileConverterExtensions
    {
        private const int READING_DATE_INDEX = 0;
        private const int INSTRUMENT_QUOTA_INDEX = 2;
        private const int INSTRUMENT_IDENTIFIERS_FILE_ROW = 1;
        private const int INSTRUMENT_IDENTIFIERS_FILE_COLUMN = 2;
        private const char COMMA_SEPARATOR = ',';
        private const char CARRIAGE_RETURN = '\r';
        private const char LINE_BREAK = '\n';
        private const char UNDERSCORE = '_';
        private const char DOUBLE_QUOTE = '"';
        private const string COMMA_WITHIN_QUOTES_PATTERN = "(\"[0-9]+),([0-9]+\")";
        private const string REPLACEMENT_FOR_COMMA_WITHIN_QUOTES = "$1.$2";
        private const decimal PRESSURE_MULTIPLIER = 9.81M;

        public static string[] GetAllRows(
            this DownloadFileResponse downloadFile)
        {
            return downloadFile?
                .FileContentBytes?
                .ConvertToString()?
                .Trim()
                .Split(LINE_BREAK);
        }

        public static List<string> GetRowsToProcess(
            this string[] rowsFile,
            int lastReadRowNumber)
        {
            return rowsFile
                [lastReadRowNumber..]
                .ToList();
        }

        public static List<string> GetInstrumentIdentifiers(
            this string[] rowsFile)
        {
            return rowsFile
                // Gets the row where the instrument identifiers are located
                .ElementAt(INSTRUMENT_IDENTIFIERS_FILE_ROW)
                .Trim(CARRIAGE_RETURN)
                // Splits the text into parts using the separator
                .Split(COMMA_SEPARATOR)
                // Gets the items starting from the third item
                [INSTRUMENT_IDENTIFIERS_FILE_COLUMN..]
                // Retrieves the identifier after the underscore
                .Select(item => item
                    .Substring((item.IndexOf(UNDERSCORE) + 1))
                    .Trim(DOUBLE_QUOTE))
                .ToList();
        }

        public static string[] GetRowValues(
            this string row)
        {
            var result = Regex.Replace(
                row,
                COMMA_WITHIN_QUOTES_PATTERN,
                REPLACEMENT_FOR_COMMA_WITHIN_QUOTES);

            return result
                .Trim(CARRIAGE_RETURN)
                .Split(COMMA_SEPARATOR)
                .Select(item => item.Trim(DOUBLE_QUOTE))
                .ToArray();
        }

        public static DateTime GetReadingDate(
            this string[] rowValues)
        {
            _ = DateTime.TryParse(
                rowValues.ElementAt(READING_DATE_INDEX),
                out DateTime result);

            return result;
        }

        public static IEnumerable<(string Identifier, string Quota)> CombineInstrumentIdentifiersAndQuotas(
            this string[] rowValues,
            List<string> identifiers)
        {
            var quotas = rowValues
                [INSTRUMENT_QUOTA_INDEX..]
                .ToList();

            return identifiers
                .Zip(quotas);
        }

        public static decimal? ExtractQuota(
            this GetAutomatedReadingsDataResponse additionalData,
            string quota)
        {
            _ = decimal.TryParse(quota, out decimal quotaConverted);

            return additionalData.Instrument.Type switch
            {
                InstrumentType.WaterLevelIndicator
                or InstrumentType.OpenStandpipePiezometer =>
                    quotaConverted > additionalData.Instrument.BaseQuota
                    ? quotaConverted
                    : null,
                InstrumentType.ElectricPiezometer => quotaConverted,
                _ => throw new NotSupportedException("The selected instrument type is not supported.")
            };
        }

        public static ReadingValueRequest GenerateReadingValueByInstrumentType(
            this GetAutomatedReadingsDataResponse additionalData,
            decimal? quota,
            DateTime readingDate)
        {
            var isDry = quota == null;

            var readingValue = new ReadingValueRequest
            {
                Date = readingDate,
                Dry = isDry,
                Quota = quota,
            };

            switch (additionalData.Instrument.Type)
            {
                case InstrumentType.WaterLevelIndicator:
                case InstrumentType.OpenStandpipePiezometer:
                    {
                        if (!isDry)
                        {
                            readingValue.Depth = additionalData.Instrument.TopQuota - quota;
                            readingValue.Pressure = (quota - additionalData.Instrument.BaseQuota) * PRESSURE_MULTIPLIER;
                        }
                    }
                    break;
                case InstrumentType.ElectricPiezometer:
                    {
                        readingValue.Measurement = new()
                        {
                            Id = additionalData.Data.First().Id,
                            Identifier = additionalData.Data.First().Identifier
                        };
                    }
                    break;
                default:
                    throw new NotSupportedException("The selected instrument type is not supported.");
            }

            return readingValue;
        }
    }
}
