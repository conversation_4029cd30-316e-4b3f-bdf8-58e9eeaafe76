using Application.Apis.Clients.Response.Clients;
using Application.Sftp;
using Application.Sftp.Model.Request;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services.Models;
using NPOI.SS.Formula.Functions;
using static Workflow.Constants.AutomatedReadingsWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.AutomatedReading.Activities
{
    [Activity(
        Category = "Automated Readings",
        DisplayName = "Get pending reading files",
        Description = "Get pending reading files from each client on SFTP server",
        Outcomes = new[] { OutcomeNames.True, OutcomeNames.False }
    )]
    public class GetPendingReadingFilesActivity : Activity
    {
        private readonly ISftpService _sftpService;

        public GetPendingReadingFilesActivity(
            ISftpService sftpService)
        {
            _sftpService = sftpService;
        }
        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
            ActivityExecutionContext context)
        {
            try
            {
                var clientConfiguration = context
                    .GetInput<GetClientAutomatedReadingsConfigurationsResponse>();

                var filesLastFetchDate = context.GetTransientVariable<DateTime>(
                    name: Variables.FilesLastFetchDate);

                var filesResult = await _sftpService.GetAllFilesAsync(
                    new GetAllFilesRequest(
                       FilesLastFetchDate: filesLastFetchDate,
                       SourcePaths: clientConfiguration.FtpRootPath.GetSourcePaths()));

                if (!filesResult.IsSuccessful)
                {
                    context.JournalData.Add(
                        key: "Error getting files",
                        value: filesResult);

                    context.SetTransientVariable(Variables.ErrorMessage, $"Ocorreu um erro ao obter os arquivos do servidor SFTP.");
                    return Outcome(OutcomeNames.Cancel);
                }

                var downloadedFilesResult = await _sftpService.BulkDownloadFilesAsync(
                    new DownloadFilesRequest(
                        Files: filesResult.Data.Files));

                if (!downloadedFilesResult.IsSuccessful)
                {
                    context.JournalData.Add(
                        key: "Error downloading files",
                        value: downloadedFilesResult);

                    context.SetTransientVariable(Variables.ErrorMessage, $"Ocorreu um erro ao obter baixar os arquivos do servidor SFTP.");
                    return Outcome(OutcomeNames.Cancel);
                }

                context.SetTransientVariable(
                    name: Variables.DownloadedFiles,
                    value: downloadedFilesResult.Data);

                context.SetTransientVariable(
                    name: Variables.TemplateType,
                    value: clientConfiguration.TemplateType);

                return Outcome(OutcomeNames.True);
            }
            catch (Exception e)
            {
                Log.Equals(e, "Error in GetPendingReadingFilesActivity");
                context.SetTransientVariable(Variables.ErrorMessage, $"Ocorreu um erro inesperado ao obter os arquivos para processar no servidor SFTP.");
                return Outcome(OutcomeNames.Cancel);
            }
        }
    }
}
