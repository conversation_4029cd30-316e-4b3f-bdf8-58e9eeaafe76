using Application.Apis.Clients.Response.Clients;
using Application.Sftp;
using Application.Sftp.Model._Shared;
using Application.Sftp.Model.Request;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services.Models;
using Serilog;
using static Workflow.Constants.AutomatedReadingsWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.AutomatedReading.Activities
{
    [Activity(
        Category = "Automated Readings",
        DisplayName = "Move processed files",
        Description = "Moves successfully processed files to a designated folder on the SFTP server.",
        Outcomes = new[] { OutcomeNames.Done }
    )]
    public sealed class MoveProcessedFilesActivity : Activity
    {
        private readonly ISftpService _sftpService;

        public MoveProcessedFilesActivity(
            ISftpService sftpService)
        {
            _sftpService = sftpService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
            ActivityExecutionContext context)
        {
            try
            {
                var filesConvertedToReadings = context.GetTransientVariable<List<FileInformation>>(
                    name: Variables.FilesConvertedToReadings);

                if (filesConvertedToReadings == null || !filesConvertedToReadings.Any())
                {
                    context.SetTransientVariable(Variables.ErrorMessage, $"Nenhum arquivo foi encontrado para mover no servidor SFTP.");
                    return Outcome(OutcomeNames.Cancel);
                }

                var clientConfiguration = context
                    .GetInput<GetClientAutomatedReadingsConfigurationsResponse>();

                var filesLastFetchDate = context.GetTransientVariable<DateTime>(
                    name: Variables.FilesLastFetchDate);

                var processedFullPath = string.Format(
                    SftpProcessedFolderName,
                    clientConfiguration.FtpRootPath.Root);

                var moveFilesResult = await _sftpService.MoveFilesAsync(
                    new MoveFilesRequest(
                        FilesLastFetchDate: filesLastFetchDate,
                        SourcePaths: clientConfiguration.FtpRootPath.GetSourcePaths(),
                        DestinationPath: processedFullPath,
                        Files: filesConvertedToReadings));

                if (!moveFilesResult.IsSuccessful)
                {
                    context.JournalData.Add(
                        key: "Error moving converted files to another folder",
                        value: moveFilesResult);

                    context.SetTransientVariable(Variables.ErrorMessage, $"As leituras foram inseridas mas não foi possível mover os arquivos no servidor SFTP.");
                    return Outcome(OutcomeNames.Cancel);
                }

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Error in MoveProcessedFilesActivity");
                context.SetTransientVariable(Variables.ErrorMessage, $"Ocorreu um erro inesperado ao mover os arquivos no servidor SFTP.");
                return Outcome(OutcomeNames.Cancel);
            }
        }
    }
}
