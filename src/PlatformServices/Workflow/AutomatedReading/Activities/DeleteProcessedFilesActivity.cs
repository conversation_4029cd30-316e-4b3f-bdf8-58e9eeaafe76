using Application.Apis.Clients.Response.Clients;
using Application.AutomatedReading;
using Application.Sftp;
using Application.Sftp.Model.Request;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services.Models;
using Microsoft.Extensions.Options;
using static Workflow.Constants.AutomatedReadingsWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.AutomatedReading.Activities
{
    [Activity(
        Category = "Automated Readings",
        DisplayName = "Delete processed files",
        Description = "Deletes previously processed files..",
        Outcomes = new[] { OutcomeNames.Done }
    )]
    public sealed class DeleteProcessedFilesActivity : Activity
    {
        private readonly ISftpService _sftpService;
        private readonly AutomatedReadingOptions _automatedReadingOptions;

        public DeleteProcessedFilesActivity(
            ISftpService sftpService,
            IOptions<AutomatedReadingOptions> automatedReadingOptions)
        {
            _sftpService = sftpService;
            _automatedReadingOptions = automatedReadingOptions.Value;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
            ActivityExecutionContext context)
        {
            try
            {
                var clientConfiguration = context
                    .GetInput<GetClientAutomatedReadingsConfigurationsResponse>();

                var processedFullPath = string.Format(
                    SftpProcessedFolderName,
                    clientConfiguration.FtpRootPath.Root);

                var deleteFilesResult = await _sftpService.DeleteFilesAsync(
                    new DeleteFilesRequest(
                        SftpFilesPurgeDate: DateTime.UtcNow.AddDays(
                            -_automatedReadingOptions.SftpFilesPurgeDays),
                        SourcePath: processedFullPath));

                if (!deleteFilesResult.IsSuccessful)
                {
                    context.JournalData.Add(
                        key: "Error deleting files",
                        value: deleteFilesResult);
                    return Fault("Failed to delete files");
                }

                return Done();
            }
            catch (Exception exception)
            {
                return Fault(exception);
            }
        }
    }
}
