using Application.Apis.Clients;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services.Models;
using static Workflow.Constants.AutomatedReadingsWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.AutomatedReading.Activities
{
    [Activity(
        Category = "Automated Readings",
        DisplayName = "Get active client automated readings configurations",
        Description = "Get the active configurations to search for files on the SFTP server",
        Outcomes = new[] { OutcomeNames.True, OutcomeNames.False }
    )]
    public sealed class GetClientConfigurationsActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public GetClientConfigurationsActivity(
            IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
            ActivityExecutionContext context)
        {
            try
            {
                var clientConfigurations = await _clientsApiService
                    .GetEnabledClientAutomatedReadingsConfigurationsAsync();

                if (clientConfigurations == null || !clientConfigurations.Any())
                {
                    return Outcome(OutcomeNames.False);
                }

                context.SetTransientVariable(
                    name: Variables.ClientConfigurations,
                    value: clientConfigurations);

                context.SetTransientVariable(
                    name: Variables.FilesLastFetchDate,
                    value: DateTime.UtcNow);

                return Outcome(OutcomeNames.True);
            }
            catch (Exception exception)
            {
                return Fault(exception);
            }
        }
    }
}
