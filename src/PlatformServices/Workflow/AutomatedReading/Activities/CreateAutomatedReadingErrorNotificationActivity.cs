using Application.Apis.Clients.Response.Clients;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Serilog;
using Activity = Elsa.Services.Activity;
using static Workflow.Constants.AutomatedReadingsWorkFlow;
using Application.Apis.Clients;
using Domain.Enums;

namespace Workflow.AutomatedReading.Activities
{
    public class CreateAutomatedReadingErrorNotificationActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public CreateAutomatedReadingErrorNotificationActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var errorMessage = context.GetTransientVariable<string>(Variables.ErrorMessage);

                if (string.IsNullOrEmpty(errorMessage))
                {
                    return Done();
                }

                var clientConfiguration = context
                    .GetInput<GetClientAutomatedReadingsConfigurationsResponse>();

                if (clientConfiguration == null || clientConfiguration.ClientId == Guid.Empty)
                {
                    return Done();
                }

                var notificationMessage = $"Erro na importação das leituras automáticas do cliente {clientConfiguration.ClientName}: {errorMessage}";

                var notificationExists = await _clientsApiService.CheckIfNotificationExists(new()
                {
                    Message = notificationMessage,
                    Theme = NotificationTheme.AutomatedReading,
                    StartDate = DateTime.UtcNow.AddHours(-12),
                    EndDate = DateTime.UtcNow
                });

                if (notificationExists)
                {
                    return Done();
                }

                var users = await _clientsApiService.GetUsersAsync(new()
                {
                    ClientId = clientConfiguration.ClientId,
                    Active = true,
                    Roles = new()
                    {
                        (int)Role.SuperSupport,
                        (int)Role.Support,
                        (int)Role.SuperAdministrator,
                        (int)Role.Administrator
                    }
                });

                if (users == null || !users.Any())
                {
                    return Done();
                }

                var result = await _clientsApiService.AddNotification(new()
                {
                    Users = users.Select(x => x.Id).ToList(),
                    NotificationMessage = notificationMessage,
                    NotificationTheme = NotificationTheme.AutomatedReading
                });

                if (result == null || !result.IsSuccessStatusCode)
                {
                    return Fault("Error creating notification");
                }

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Error in CreateAutomatedReadingErrorNotificationActivity");
                return Fault(e);
            }
        }
    }
}
