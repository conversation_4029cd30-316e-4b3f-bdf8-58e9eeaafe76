using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Application.Apis.Clients.Request.Instrument.GetAutomatedReadingsData;
using Application.Sftp.Model._Shared;
using Application.Sftp.Model.Response;
using Database.Repositories.AutomatedReadingVariable;
using System.Text.Json;
using Workflow.AutomatedReading.Extensions;
using static Workflow.Constants.AutomatedReadingsWorkFlow;

namespace Workflow.AutomatedReading.Activities.Converters.AtlanticNikel.Strategy
{
    public class DatFileConverterStrategy : IFileConverterStrategy
    {
        private readonly IClientsApiService _clientsApiService;
        private readonly IAutomatedReadingVariableRepository _automatedReadingVariableRepository;

        public DatFileConverterStrategy(
            IClientsApiService clientsApiService,
            IAutomatedReadingVariableRepository automatedReadingVariableRepository)
        {
            _clientsApiService = clientsApiService;
            _automatedReadingVariableRepository = automatedReadingVariableRepository;
        }

        public async Task<FileConverterResult> ExecuteAsync(
            List<DownloadFileResponse> downloadFiles,
            Guid clientId,
            Guid structureId)
        {
            var downloadFile = downloadFiles?
                .FirstOrDefault();
            var rowsFile = downloadFile
                .GetAllRows();

            if (rowsFile == null || !rowsFile.Any())
            {
                return new FileConverterResult
                {
                    Message = "File is empty",
                    FilesConvertedToReadings = new List<FileInformation>() { downloadFile.File }
                };
            }

            var lastRowNumber = await _automatedReadingVariableRepository
                .GetVariableValueAsync<int>(
                    Variables.AtlanticNickel.LastRowNumberDatProcessed,
                    clientId);
            if (lastRowNumber == 0)
            {
                return new FileConverterResult
                {
                    Message = $"The variable: {nameof(Variables.AtlanticNickel.LastRowNumberDatProcessed)} was not found"
                };
            }

            var rowsToProcess = rowsFile
                .GetRowsToProcess(lastRowNumber);
            if (!rowsToProcess.Any())
            {
                return new FileConverterResult
                {
                    Message = "The file does not contain new data to be processed",
                    FilesConvertedToReadings = new List<FileInformation>() { downloadFile.File }
                };
            }

            var instrumentIdentifiers = rowsFile
                .GetInstrumentIdentifiers();

            var request = new GetAutomatedReadingsDataRequest
            {
                InstrumentIdentifiers = instrumentIdentifiers,
                StructureId = structureId
            };

            var additionalInstrumentData = await _clientsApiService
                .GetAutomatedReadingsDataAsync(request);
            if (additionalInstrumentData == null || !additionalInstrumentData.Any())
            {
                return new FileConverterResult
                {
                    Message = $"No additional information was found for the instruments in the clients API - " +
                    $"Request: {JsonSerializer.Serialize(request)}"
                };
            }

            var readingsToBeSent = new List<AddReadingRequest>();

            foreach (var row in rowsToProcess)
            {
                var rowValues = row
                    .GetRowValues();

                var readingDate = rowValues
                    .GetReadingDate();

                var instrumentWithQuotas = rowValues
                    .CombineInstrumentIdentifiersAndQuotas(instrumentIdentifiers);

                foreach (var instrument in instrumentWithQuotas)
                {
                    var additionalData = additionalInstrumentData
                        .FirstOrDefault(i => i.Instrument.Identifier == instrument.Identifier);
                    if (additionalData == null)
                    {
                        continue;
                    }

                    if (string.IsNullOrWhiteSpace(instrument.Quota))
                    {
                        continue;
                    }

                    var quotaExtracted = additionalData
                        .ExtractQuota(instrument.Quota);

                    var readingNew = additionalData.Instrument
                        .GenerateAddReadingRequest();

                    readingNew.Values.Add(
                        additionalData.GenerateReadingValueByInstrumentType(
                            quotaExtracted,
                            readingDate));

                    readingsToBeSent.Add(readingNew);
                }
            }

            var lastRowExecuted = lastRowNumber + rowsToProcess.Count();
            await _automatedReadingVariableRepository.InsertAsync(
                new Domain.Entities.AutomatedReadingVariable
                {
                    ClientId = clientId,
                    VariableName = Variables.AtlanticNickel.LastRowNumberDatProcessed,
                    VariableValue = lastRowExecuted.ToString()
                });

            return new FileConverterResult
            {
                ReadingsToBeSent = readingsToBeSent,
                FilesConvertedToReadings =
                    new List<FileInformation>() { downloadFile.File }
            };
        }
    }
}