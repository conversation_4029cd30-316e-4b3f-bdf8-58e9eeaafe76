using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Application.Apis.Clients.Request.Instrument.GetAutomatedReadingsData;
using Application.Sftp.Model._Shared;
using Application.Sftp.Model.Response;
using Database.Repositories.AutomatedReadingVariable;
using Domain.AutomatedReading.AtlanticNikel;
using System.Text;
using System.Text.Json;
using Workflow.AutomatedReading.Extensions;
using static Workflow.Constants.AutomatedReadingsWorkFlow;

namespace Workflow.AutomatedReading.Activities.Converters.AtlanticNikel.Strategy
{
    public class CsvFileConverterStrategy : IFileConverterStrategy
    {
        private readonly IClientsApiService _clientsApiService;
        private readonly IAutomatedReadingVariableRepository _automatedReadingVariableRepository;

        public CsvFileConverterStrategy(
            IClientsApiService clientsApiService,
            IAutomatedReadingVariableRepository automatedReadingVariableRepository)
        {
            _clientsApiService = clientsApiService;
            _automatedReadingVariableRepository = automatedReadingVariableRepository;
        }

        public async Task<FileConverterResult> ExecuteAsync(
            List<DownloadFileResponse> files,
            Guid clientId,
            Guid structureId)
        {
            var downloadFile = files.FirstOrDefault();
            if (downloadFile?.FileContentBytes == null
                || downloadFile.FileContentBytes.Length == 0)
            {
                return new FileConverterResult
                {
                    Message = "File is empty",
                    FilesConvertedToReadings = new List<FileInformation>() { downloadFile.File }
                };
            }

            var lastRowNumberProcessed = await _automatedReadingVariableRepository
                .GetVariableValueAsync<int>(
                    Variables.AtlanticNickel.LastRowNumberCsvProcessed,
                    clientId);
            if (lastRowNumberProcessed == 0)
            {
                return new FileConverterResult
                {
                    Message = $"The variable: {nameof(Variables.AtlanticNickel.LastRowNumberCsvProcessed)} was not found"
                };
            }

            int currentRowReadFromFile = 0;
            var surfaceLandmarksToProcess = new List<SurfaceLandmarkReading>();

            using (var memoryStream = new MemoryStream(downloadFile.FileContentBytes))
            using (var reader = new StreamReader(memoryStream, Encoding.GetEncoding("ISO-8859-1")))
            {
                while (!reader.EndOfStream)
                {
                    var line = reader.ReadLine();

                    if (currentRowReadFromFile >= lastRowNumberProcessed)
                    {
                        var rowValues = line?.Split(',');
                        if (rowValues != null && rowValues.Any())
                        {
                            surfaceLandmarksToProcess.Add(
                                rowValues.GetSurfaceLandmarkReading());
                        }
                    }

                    currentRowReadFromFile++;
                }
            }

            if (!surfaceLandmarksToProcess.Any())
            {
                return new FileConverterResult
                {
                    Message = "The file does not contain new data to be processed",
                    FilesConvertedToReadings = new List<FileInformation>() { downloadFile.File }
                };
            }

            var request = new GetAutomatedReadingsDataRequest
            {
                InstrumentIdentifiers = surfaceLandmarksToProcess
                    .Select(r => r.InstrumentIdentifier),
                StructureId = structureId
            };

            var additionalInstrumentData = await _clientsApiService
                .GetAutomatedReadingsDataAsync(request);
            if (additionalInstrumentData == null || !additionalInstrumentData.Any())
            {
                return new FileConverterResult
                {
                    Message = $"No additional information was found for the instruments in the clients API - " +
                    $"Request: {JsonSerializer.Serialize(request)}"
                };
            }

            var readingsToBeSent = new List<AddReadingRequest>();

            foreach (var surfaceLandmark in surfaceLandmarksToProcess)
            {
                var additionalData = additionalInstrumentData
                        .FirstOrDefault(i => i.Instrument.Identifier == surfaceLandmark.InstrumentIdentifier);
                if (additionalData == null)
                {
                    continue;
                }

                var readingNew = additionalData.Instrument.GenerateAddReadingRequest(
                    isReferencial: false);

                readingNew.Values.Add(
                    surfaceLandmark.GenerateReadingValue(
                        additionalData.Instrument.Azimuth));

                readingsToBeSent.Add(readingNew);
            }

            await _automatedReadingVariableRepository.InsertAsync(
                new Domain.Entities.AutomatedReadingVariable
                {
                    ClientId = clientId,
                    VariableName = Variables.AtlanticNickel.LastRowNumberCsvProcessed,
                    VariableValue = currentRowReadFromFile.ToString()
                });

            return new FileConverterResult
            {
                ReadingsToBeSent = readingsToBeSent,
                FilesConvertedToReadings =
                    new List<FileInformation>() { downloadFile.File }
            };
        }
    }
}