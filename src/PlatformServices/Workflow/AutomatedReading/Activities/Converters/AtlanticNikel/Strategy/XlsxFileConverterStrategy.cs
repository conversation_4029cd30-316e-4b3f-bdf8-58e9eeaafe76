using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Application.Apis.Clients.Request.Instrument.GetAutomatedReadingsData;
using Application.Sftp.Model._Shared;
using Application.Sftp.Model.Response;
using Database.Repositories.AutomatedReadingVariable;
using Domain.Enums;
using NPOI.XSSF.UserModel;
using System.Text.Json;
using Workflow.AutomatedReading.Extensions;
using static Workflow.Constants.AutomatedReadingsWorkFlow;

namespace Workflow.AutomatedReading.Activities.Converters.AtlanticNikel.Strategy
{
    public class XlsxFileConverterStrategy : IFileConverterStrategy
    {
        private readonly IClientsApiService _clientsApiService;
        private readonly IAutomatedReadingVariableRepository _automatedReadingVariableRepository;

        public XlsxFileConverterStrategy(
                    IClientsApiService clientsApiService,
            IAutomatedReadingVariableRepository automatedReadingVariableRepository)
        {
            _clientsApiService = clientsApiService;
            _automatedReadingVariableRepository = automatedReadingVariableRepository;
        }

        public async Task<FileConverterResult> ExecuteAsync(
            List<DownloadFileResponse> files,
            Guid clientId,
            Guid structureId)
        {
            var downloadFile = files.FirstOrDefault();
            if (downloadFile?.FileContentBytes == null
                || downloadFile.FileContentBytes.Length == 0)
            {
                return new FileConverterResult
                {
                    Message = "File is empty",
                    FilesConvertedToReadings = new List<FileInformation>() { downloadFile.File }
                };
            }

            //Searches last row with instruments (piezometers) reading processed from the .xlsx file
            var lastInstrumentsRowNumberProcessed = await _automatedReadingVariableRepository
                .GetVariableValueAsync<int>(
                    Variables.AtlanticNickel.LastInstrumentsRowNumberXlsxProcessed,
                    clientId);
            if (lastInstrumentsRowNumberProcessed == 0)
            {
                return new FileConverterResult
                {
                    Message = $"The variable: {nameof(Variables.AtlanticNickel.LastInstrumentsRowNumberXlsxProcessed)} was not found"
                };
            }

            //Searches last row with pluviometer reading processed from the .xlsx file
            var lastPluviometerRowNumberProcessed = await _automatedReadingVariableRepository
                .GetVariableValueAsync<int>(
                    Variables.AtlanticNickel.LastPluviometerRowNumberXlsxProcessed,
                    clientId);
            if (lastPluviometerRowNumberProcessed == 0)
            {
                return new FileConverterResult
                {
                    Message = $"The variable: {nameof(Variables.AtlanticNickel.LastPluviometerRowNumberXlsxProcessed)} was not found"
                };
            }

            var instrumentIdentifiers = new List<(string Identifier, int Column)>();

            using (var memoryStream = new MemoryStream(downloadFile.FileContentBytes))
            using (var workbook = new XSSFWorkbook(memoryStream))
            {
                var readingsToBeSent = new List<AddReadingRequest>();

                #region Instruments readings

                //Get worksheet that has instruments readings (without pluviometer)
                var worksheetInstrumentsReadings = workbook
                    .GetInstrumentsReadingsWorksheet();

                //Get identifiers of the instruments
                instrumentIdentifiers
                    .AddRange(
                        worksheetInstrumentsReadings
                            .GetInstrumentsIdentifiers()
                    );

                var request = new GetAutomatedReadingsDataRequest
                {
                    InstrumentIdentifiers = instrumentIdentifiers.Select(x => x.Identifier).ToList(),
                    InstrumentTypes = new List<InstrumentType> { InstrumentType.Pluviometer }, //Searches pluviometer by type because file doesn't have the text identifier
                    StructureId = structureId
                };

                var additionalInstrumentData = await _clientsApiService
                    .GetAutomatedReadingsDataAsync(request);
                if (additionalInstrumentData == null || !additionalInstrumentData.Any())
                {
                    return new FileConverterResult
                    {
                        Message = $"No additional information was found for the instruments in the clients API - " +
                        $"Request: {JsonSerializer.Serialize(request)}"
                    };
                }

                //Extract instruments readings
                for (int row = lastInstrumentsRowNumberProcessed; row < worksheetInstrumentsReadings.LastRowNum; row++)
                {
                    var readingDate = worksheetInstrumentsReadings.GetInstrumentReadingDate(row);

                    if (readingDate == null)
                    {
                        continue;
                    }

                    readingsToBeSent.AddRange(
                        worksheetInstrumentsReadings
                            .GetInstrumentsReadings(readingDate.Value, instrumentIdentifiers, additionalInstrumentData, row));
                }

                #endregion

                #region Pluviometer readings

                //Get workbook that has pluviometer readings
                var worksheetPluviometerReadings = workbook
                    .GetPluviometerReadingsWorksheet();

                //Get pluviometer instrument searched previously by type
                var pluviometer = additionalInstrumentData
                    .FirstOrDefault(x => x.Instrument.Type == InstrumentType.Pluviometer);

                if (pluviometer != null)
                {
                    //Gets pluviometer readings
                    var (readings, lastRowProcessed) = worksheetPluviometerReadings
                        .GetPluviometerReadings(lastPluviometerRowNumberProcessed, pluviometer);

                    readingsToBeSent.AddRange(readings);

                    //Updates last row with pluviometer reading processed
                    await _automatedReadingVariableRepository.UpdateAsync(
                        new Domain.Entities.AutomatedReadingVariable
                        {
                            ClientId = clientId,
                            VariableName = Variables.AtlanticNickel.LastPluviometerRowNumberXlsxProcessed,
                            VariableValue = lastRowProcessed.ToString()
                        });
                }
                #endregion

                //Updates last row with instruments reading processed
                var lastRowExecuted = worksheetInstrumentsReadings.LastRowNum;
                await _automatedReadingVariableRepository.UpdateAsync(
                    new Domain.Entities.AutomatedReadingVariable
                    {
                        ClientId = clientId,
                        VariableName = Variables.AtlanticNickel.LastInstrumentsRowNumberXlsxProcessed,
                        VariableValue = lastRowExecuted.ToString()
                    });

                return new FileConverterResult
                {
                    ReadingsToBeSent = readingsToBeSent,
                    FilesConvertedToReadings =
                    new List<FileInformation>() { downloadFile.File }
                };
            }
        }
    }
}
