using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Application.Apis.Clients.Response.Clients;
using Application.Sftp.Model._Shared;
using Application.Sftp.Model.Response;
using Database.Repositories.AutomatedReadingVariable;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services.Models;
using Serilog;
using Workflow.AutomatedReading.Activities.Converters.AtlanticNikel.Strategy;
using static Workflow.Constants.AutomatedReadingsWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.AutomatedReading.Activities.Converters.AtlanticNikel
{
    [Activity(
        Category = "Automated Readings",
        DisplayName = "Atlantic Nikel file conversion",
        Description = "Processes Atlantic Nikel files and transforms them into the required format for reading creation.",
        Outcomes = new[] { OutcomeNames.Done }
    )]
    public sealed class AtlanticNikelFileConverterActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;
        private readonly IAutomatedReadingVariableRepository _automatedReadingVariableRepository;

        public AtlanticNikelFileConverterActivity(
            IClientsApiService clientsApiService,
            IAutomatedReadingVariableRepository automatedReadingVariableRepository)
        {
            _clientsApiService = clientsApiService;
            _automatedReadingVariableRepository = automatedReadingVariableRepository;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
            ActivityExecutionContext context)
        {
            try
            {
                var clientConfiguration = context
                    .GetInput<GetClientAutomatedReadingsConfigurationsResponse>();

                var downloadFiles = context.GetTransientVariable<DownloadFilesResponse>(
                    name: Variables.DownloadedFiles);
                if (downloadFiles?.Files == null || !downloadFiles.Files.Any())
                {
                    context.SetTransientVariable(Variables.ErrorMessage, $"Não há conteúdo nos arquivos obtidos do servidor.");
                    return Outcome(OutcomeNames.Cancel);
                }

                var structureIdValue = await _automatedReadingVariableRepository
                    .GetVariableValueAsync<string>(
                        Variables.AtlanticNickel.SantaRitaStructureId,
                        clientConfiguration.ClientId);

                if (!Guid.TryParse(structureIdValue, out Guid structureId)
                    || structureId == Guid.Empty)
                {
                    context.SetTransientVariable(Variables.ErrorMessage, $"Não foi possível encontrar uma das variáveis para converter os arquivos.");
                    return Outcome(OutcomeNames.Cancel);
                }

                var filesGroupedByExtension = downloadFiles
                    .Files
                    .GroupBy(f => f.File.Extension);

                var readingsToBeSent = new List<AddReadingRequest>();
                var filesConvertedToReadings = new List<FileInformation>();

                foreach (var fileGrouped in filesGroupedByExtension)
                {
                    var fileConverterStrategy = GetStrategy(fileGrouped.Key);

                    var fileConverterResult = await fileConverterStrategy.ExecuteAsync(
                        fileGrouped.ToList(),
                        clientConfiguration.ClientId,
                        structureId);

                    if (!fileConverterResult.IsValid())
                    {
                        context.JournalData.Add(
                            key: "Error getting converted file",
                            value: fileConverterResult);

                        context.SetTransientVariable(Variables.ErrorMessage, fileConverterResult.Message);
                        return Outcome(OutcomeNames.Cancel);
                    }

                    readingsToBeSent.AddRange(fileConverterResult.ReadingsToBeSent);
                    filesConvertedToReadings.AddRange(fileConverterResult.FilesConvertedToReadings);
                }

                context.SetTransientVariable(
                    name: Variables.ReadingsToBeSent,
                    value: readingsToBeSent);

                context.SetTransientVariable(
                    name: Variables.FilesConvertedToReadings,
                    value: filesConvertedToReadings);

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Error in AtlanticNikelFileConverterActivity");
                context.SetTransientVariable(Variables.ErrorMessage, $"Ocorreu um erro inesperado nas conversões de arquivos.");
                return Outcome(OutcomeNames.Cancel);
            }
        }

        private IFileConverterStrategy GetStrategy(
            string extension) => extension switch
            {
                FileExtensions.Dat =>
                    new DatFileConverterStrategy(
                        _clientsApiService,
                        _automatedReadingVariableRepository),
                FileExtensions.Csv =>
                    new CsvFileConverterStrategy(
                        _clientsApiService,
                        _automatedReadingVariableRepository),
                FileExtensions.Xlsx =>
                    new XlsxFileConverterStrategy(
                        _clientsApiService,
                        _automatedReadingVariableRepository),
                _ => throw new NotSupportedException($"The strategy for extension: {extension} was not implemented.")
            };
    }
}
