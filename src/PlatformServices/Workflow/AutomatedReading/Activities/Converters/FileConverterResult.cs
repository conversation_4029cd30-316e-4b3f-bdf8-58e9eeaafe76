using Application.Apis.Clients.Request;
using Application.Sftp.Model._Shared;

namespace Workflow.AutomatedReading.Activities.Converters
{
    public sealed record FileConverterResult
    {
        public string Message { get; set; }
        public List<AddReadingRequest> ReadingsToBeSent { get; set; } = new();
        public List<FileInformation> FilesConvertedToReadings { get; set; } = new();

        public FileConverterResult()
        { }

        public bool IsValid()
        {
            return ReadingsToBeSent != null
                && ReadingsToBeSent.Any()
                && FilesConvertedToReadings != null
                && FilesConvertedToReadings.Any();
        }
    }
}
