using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Application.Apis.Clients.Request.Instrument.GetAutomatedReadingsData;
using Application.Apis.Clients.Response.Instrument.GetAutomatedReadingsData;
using Application.Sftp.Model._Shared;
using Application.Sftp.Model.Response;
using Domain.AutomatedReading.Mosaic;
using Domain.Enums;
using Elsa.ActivityResults;
using System.Text;
using System.Text.Json;
using Workflow.AutomatedReading.Extensions;

namespace Workflow.AutomatedReading.Activities.Converters.Mosaic.Strategy
{
    public sealed class CsvFileConverterStrategy : IFileConverterStrategy
    {
        private readonly IClientsApiService _clientsApiService;

        public CsvFileConverterStrategy(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        public async Task<FileConverterResult> ExecuteAsync(List<DownloadFileResponse> files, Guid clientId)
        {
            var downloadFile = files.FirstOrDefault();

            if (downloadFile?.FileContentBytes == null
                || downloadFile.FileContentBytes.Length == 0)
            {
                return new FileConverterResult
                {
                    Message = "Arquivo está vazio.",
                    FilesConvertedToReadings = new List<FileInformation>() { downloadFile.File }
                };
            }

            var readings = new List<MosaicReading>();

            using (var memoryStream = new MemoryStream(downloadFile.FileContentBytes))
            using (var reader = new StreamReader(memoryStream, Encoding.GetEncoding("ISO-8859-1")))
            {
                // Skip the header line
                string headerLine = reader.ReadLine();

                while (!reader.EndOfStream)
                {
                    var line = reader.ReadLine();

                    if (string.IsNullOrWhiteSpace(line))
                        continue;

                    var rowValues = line.Split(',');

                    if (rowValues == null || !rowValues.Any())
                        continue;

                    readings.Add(rowValues.GetReading());
                }
            }

            if (!readings.Any())
            {
                return new FileConverterResult
                {
                    Message = "O arquivo não tem dados para ser processado.",
                    FilesConvertedToReadings = new List<FileInformation>() { downloadFile.File }
                };
            }

            var groupedReadings = readings
                .GroupBy(x => new { x.StructureName, x.ClientUnitName })
                .Select(g => new
                {
                    g.Key.StructureName,
                    g.Key.ClientUnitName,
                    Readings = g.ToList()
                });

            var automatedReadingsData = new List<GetAutomatedReadingsDataResponse>();

            foreach (var groupReadings in groupedReadings)
            {
                var request = new GetAutomatedReadingsDataRequest()
                {
                    InstrumentIdentifiers = groupReadings.Readings
                        .Select(x => x.InstrumentIdentifier)
                        .Distinct()
                        .ToList(),
                    ClientUnitName = groupReadings.ClientUnitName,
                    StructureName = groupReadings.StructureName,
                };

                var response = await _clientsApiService
                    .GetAutomatedReadingsDataAsync(request);

                if (response == null || !response.Any())
                {
                    var newIdentifiers = new List<string>();

                    foreach (var missingIdentifier in request.InstrumentIdentifiers)
                    {
                        var parts = missingIdentifier.Split('-');

                        if (parts.Length == 4)
                        {
                            var newIdentifier = string.Join("-", parts.Skip(2));

                            newIdentifiers.Add(newIdentifier);
                        }
                    }

                    if (newIdentifiers.Any())
                    {
                        var newRequest = new GetAutomatedReadingsDataRequest()
                        {
                            InstrumentIdentifiers = newIdentifiers,
                            ClientUnitName = groupReadings.ClientUnitName,
                            StructureName = groupReadings.StructureName,
                        };

                        var newResponse = await _clientsApiService
                            .GetAutomatedReadingsDataAsync(newRequest);

                        if (newResponse != null && newResponse.Any())
                        {
                            response = newResponse;
                        }
                    }

                    continue;
                }

                var hasAllIdentifiers = request.InstrumentIdentifiers.All(identifier =>
                    response.Any(i => i.Instrument.Identifier == identifier || i.Instrument.AlternativeName == identifier));

                if (!hasAllIdentifiers) 
                {
                    var missingIdentifiers = request.InstrumentIdentifiers
                        .Where(identifier => !response.Any(i => i.Instrument.Identifier == identifier || i.Instrument.AlternativeName == identifier))
                        .ToList();

                    var newIdentifiers = new List<string>();

                    foreach (var missingIdentifier in missingIdentifiers)
                    {
                        var parts = missingIdentifier.Split('-');
                        
                        if (parts.Length == 4)
                        {
                            var newIdentifier = string.Join("-", parts.Skip(2));

                            newIdentifiers.Add(newIdentifier);
                        }
                    }

                    if (newIdentifiers.Any())
                    {
                        var newRequest = new GetAutomatedReadingsDataRequest()
                        {
                            InstrumentIdentifiers = newIdentifiers,
                            ClientUnitName = groupReadings.ClientUnitName,
                            StructureName = groupReadings.StructureName,
                        };

                        var newResponse = await _clientsApiService
                            .GetAutomatedReadingsDataAsync(newRequest);

                        if (newResponse != null && newResponse.Any())
                        {
                            response.AddRange(newResponse);
                        }
                    }
                }

                automatedReadingsData.AddRange(response);
            }

            var readingsToBeSent = new List<AddReadingRequest>();

            foreach (var reading in readings)
            {
                var additionalData = FindAdditionalData(automatedReadingsData, reading);

                if (additionalData == null)
                {
                    return new FileConverterResult
                    {
                        Message = $"O instrumento {reading.InstrumentIdentifier} não foi encontrado no banco de dados.",
                        FilesConvertedToReadings = new List<FileInformation> { downloadFile.File }
                    };
                }

                readingsToBeSent.Add(ConvertToReadingRequest(reading, additionalData));
            }

            return new FileConverterResult
            {
                ReadingsToBeSent = readingsToBeSent,
                FilesConvertedToReadings =
                    new List<FileInformation>() { downloadFile.File }
            };
        }

        private GetAutomatedReadingsDataResponse FindAdditionalData(IEnumerable<GetAutomatedReadingsDataResponse> data, MosaicReading reading)
        {
            var additionalData = data
                .FirstOrDefault(i => (i.Instrument.Identifier == reading.InstrumentIdentifier || i.Instrument.AlternativeName == reading.InstrumentIdentifier)
                    && i.Instrument.ClientUnitName == reading.ClientUnitName
                    && i.Instrument.StructureName == reading.StructureName);

            if (additionalData == null)
            {
                var parts = reading.InstrumentIdentifier.Split('-');

                if (parts.Length == 4)
                {
                    var newIdentifier = string.Join("-", parts.Skip(2));

                    additionalData = data
                        .FirstOrDefault(i => (i.Instrument.Identifier == newIdentifier || i.Instrument.AlternativeName == newIdentifier)
                            && i.Instrument.ClientUnitName == reading.ClientUnitName
                            && i.Instrument.StructureName == reading.StructureName);
                }
            }

            return additionalData;
        }

        private AddReadingRequest ConvertToReadingRequest(MosaicReading reading, GetAutomatedReadingsDataResponse additionalData)
        {
            Application.Apis._Shared.Instrument.Measurement measurement = null;

            if (additionalData.Instrument.Type == Domain.Enums.InstrumentType.ElectricPiezometer)
            {
                measurement = new() { Id = additionalData.Data.First().Id };
            }

            var readingValue = new ReadingValueRequest
            {
                Measurement = measurement,
                Date = reading.ReadingValueDate,
                Dry = reading.Dry,
                Quota = !reading.Dry ? reading.Quota : null,
                Depth = reading.Dry || additionalData.Instrument.Type == Domain.Enums.InstrumentType.ElectricPiezometer ? null : additionalData.Instrument.TopQuota - reading.Quota,
                Pressure = reading.Dry || additionalData.Instrument.Type == Domain.Enums.InstrumentType.ElectricPiezometer ? null : (reading.Quota - additionalData.Instrument.BaseQuota) * Constants.Gravity
            };

            return new()
            {
                Instrument = new InstrumentRequest
                {
                    Id = additionalData.Instrument.Id,
                    Identifier = additionalData.Instrument.Identifier,
                    Type = additionalData.Instrument.Type
                },
                IsAutomated = true,
                Values = new List<ReadingValueRequest> { readingValue }
            };
        }
    }
}
