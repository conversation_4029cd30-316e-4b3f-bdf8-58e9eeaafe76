using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services.Models;
using Activity = Elsa.Services.Activity;

namespace Workflow.AutomatedReading.Activities
{
    [Activity(
        Category = "Automated Readings",
        DisplayName = "Vale file conversion",
        Description = "Processes Vale files and transforms them into the required format for reading creation.",
        Outcomes = new[] { OutcomeNames.Done }
    )]
    public sealed class ValeFileConverterActivity : Activity
    {
        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
            ActivityExecutionContext context)
        {
            try
            {
                //TODO: Conversão dos arquivos será implementada na sprint 72
                //TODO: Preencher variavel com as leituras (ReadingsToBeSent)
                //TODO: Preencher variavel com os arquivos convertidos (FilesConvertedToReadings)
                return Done();
            }
            catch (Exception exception)
            {
                return Fault(exception);
            }
        }
    }
}
