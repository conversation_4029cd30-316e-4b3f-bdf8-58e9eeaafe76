using Application.Apis.AITextGeneration;
using Application.Apis.AITranscription;
using Application.Apis.Clients.Response.InspectionSheet;
using Application.Extensions;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using static Domain.Constants.InspectionSheetsContextMessages;
using static Workflow.Constants.InspectionWorkflow;
using Activity = Elsa.Services.Activity;
using File = Application.Apis._Shared.File;

namespace Workflow.InspectionSheet.TranscribeInspectionSheet.Activities;

public class TranscribeAudioActivity : Activity
{
    private readonly ITranscriptionApiService _transcriptionApiService;
    private readonly ITextGenerationApiService _textGenerationApiService;

    public TranscribeAudioActivity(
        ITranscriptionApiService transcriptionApiService,
        ITextGenerationApiService textGenerationApiService)
    {
        _transcriptionApiService = transcriptionApiService;
        _textGenerationApiService = textGenerationApiService;
    }

    protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
        ActivityExecutionContext context)
    {
        try
        {
            var inspectionSheet = context
                .GetTransientVariable<GetInspectionSheetByIdResponse>(
                    Variables.InspectionSheet);

            if (inspectionSheet == null)
            {
                return Fault("Could not find the inspection sheet.");
            }
            
            var parallelOptions = new ParallelOptions { MaxDegreeOfParallelism = 4 };

            var notes =
                inspectionSheet.Notes.Where(note => note.VoiceNote != null);
            
            await Parallel.ForEachAsync(
                notes,
                parallelOptions,
                async (note, _) =>
                {
                    var originalTranscription = await SendAudioToTranscribe(note.VoiceNote);
                    
                    note.Note = await _textGenerationApiService.GenerateText(
                        ImproveTranscriptionMessage,
                        originalTranscription);
                });

            var occurrences = inspectionSheet.Areas
                .SelectMany(area => area.Aspects
                    .SelectMany(aspect => aspect.Occurrences)
                    .Where(occurrence => occurrence.VoiceNote != null)
                );

            await Parallel.ForEachAsync(
                occurrences,
                parallelOptions,
                async (occurrence, _) =>
                {
                    var originalTranscription =
                        await SendAudioToTranscribe(occurrence.VoiceNote);
                    
                    occurrence.Note = await _textGenerationApiService.GenerateText(
                        ImproveTranscriptionMessage,
                        originalTranscription);
                });

            context.SetVariable(Variables.InspectionSheet, inspectionSheet);

            return Done();
        }
        catch (Exception ex)
        {
            context.JournalData.Add("ExceptionMessage", ex.Message);
            return Fault(ex);
        }
    }

    private async Task<string> SendAudioToTranscribe(
        File file)
    {
        var byteArray = Convert.FromBase64String(
            file.Base64);

        var transcriptionId = await _transcriptionApiService
            .SendAudioAsync(
                byteArray,
                file.Name);

        if (string.IsNullOrWhiteSpace(transcriptionId))
        {
            throw new InvalidOperationException(
                "Could not send the audio to transcribe.");
        }

        var transcriptionText = await _transcriptionApiService
            .GetTextAsync(transcriptionId);

        if (string.IsNullOrWhiteSpace(transcriptionText))
        {
            throw new InvalidOperationException(
                "Could not get the audio transcription.");
        }

        return transcriptionText;
    }
}