using System.Text.Json;
using Application.Apis.Clients;
using Application.Apis.Clients.Request.InspectionSheet;
using Application.Apis.Clients.Response.InspectionSheet;
using Elsa.ActivityResults;
using Elsa.Services;
using Elsa.Services.Models;
using static Workflow.Constants.InspectionWorkflow;

namespace Workflow.InspectionSheet.TranscribeInspectionSheet.Activities;

public class UpdateInspectionSheetActivity : Activity
{
    private readonly IClientsApiService _clientsApiService;

    public UpdateInspectionSheetActivity(IClientsApiService clientsApiService)
    {
        _clientsApiService = clientsApiService;
    }

    protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
        ActivityExecutionContext context)
    {
        try
        {
            var inspectionSheet = context
                .GetTransientVariable<GetInspectionSheetByIdResponse>(
                    Variables.InspectionSheet);

            if (inspectionSheet == null)
            {
                return Fault("Could not find the inspection sheet.");
            }

            var request = new UpdateInspectionSheetRequest
            {
                Id = inspectionSheet.Id,
                Type = inspectionSheet.Type,
                StartDate = inspectionSheet.StartDate,
                EndDate = inspectionSheet.EndDate,
                ExecutedBy = inspectionSheet.ExecutedBy,
                Responsibles = inspectionSheet.Responsibles,
                EvaluatorName = inspectionSheet.EvaluatorName,
                EvaluatorPosition = inspectionSheet.EvaluatorPosition,
                EvaluatorCrea = inspectionSheet.EvaluatorCrea,
                EvaluatorArt = inspectionSheet.EvaluatorArt,
                File = inspectionSheet.File,
                SpillwayStructuresReliability = inspectionSheet.SpillwayStructuresReliability,
                Percolation = inspectionSheet.Percolation,
                DeformationsAndSettlements = inspectionSheet.DeformationsAndSettlements,
                SlopeDeterioration = inspectionSheet.SlopeDeterioration,
                SurfaceDrainage = inspectionSheet.SurfaceDrainage,
                Notes = inspectionSheet.Notes,
                IdentifiedAnomalies = inspectionSheet.IdentifiedAnomalies,
                Areas = inspectionSheet.Areas,
                UserCompletedTheInspection = false,
                TranscriptionHasFinished = true
            };

            await _clientsApiService.UpdateInspectionSheetAsync(
                request);

            return Done();
        }
        catch (Exception ex)
        {
            context.JournalData.Add("ExceptionMessage", ex.Message);
            return Fault(ex);
        }
    }
}