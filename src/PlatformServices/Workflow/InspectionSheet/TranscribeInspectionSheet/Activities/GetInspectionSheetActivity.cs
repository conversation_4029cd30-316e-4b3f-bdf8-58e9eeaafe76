using System.Text.Json;
using Application.Apis.Clients;
using Domain.Messages.Commands.InspectionSheet;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services;
using Elsa.Services.Models;

namespace Workflow.InspectionSheet.TranscribeInspectionSheet.Activities;

public sealed class GetInspectionSheetActivity : Activity
{
    private readonly IClientsApiService _clientsApiService;

    public GetInspectionSheetActivity(IClientsApiService clientsApiService)
    {
        _clientsApiService = clientsApiService;
    }

    protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
        ActivityExecutionContext context)
    {
        try
        {
            var command =
                context.GetInput<TranscribeInspectionSheetVoiceNotes>();

            var inspectionSheet =
                await _clientsApiService.GetInspectionSheetAsync(command.Id);

            if (inspectionSheet == null)
            {
                return Fault("Could not find the inspection sheet.");
            }

            context.SetTransientVariable(
                Constants.InspectionWorkflow.Variables.InspectionSheet,
                inspectionSheet);
            
            context.JournalData.Add(
                Constants.InspectionWorkflow.Variables.InspectionSheet,
                JsonSerializer.Serialize(inspectionSheet));

            return inspectionSheet.HasVoiceNotesToTranscribe
                ? Done()
                : Outcome(OutcomeNames.Cancel);
        }
        catch (Exception ex)
        {
            context.JournalData.Add("ExceptionMessage", ex.Message);
            return Fault(ex);
        }
    }
}