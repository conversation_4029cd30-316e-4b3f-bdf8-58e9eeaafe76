using Domain.Messages.Commands.InspectionSheet;
using Elsa;
using Elsa.Activities.ControlFlow;
using Elsa.Activities.MassTransit;
using Elsa.Activities.Primitives;
using Elsa.Builders;
using Workflow.InspectionSheet.TranscribeInspectionSheet.Activities;
using static Workflow.Constants.InspectionWorkflow.Variables;

namespace Workflow.InspectionSheet.TranscribeInspectionSheet;

public sealed class TranscribeInspectionSheetWorkflow : IWorkflow
{
    public void Build(IWorkflowBuilder builder)
    {
        builder
            .AsTransient()
            .WithDisplayName("Transcribe inspection sheet workflow")
            .ReceiveMassTransitMessage(activity =>
                activity.Set(
                    act => act.MessageType,
                    act => typeof(TranscribeInspectionSheetVoiceNotes)))
            .WithDisplayName("Receive transcribe inspection sheet message")
            .WithDescription(
                "When a message is received, will transcribe the audio and update the inspection sheet")
            .SetTransientVariable(
                Command,
                context => context.GetInput<TranscribeInspectionSheetVoiceNotes>())
            .Then<GetInspectionSheetActivity>()
            .Then<TranscribeAudioActivity>()
            .Then<UpdateInspectionSheetActivity>()
            .Finish();
    }
}