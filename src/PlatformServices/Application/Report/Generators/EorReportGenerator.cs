using Application.Apis.Clients.Response.Report;
using Application.Docx.Extensions;
using Domain.Entities.Report;
using Domain.Enums;
using NPOI.XWPF.UserModel;
using System.Net.Mime;
using static Application.Docx.Constants;

namespace Application.Report.Generators
{
    public sealed class EorReportGenerator : IReportGenerator
    {
        public string Generate(PendingReport report, string rootPath)
        {
            var templateFilePath = report.GetDocxTemplatePath(rootPath);
            var outputFileName = report.GetDocxOutputFileName();
            var outputFilePath = report.GetDocxOutputFilePath(rootPath);
            var stabilityTableImages = report.GetSecurityLevelImagePaths(rootPath);
            var rawData = report.RawData as GetEoRReportDataResponse;

            using var fileStream = new FileStream(templateFilePath, FileMode.Open, FileAccess.Read);
            using var document = new XWPFDocument(fileStream);

            document.ReplaceText(report.Result.IntermediatePayload);
            document.ReplaceText(report.Result.DefinitivePayload);
            document.ReplacePictures(report.Result.ImagePayload);

            switch (report.Configuration.Template)
            {
                case ReportTemplateType.Vale:
                    document.AddValeResponsiblesTable(rawData?.Structure?.Responsibles);
                    document.AddValePercolationReadingsTable(rawData?.Sections.SelectMany(section => section.Instruments));
                    document.AddValeStabilityTable(rawData);
                    break;

                default:
                    document.AddMosaicResponsiblesTable(rawData?.Structure?.Responsibles);
                    document.AddMosaicPercolationReadingsTable(rawData.Sections, stabilityTableImages);
                    document.AddMosaicSurfaceLandmarkInfoTable(rawData?.Sections.SelectMany(section => section.Instruments));
                    document.AddMosaicSettlementGaugeInfoTable(rawData?.Sections.SelectMany(section => section.Instruments));
                    document.AddMosaicInclinometerInfoTable(rawData?.Sections.SelectMany(section => section.Instruments));
                    document.AddMosaicSettlementGaugeReadingsTable(rawData?.Sections.SelectMany(section => section.Instruments), stabilityTableImages);
                    document.AddMosaicInclinometerReadingsTable(rawData?.Sections.SelectMany(section => section.Instruments), stabilityTableImages);
                    document.AddMosaicSurfaceLandmarkReadingsTable(rawData?.Sections.SelectMany(section => section.Instruments), stabilityTableImages);
                    document.AddMosaicStabilityTable(rawData, stabilityTableImages);
                    break;
            }

            using var outFileStream = new FileStream(outputFilePath, FileMode.Create, FileAccess.Write);
            document.Write(outFileStream);
            outFileStream.Close();

            report.Result.FileInfo = new ReportFileInfo
            {
                Name = outputFileName,
                Path = outputFilePath,
                ContentType = new ContentType(DocxContentType)
            };

            return outputFilePath;
        }
    }
}
