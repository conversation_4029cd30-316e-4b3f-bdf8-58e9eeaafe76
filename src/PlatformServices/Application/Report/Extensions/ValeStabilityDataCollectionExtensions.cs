using Application.Apis.Clients.Response.Report;
using Application.Extensions;
using Domain.Enums;
using Domain.Report.Vale;

namespace Application.Report.Extensions
{
    public static class ValeStabilityDataCollectionExtensions
    {
        public static ValeStabilityTable ConvertToEorReportTable(
            this IEnumerable<ValeStabilityData> stabilityData,
            IEnumerable<Section> sections)
        {
            var grouped = stabilityData.GroupBy(item => new { item.SectionId, item.Month, item.Fortnight });

            var result = new ValeStabilityTable();

            foreach (var section in sections)
            {
                var itemsBySection = grouped.Where(group => group.Key.SectionId == section.Id);

                if (!itemsBySection.Any())
                {
                    continue;
                }

                result.Sections.Add(new ValeStabilitySection
                {
                    SectionName = section.Name,
                    Fortnights = itemsBySection?.Select(items => new ValeStabilityFortnight
                    {
                        Month = items.Key.Month,
                        Fortnight = items.Key.Fortnight,
                        DrainedValue = items.FirstOrDefault(item => item.SoilConditionType == SoilConditionType.Drained)?.Value.RoundNumber(2).ToString() ?? "-",
                        UndrainedValue = items.FirstOrDefault(item => item.SoilConditionType == SoilConditionType.Undrained)?.Value.RoundNumber(2).ToString() ?? "-",
                        PseudoStaticValue = items.FirstOrDefault(item => item.SoilConditionType == SoilConditionType.PseudoStatic)?.Value.RoundNumber(2).ToString() ?? "-"
                    })
                });
            }

            return result;
        }
    }
}
