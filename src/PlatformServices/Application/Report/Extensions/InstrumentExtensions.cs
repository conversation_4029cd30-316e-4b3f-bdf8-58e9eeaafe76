using Application.Apis.Clients.Response.Report;
using Application.Extensions;
using Domain.Report.Vale;
using System.Text.Json;

namespace Application.Report.Extensions
{
    public static class InstrumentExtensions
    {
        public static ValePercolationReadingsTableItem ConvertToEorReportTable(
            this Instrument instrument)
        {
            var statisticData = JsonSerializer.Deserialize<PercolationStatisticData>(instrument.StatisticData) as PercolationStatisticData;

            return new ValePercolationReadingsTableItem
            {
                Instrument = instrument.Name,
                IsOffline = !instrument.Online,
                TopQuotaValue = instrument.TopQuota?.RoundNumber(2).ToString() ?? "-",
                BaseQuotaValue = instrument.BaseQuota?.RoundNumber(2).ToString() ?? "-",
                SafetyFactorNormalValue = "-",
                SafetyFactorAttentionValue = instrument.SecurityLevels?.FirstOrDefault()?.Attention?.RoundNumber(2).ToString() ?? "-",
                SafetyFactorAlertValue = instrument.SecurityLevels?.FirstOrDefault()?.Alert?.RoundNumber(2).ToString() ?? "-",
                SafetyFactorEmergencyValue = instrument.SecurityLevels?.FirstOrDefault()?.Emergency?.RoundNumber(2).ToString() ?? "-",
                LastReadingValue = statisticData?.LastReadingValue?.Dry ?? false
                    ? "Seco"
                    : statisticData?.LastReadingValue?.Quota?.RoundNumber(2).ToString() ?? "-",
                MaxReadingValue = statisticData?.MaxReadingValue?.Dry ?? false
                    ? "Seco"
                    : statisticData?.MaxReadingValue?.Quota?.RoundNumber(2).ToString() ?? "-",
            };
        }
    }
}
