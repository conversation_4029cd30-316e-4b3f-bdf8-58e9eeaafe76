using Application.Apis.Clients.Response.Report;
using Application.Extensions;
using Domain.Entities.Report.Mosaic;

namespace Application.Report.Extensions
{
    public static class MosaicStabilityDataCollectionExtensions
    {
        public static MosaicStabilityTable ConvertToEorReportTable(
            this IEnumerable<MosaicStabilityData> stabilityData,
            IEnumerable<Section> sections)
        {
            var grouped = stabilityData.GroupBy(item => item.SectionId);

            var result = new MosaicStabilityTable();

            foreach (var section in sections)
            {
                var itemsBySection = grouped.Where(group => group.Key == section.Id).SelectMany(group => group.Select(item => item));

                if (!itemsBySection.Any())
                {
                    continue;
                }

                result.Sections.Add(new MosaicStabilityTableSection
                {
                    SectionName = section.Name,
                    Values = itemsBySection?.Select(item => new MosaicStabilityTableValue
                    {
                        SoilConditionType = item.SoilConditionType,
                        AverageValue = item.AverageValue.RoundNumber(2),
                        MinValue = item.MinValue.RoundNumber(2),
                        MaxValue = item.MaxValue.RoundNumber(2),
                        LastValue = item.LastSafetyFactor.Value.RoundNumber(2),
                        LastValueDate = item.LastSafetyFactor.ReadingCreatedDate,
                        LastValueHasAlert = item.LastSafetyFactor.HasAlert,
                        ImagePlaceholder = item.LastSafetyFactor.HasAlert
                            ? "StabilityTableAlertImage"
                            : "StabilityTableOKImage"
                    })
                });
            }

            return result;
        }
    }
}
