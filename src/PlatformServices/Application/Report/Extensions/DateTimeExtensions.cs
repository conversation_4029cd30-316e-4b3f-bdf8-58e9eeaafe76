namespace Application.Report.Extensions
{
    public static class DateTimeExtensions
    {
        public static DateTime GetLastDayOfMonth(
            this DateTime dateTime)
        {
            return new DateTime(
                dateTime.Year,
                dateTime.Month,
                DateTime.DaysInMonth(dateTime.Year, dateTime.Month));
        }

        public static DateTime GetFirstDayOfMonth(
            this DateTime dateTime)
        {
            return new DateTime(
                dateTime.Year,
                dateTime.Month,
                1);
        }

        public static DateTime GetMiddleDayOfMonth(
            this DateTime dateTime)
        {
            return new DateTime(
                dateTime.Year,
                dateTime.Month,
                15);
        }

        public static bool IsInFirstHalfOfMonth(
            this DateTime dateTime)
        {
            return dateTime.Day < 15;
        }
    }
}