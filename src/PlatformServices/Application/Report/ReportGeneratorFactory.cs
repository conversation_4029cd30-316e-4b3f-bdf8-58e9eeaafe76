using Application.Apis.Clients.Response.Report;
using Application.Report.Generators;

namespace Application.Report
{
    public static class ReportGeneratorFactory
    {
        public static IReportGenerator GetInstance(
            ReportSubjectType subject)
        {
            return subject switch
            {
                ReportSubjectType.EoR => new EorReportGenerator(),
                _ => throw new NotImplementedException()
            };
        }
    }
}
