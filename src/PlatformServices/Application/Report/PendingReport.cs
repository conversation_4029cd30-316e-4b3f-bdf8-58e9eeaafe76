using Application.Apis.Clients.Response.Report;
using Application.Report.Extensions;
using Domain.Entities.Report;
using Domain.Enums;
using Domain.Resources;
using Microsoft.Extensions.Localization;
using System.Net.Mail;

namespace Application.Report
{
    public sealed record PendingReport
    {
        public GetPendingReportResponse Configuration { get; set; }
        public dynamic RawData { get; set; }
        public ReportResult Result { get; set; }

        public MailMessage ToMailMessage(
            IStringLocalizer<Texts> localizer)
        {
            var message = new MailMessage();

            message.To.Add(string.Join(',', Configuration.DestinationEmails));

            message.Subject = localizer
                .GetString(LocaleKeys.EoRReportEmailTitle)
                .Value;

            message.Body = string.Format(
                localizer.GetString(LocaleKeys.EoRReportEmailBody),
                Configuration.Title,
                Configuration.StartDate,
                Configuration.EndDate,
                RawData.Structure.Name,
                RawData.ClientUnit.Name);

            var attachment = new Attachment(
                File.OpenRead(Result.FileInfo.Path),
                Result.FileInfo.ContentType)
            {
                Name = $"{Configuration.Title}.docx"
            };

            message.Attachments.Add(attachment);

            return message;
        }

        private string GetDocxTemplateName()
        {
            return (Configuration.SubjectType, Configuration.Template) switch
            {
                (ReportSubjectType.EoR, ReportTemplateType.Vale) => "eor_vale.docx",
                (ReportSubjectType.EoR, _) => "eor_mosaic.docx",
                _ => throw new NotSupportedException("Unsupported report type")
            };
        }

        public string GetDocxTemplatePath(
            string webRootPath)
        {
            return Path.Combine(webRootPath, "report", "templates", GetDocxTemplateName());
        }

        public string GetDocxOutputFilePath(string webRootPath)
        {
            return Path.Combine(webRootPath, "report", "results", Configuration.Id.ToString(), GetDocxOutputFileName());
        }

        public string GetDocxOutputFileName() =>
            $"{DateTime.UtcNow:ddMMyyyyhhmm}.docx";

        public List<(DateTime, DateTime)> GetSafetyFactorDates()
        {
            if (Configuration.Template != ReportTemplateType.Vale)
            {
                return new List<(DateTime, DateTime)>
                {
                    (Configuration.StartDate, Configuration.EndDate)
                };
            }

            var startDate = Configuration.StartDate.IsInFirstHalfOfMonth()
                ? Configuration.StartDate.GetFirstDayOfMonth()
                : Configuration.StartDate.GetLastDayOfMonth();

            var endDate = Configuration.EndDate.IsInFirstHalfOfMonth()
                ? Configuration.EndDate.GetFirstDayOfMonth()
                : Configuration.EndDate.GetLastDayOfMonth();

            var currentDate = startDate;

            var intervals = new List<(DateTime, DateTime)>();

            while (currentDate <= endDate)
            {
                if (currentDate.IsInFirstHalfOfMonth())
                {
                    intervals.Add((currentDate.GetFirstDayOfMonth(), currentDate.AddDays(14)));
                    currentDate = currentDate.GetFirstDayOfMonth().AddDays(14);
                }
                else
                {
                    intervals.Add((currentDate.GetMiddleDayOfMonth(), currentDate.GetLastDayOfMonth()));
                    currentDate = currentDate.GetFirstDayOfMonth().AddMonths(1);
                }
            }

            return intervals;
        }

        public string GetOutputImageDirectory(
            string fileRootPath)
        {
            return Path.Combine(
                fileRootPath,
                "report",
                "results",
                Configuration.Id.ToString(),
                "images");
        }

        public IDictionary<SecurityLevelType, string> GetSecurityLevelImagePaths(string rootPath)
        {
            return new Dictionary<SecurityLevelType, string>
            {
                [SecurityLevelType.None] = Path.Combine(rootPath, "report", "templates", "images", "eor_green_circle.png"),
                [SecurityLevelType.Attention] = Path.Combine(rootPath, "report", "templates", "images", "eor_yellow_circle.png"),
                [SecurityLevelType.Alert] = Path.Combine(rootPath, "report", "templates", "images", "eor_orange_circle.png"),
                [SecurityLevelType.Emergency] = Path.Combine(rootPath, "report", "templates", "images", "eor_red_circle.png")
            };
        }
    }
}