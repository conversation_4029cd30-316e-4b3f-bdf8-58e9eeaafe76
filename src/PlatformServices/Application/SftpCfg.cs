using Application.Sftp;
using Microsoft.Extensions.DependencyInjection;
using Renci.SshNet;

namespace Application
{
    public static class SftpCfg
    {
        public static IServiceCollection AddSftpService(
            this IServiceCollection services,
            SftpOptions sftpOptions)
        {
            services.AddSingleton(s => new SftpClient(
                host: sftpOptions.Host,
                port: sftpOptions.Port,
                username: sftpOptions.UserName,
                password: sftpOptions.Password));

            services.AddTransient<ISftpService, SftpService>();

            return services;
        }
    }
}