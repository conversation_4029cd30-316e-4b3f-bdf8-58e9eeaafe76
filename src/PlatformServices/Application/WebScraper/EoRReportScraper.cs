using Application.Apis.Clients.Response.Report;
using Application.BlobStorage;
using Application.Report;
using Application.WebScraper.Extensions;
using Application.WebScraper.PageObjectModels;
using Domain.Entities.Report;
using Domain.Enums;
using FluentStorage.Utils.Extensions;
using Microsoft.Extensions.Options;
using Microsoft.Playwright;
using SixLabors.ImageSharp;
using System.Text;
using static Application.WebScraper.Constants.ImageDimensions.Eor;
using static Application.WebScraper.Constants.Selectors;
using static Application.WebScraper.Constants.TextPlaceholders.Eor;

namespace Application.WebScraper
{
    public sealed class EoRReportScraper : IEoRReportScraper
    {
        private readonly IBrowser _browser;
        private readonly WebScraperOptions _options;
        private readonly IBlobStorageService _blobStorageService;
        private readonly BlobStorageOptions _blobStorageOptions;

        public EoRReportScraper(
            IBrowser browser,
            IOptions<WebScraperOptions> options,
            IBlobStorageService blobStorageService,
            IOptions<BlobStorageOptions> blobStorageOptions)
        {
            _browser = browser;
            _options = options.Value;
            _blobStorageService = blobStorageService;
            _blobStorageOptions = blobStorageOptions.Value;
        }

        public async Task ScrapeAsync(
            PendingReport report,
            string fileRootPath)
        {
            var page = await _browser.NewPageAsync(
                new BrowserNewPageOptions
                {
                    ViewportSize = _options.ViewportSize,
                    Locale = "pt-BR",
                });
            
            await page.AddInitScriptAsync(
                "window.sessionStorage.setItem('videoWatched', true);");

            await page.RouteAsync("**/api/v1/notifications?**",
                async route =>
                {
                    await route.FulfillAsync(new()
                        { Status = 204, Body = string.Empty });
                });

            try
            {
                var loginPage = new LoginPage(page, _options);
                await AuthenticateAsync(loginPage);
                
                var homePage = new HomePage(page, _options);
                
                await homePage.FillStructureAsync(
                    report.RawData.Client.Id.ToString(),
                    report.RawData.ClientUnit.Id.ToString(),
                    report.RawData.Structure.Id.ToString());

                var structurePage = new StructurePage(page, _options);

                await GetStructureMapsAsync(
                    structurePage,
                    report,
                    fileRootPath);

                var instrumentListPage = new InstrumentListPage(
                    page,
                    _options);

                await GetInstrumentMapsAsync(
                    instrumentListPage,
                    report,
                    fileRootPath);

                var pictureIndex = 1;

                var instrumentDetailsPage = new InstrumentDetailsPage(
                    page,
                    _options);

                await GetWaterLevelIndicatorAndPiezometerChartsAsync(
                    instrumentDetailsPage,
                    report,
                    fileRootPath,
                    pictureIndex);

                await GetPluviometryAndWaterLevelindicatorChartsAsync(
                    instrumentDetailsPage,
                    report,
                    fileRootPath,
                    pictureIndex);

                await GetSurfaceLandmarkAndPrismChartsAsync(
                    instrumentDetailsPage,
                    report,
                    fileRootPath,
                    pictureIndex);

                await GetInclinometerChartsAsync(
                    instrumentDetailsPage,
                    report,
                    fileRootPath,
                    pictureIndex);

                await GetSettlementGaugeChartsAsync(
                    instrumentDetailsPage,
                    report,
                    fileRootPath,
                    pictureIndex);

                pictureIndex = 1;

                var stabilityChartsPage = new StabilityChartsPage(
                    page,
                    _options);

                await stabilityChartsPage.GotoAsync();
                await stabilityChartsPage.InterceptRequestsForFiltersAsync();

                await GetStabilityHistoryChartsAsync(
                    stabilityChartsPage,
                    report,
                    fileRootPath,
                    pictureIndex);

                await GetLastSafetyFactorChartsAsync(
                    stabilityChartsPage,
                    report,
                    fileRootPath,
                    pictureIndex);

                await DownloadClientLogoAsync(report, fileRootPath);
            }
            catch (Exception e)
            {
                throw;
            }
            finally
            {
                await page.CloseAsync();
            }
        }

        private async Task AuthenticateAsync(LoginPage page)
        {
            await page.GotoAsync();
            await page.AuthenticateAsync();
        }

        private async Task GetStructureMapsAsync(
            StructurePage structurePage,
            PendingReport report,
            string fileRootPath)
        {
            var structureId = report.Configuration.StructureId;
            await structurePage.GotoAsync(structureId);

            var path = report.GetStructureMapPath(fileRootPath);
            var success = await structurePage.CaptureStructureMapAsync(path);

            if (!success)
            {
                return;
            }

            report.Result.ImagePayload.Add(StructureBigPictureMap,
                new SimplePicture
                {
                    Path = path,
                    Width = StructureMapWidthInPixels,
                    Height = StructureMapHeightInPixels
                });
        }

        private async Task GetInstrumentMapsAsync(
            InstrumentListPage instrumentListPage,
            PendingReport report,
            string fileRootPath)
        {
            await instrumentListPage.GotoAsync();

            var instrumentNames = (report.RawData as GetEoRReportDataResponse)
                .Sections
                .SelectMany(section => section.Instruments)?
                .Where(instrument =>
                    instrument.Subtype == InstrumentSubtype.Percolation)?
                .Select(Instrument => Instrument.Name);

            var path = report.GetPercolationMapPath(fileRootPath);

            var success = await instrumentListPage.CaptureInstrumentsMapAsync(
                InstrumentSubtype.Percolation,
                instrumentNames,
                path);

            if (!success)
            {
                return;
            }

            report.Result.ImagePayload.Add(
                StructurePercolationInstrumentsMap,
                new SimplePicture
                {
                    Path = path,
                    Width = InstrumentMapWidthInPixels,
                    Height = InstrumentMapHeightInPixels
                });

            path = report.GetDisplacementMapPath(fileRootPath);

            instrumentNames = (report.RawData as GetEoRReportDataResponse)
                .Sections
                .SelectMany(section => section.Instruments)?
                .Where(instrument =>
                    instrument.Subtype == InstrumentSubtype.Displacement)?
                .Select(Instrument => Instrument.Name);

            success = await instrumentListPage.CaptureInstrumentsMapAsync(
                InstrumentSubtype.Displacement,
                instrumentNames,
                path);

            if (!success)
            {
                return;
            }

            report.Result.ImagePayload.Add(
                StructureDisplacementInstrumentsMap,
                new SimplePicture
                {
                    Path = path,
                    Width = InstrumentMapWidthInPixels,
                    Height = InstrumentMapHeightInPixels
                });
        }

        private async Task GetWaterLevelIndicatorAndPiezometerChartsAsync(
            InstrumentDetailsPage instrumentPage,
            PendingReport report,
            string fileRootPath,
            int pictureIndex)
        {
            var structureId = report.Configuration.StructureId;
            var rawData = report.RawData as GetEoRReportDataResponse;
            var imageResult = new Dictionary<string, ReportPicture>();
            var textTemplate = new StringBuilder();

            var allowedInstruments = new[]
            {
                InstrumentType.WaterLevelIndicator,
                InstrumentType.OpenStandpipePiezometer,
                InstrumentType.ElectricPiezometer,
            };

            foreach (var section in rawData.Sections)
            {
                textTemplate.AppendLine(
                    $"•\tDados de leitura de instrumentação - Seção {section.Name}");
                textTemplate.AppendLine();

                var linimetricRulerName = section
                    .Instruments
                    .FirstOrDefault(instrument =>
                        instrument.Type == InstrumentType.LinimetricRuler)?
                    .Name;

                var pluviometerName = section
                    .Instruments
                    .FirstOrDefault(instrument =>
                        instrument.Type == InstrumentType.Pluviometer)?
                    .Name;

                var elegibleInstruments = section
                    .Instruments
                    .Where(instrument =>
                        allowedInstruments.Contains(instrument.Type));

                foreach (var instrument in elegibleInstruments)
                {
                    var path = report.GetInstrumentChartPath(
                        fileRootPath,
                        instrument.Id);

                    await instrumentPage.GotoAsync(
                        structureId,
                        instrument.Id,
                        instrument.Type);

                    var success = await instrumentPage
                        .CaptureWaterLevelIndicatorOrPiezometerAsync(
                            linimetricRulerName,
                            pluviometerName,
                            path);

                    if (!success)
                    {
                        continue;
                    }

                    var imageKey = string.Format(
                        PercolationReadingChartsImage,
                        $"{instrument.Id}_{section.Id}");

                    textTemplate.AppendLine("{{" + imageKey + "}}");
                    textTemplate.AppendLine();

                    var picture = new EorPercolationReadingChartPicture
                    {
                        Path = path,
                        Width = InstrumentChartWidthInPixels,
                        Height = InstrumentChartHeightInPixels,
                        InstrumentName = instrument.Name,
                        Index = pictureIndex,
                    };

                    imageResult.Add(imageKey, picture);

                    pictureIndex++;
                }

                textTemplate.AppendLine();
            }

            report.Result.IntermediatePayload.Add(
                PercolationReadingCharts,
                textTemplate.ToString());

            report.Result.ImagePayload.AddRange(imageResult);
        }

        private async Task GetSurfaceLandmarkAndPrismChartsAsync(
            InstrumentDetailsPage instrumentPage,
            PendingReport report,
            string fileRootPath,
            int pictureIndex)
        {
            var structureId = report.Configuration.StructureId;
            var rawData = report.RawData as GetEoRReportDataResponse;
            var imageResult = new Dictionary<string, ReportPicture>();
            var textTemplate = new StringBuilder();

            var allowedInstruments = new[]
            {
                InstrumentType.SurfaceLandmark,
                InstrumentType.Prism
            };

            foreach (var section in rawData.Sections)
            {
                var eligibleInstruments = section
                    .Instruments
                    .Where(instrument =>
                        allowedInstruments.Contains(instrument.Type));

                foreach (var instrument in eligibleInstruments)
                {
                    await instrumentPage.GotoAsync(
                        structureId,
                        instrument.Id,
                        instrument.Type);

                    foreach (var axis in Enum.GetValues<Axis>())
                    {
                        var path = report.GetInstrumentChartPath(
                            fileRootPath,
                            $"{instrument.Id}_{axis}");

                        var success = await instrumentPage
                            .CaptureSurfaceLandmarkOrPrismAsync(
                                report.Configuration.StartDate,
                                report.Configuration.EndDate,
                                axis,
                                path);

                        if (!success)
                        {
                            continue;
                        }

                        var imageKey = string.Format(
                            SurfaceLandmarkAndPrismReadingChartsImage,
                            $"{instrument.Id}_{axis}_{section.Id}");

                        textTemplate.AppendLine("{{" + imageKey + "}}");
                        textTemplate.AppendLine();

                        var picture = new EorDisplacementReadingChartPicture
                        {
                            Path = path,
                            Width = InstrumentChartWidthInPixels,
                            Height = InstrumentChartHeightInPixels,
                            InstrumentName = instrument.Name,
                            Index = pictureIndex
                        };

                        imageResult.Add(imageKey, picture);

                        pictureIndex++;
                    }
                }
            }

            report.Result.IntermediatePayload.Add(
                SurfaceLandmarkAndPrismReadingCharts,
                textTemplate.ToString());

            report.Result.ImagePayload.AddRange(imageResult);
        }

        private async Task GetPluviometryAndWaterLevelindicatorChartsAsync(
            InstrumentDetailsPage instrumentPage,
            PendingReport report,
            string fileRootPath,
            int pictureIndex)
        {
            var structureId = report.Configuration.StructureId;
            var rawData = report.RawData as GetEoRReportDataResponse;
            var textTemplate = new StringBuilder();
            var imageResult = new Dictionary<string, ReportPicture>();

            foreach (var section in rawData.Sections)
            {
                var path = report.GetInstrumentChartPath(
                    fileRootPath,
                    section.Id);

                var waterlevelIndicatorId = section
                    .Instruments
                    .FirstOrDefault(instrument =>
                        instrument.Type == InstrumentType.WaterLevelIndicator)?
                    .Id;

                var linimetricRulerName = section
                    .Instruments
                    .FirstOrDefault(instrument =>
                        instrument.Type == InstrumentType.LinimetricRuler)?
                    .Name;

                var pluviometerName = section
                    .Instruments
                    .FirstOrDefault(instrument =>
                        instrument.Type == InstrumentType.Pluviometer)?
                    .Name;

                if (!waterlevelIndicatorId.HasValue)
                {
                    continue;
                }

                await instrumentPage.GotoAsync(
                    structureId,
                    waterlevelIndicatorId.Value,
                    InstrumentType.WaterLevelIndicator);

                var success = await instrumentPage
                    .CapturePluviometryAndWaterLevelAsync(
                        linimetricRulerName,
                        pluviometerName,
                        path);

                if (!success)
                {
                    continue;
                }

                var imageKey = string.Format(
                    PluviometryAndWaterLevelChartsImage,
                    section.Id.ToString());

                textTemplate.AppendLine("{{" + imageKey + "}}");
                textTemplate.AppendLine();

                var picture = new EorPluviometryAndWaterLevelReadingChartPicture
                {
                    Path = path,
                    Width = InstrumentChartWidthInPixels,
                    Height = InstrumentChartHeightInPixels,
                    StartDate = report.Configuration.StartDate,
                    EndDate = report.Configuration.EndDate,
                    SectionName = section.Name,
                    Index = pictureIndex
                };

                imageResult.Add(imageKey, picture);

                pictureIndex++;
            }

            report.Result.IntermediatePayload.Add(
                PluviometryAndWaterLevelCharts,
                textTemplate.ToString());

            report.Result.ImagePayload.AddRange(imageResult);
        }

        private async Task GetInclinometerChartsAsync(
            InstrumentDetailsPage instrumentPage,
            PendingReport report,
            string fileRootPath,
            int pictureIndex)
        {
            var structureId = report.Configuration.StructureId;
            var rawData = report.RawData as GetEoRReportDataResponse;
            var imageResult = new Dictionary<string, ReportPicture>();
            var textTemplate = new StringBuilder();

            var allowedInstruments = new[]
            {
                InstrumentType.IPIInclinometer,
                InstrumentType.ConventionalInclinometer,
            };

            foreach (var section in rawData.Sections)
            {
                var elegibleInstruments = section
                    .Instruments
                    .Where(instrument =>
                        allowedInstruments.Contains(instrument.Type));

                foreach (var instrument in elegibleInstruments)
                {
                    var aPath = report.GetInstrumentChartPath(
                        fileRootPath,
                        $"{instrument.Id}_A");

                    var bPath = report.GetInstrumentChartPath(
                        fileRootPath,
                        $"{instrument.Id}_B");

                    await instrumentPage.GotoAsync(
                        structureId,
                        instrument.Id,
                        instrument.Type);

                    var success = await instrumentPage
                        .CaptureInclinometerAsync(
                            report.Configuration.StartDate,
                            report.Configuration.EndDate,
                            aPath,
                            bPath);

                    if (!success)
                    {
                        continue;
                    }

                    var aImageKey = string.Format(
                        InclinometerReadingChartsImage,
                        $"{instrument.Id}_{section.Id}_A");

                    textTemplate.AppendLine("{{" + aImageKey + "}}");
                    textTemplate.AppendLine();

                    var aPicture = new EorDisplacementReadingChartPicture
                    {
                        Path = aPath,
                        Width = InstrumentChartWidthInPixels,
                        Height = InstrumentChartHeightInPixels,
                        InstrumentName = instrument.Name,
                        Index = pictureIndex
                    };

                    imageResult.Add(aImageKey, aPicture);

                    pictureIndex++;

                    var bImageKey = string.Format(
                        InclinometerReadingChartsImage,
                        $"{instrument.Id}_{section.Id}_A");

                    textTemplate.AppendLine("{{" + bImageKey + "}}");
                    textTemplate.AppendLine();

                    var bPicture = new EorDisplacementReadingChartPicture
                    {
                        Path = bPath,
                        Width = InstrumentChartWidthInPixels,
                        Height = InstrumentChartHeightInPixels,
                        InstrumentName = instrument.Name,
                        Index = pictureIndex
                    };

                    imageResult.Add(bImageKey, bPicture);

                    pictureIndex++;
                }

                textTemplate.AppendLine();
            }

            report.Result.IntermediatePayload.Add(
                InclinometerReadingCharts,
                textTemplate.ToString());

            report.Result.ImagePayload.AddRange(imageResult);
        }

        private async Task GetSettlementGaugeChartsAsync(
            InstrumentDetailsPage instrumentPage,
            PendingReport report,
            string fileRootPath,
            int pictureIndex)
        {
            var structureId = report.Configuration.StructureId;
            var rawData = report.RawData as GetEoRReportDataResponse;
            var imageResult = new Dictionary<string, ReportPicture>();
            var textTemplate = new StringBuilder();

            var allowedInstruments = new[]
            {
                InstrumentType.SettlementGauge
            };

            foreach (var section in rawData.Sections)
            {
                var elegibleInstruments = section
                    .Instruments
                    .Where(instrument =>
                        allowedInstruments.Contains(instrument.Type));

                foreach (var instrument in elegibleInstruments)
                {
                    var path = report.GetInstrumentChartPath(
                        fileRootPath,
                        instrument.Id);

                    await instrumentPage.GotoAsync(
                        structureId,
                        instrument.Id,
                        instrument.Type);

                    var success = await instrumentPage
                        .CaptureSettlementGaugeAsync(
                            report.Configuration.StartDate,
                            report.Configuration.EndDate,
                            path);

                    if (!success)
                    {
                        continue;
                    }

                    var imageKey = string.Format(
                        SettlementGaugeReadingChartsImage,
                        $"{instrument.Id}_{section.Id}");

                    textTemplate.AppendLine("{{" + imageKey + "}}");
                    textTemplate.AppendLine();

                    var picture = new EorDisplacementReadingChartPicture
                    {
                        Path = path,
                        Width = InstrumentChartWidthInPixels,
                        Height = InstrumentChartHeightInPixels,
                        InstrumentName = instrument.Name,
                        Index = pictureIndex
                    };

                    imageResult.Add(imageKey, picture);

                    pictureIndex++;
                }

                textTemplate.AppendLine();
            }

            report.Result.IntermediatePayload.Add(
                SettlementGaugeReadingCharts,
                textTemplate.ToString());

            report.Result.ImagePayload.AddRange(imageResult);
        }

        private async Task GetStabilityHistoryChartsAsync(
            StabilityChartsPage stabilityChartsPage,
            PendingReport report,
            string fileRootPath,
            int pictureIndex)
        {
            var imageResult = new Dictionary<string, ReportPicture>();
            var textTemplate = new StringBuilder();

            await stabilityChartsPage.GotoAsync();

            var soilConditionTypes = Enum.GetValues<SoilConditionType>();

            foreach (var surfaceType in stabilityChartsPage.SurfaceTypeFilters)
            {
                await stabilityChartsPage.ClearFormAsync();

                var hasData = await stabilityChartsPage
                    .ShowStabilityHistoryChartsAsync(
                        surfaceType,
                        report.Configuration.StartDate,
                        report.Configuration.EndDate);

                if (!hasData)
                {
                    continue;
                }

                foreach (var soilConditionType in soilConditionTypes)
                {
                    var path = report.GetStabilityChartPath(
                        fileRootPath,
                        surfaceType,
                        soilConditionType);

                    var success = await stabilityChartsPage
                        .GetStabilityHistoryChartAsync(
                            soilConditionType,
                            path);

                    if (!success)
                    {
                        continue;
                    }

                    var imageKey = string.Format(
                        HistorycalStabilityAnalisysChartsImage,
                        $"{surfaceType}_{soilConditionType}");

                    textTemplate.AppendLine("{{" + imageKey + "}}");
                    textTemplate.AppendLine();

                    var picture = new EorStabilityHistoryChartPicture
                    {
                        Path = path,
                        Width = StabilityChartWidthInPixels,
                        Height = StabilityChartHeightInPixels,
                        SoilConditionType = soilConditionType,
                        StartDate = report.Configuration.StartDate,
                        Template = report.Configuration.Template,
                        Index = pictureIndex
                    };

                    imageResult.Add(imageKey, picture);

                    pictureIndex++;
                }

                textTemplate.AppendLine();
            }

            report.Result.IntermediatePayload.Add(
                HistorycalStabilityAnalisysCharts,
                textTemplate.ToString());

            report.Result.ImagePayload.AddRange(imageResult);
        }

        private async Task GetLastSafetyFactorChartsAsync(
            StabilityChartsPage stabilityChartsPage,
            PendingReport report,
            string fileRootPath,
            int pictureIndex)
        {
            var imageResult = new Dictionary<string, ReportPicture>();
            var textTemplate = new StringBuilder();

            await stabilityChartsPage.GotoAsync();

            var soilConditionTypes = Enum.GetValues<SoilConditionType>();
            var dateIntervals = report.GetSafetyFactorDates();

            foreach (var surfaceType in stabilityChartsPage.SurfaceTypeFilters)
            {
                foreach (var (startDate, endDate) in dateIntervals)
                {
                    await stabilityChartsPage.ClearFormAsync();

                    var hasData = await stabilityChartsPage
                        .ShowStabilityHistoryChartsAsync(
                            surfaceType,
                            startDate,
                            endDate);

                    if (!hasData)
                    {
                        continue;
                    }

                    foreach (var soilConditionType in soilConditionTypes)
                    {
                        foreach (var section in report.RawData.Sections)
                        {
                            var path = report.GetStabilityHistoryChartPath(
                                fileRootPath,
                                surfaceType,
                                soilConditionType,
                                (Guid)section.Id,
                                startDate);

                            var success = await stabilityChartsPage
                                .GetLastSafetyFactorImageAsync(
                                    section.Name,
                                    soilConditionType,
                                    path);

                            if (!success)
                            {
                                continue;
                            }

                            var imageKey = string.Format(
                                StabilityAnalisysResultsImage,
                                $"{surfaceType}_{soilConditionType}_{section.Id}_{startDate:ddMMyyy}");

                            textTemplate.AppendLine("{{" + imageKey + "}}");
                            textTemplate.AppendLine();

                            using var image = await Image.LoadAsync(path);
                            var picture = new EorStabilityChartPicture
                            {
                                Path = path,
                                Width = LastSafetyFactorMaxWidthInPixels,
                                Height =
                                    image.ReduceHeight(
                                        LastSafetyFactorMaxWidthInPixels),
                                StartDate = startDate,
                                EndDate = endDate,
                                SoilConditionType = soilConditionType,
                                Template = report.Configuration.Template,
                                Index = pictureIndex
                            };

                            imageResult.Add(imageKey, picture);

                            pictureIndex++;
                        }

                        textTemplate.AppendLine();
                    }
                }
            }

            report.Result.IntermediatePayload.Add(
                StabilityAnalisysResults,
                textTemplate.ToString());

            report.Result.ImagePayload.AddRange(imageResult);
        }

        private async Task DownloadClientLogoAsync(
            PendingReport report,
            string fileRootPath)
        {
            byte[] logoBytes = await _blobStorageService
                .GetAsync(
                    report.RawData.Client.LogoUniqueName,
                    _blobStorageOptions.ClientsContainer);

            if (logoBytes == null)
            {
                return;
            }

            var path = report.GetClientLogoPath(fileRootPath);

            await File.WriteAllBytesAsync(path, logoBytes);

            using var image = await Image.LoadAsync(path);

            report.Result.ImagePayload.Add(ClientLogo,
                new SimplePicture
                {
                    Path = path,
                    Width = ClientLogoMaxWidthInPixels,
                    Height = image.ReduceHeight(ClientLogoMaxWidthInPixels)
                });
        }
    }
}