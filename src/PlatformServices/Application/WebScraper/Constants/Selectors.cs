namespace Application.WebScraper.Constants
{
    public static class Selectors
    {
        public static class HtmlTags
        {
            public const string Canvas = "canvas";
            public const string Button = "button";
            public const string Div = "div";
            public const string Span = "span";
        }

        public static class Shared
        {
            public const string StructureMenu = ".dropdown-structure > button";
            public const string LoadButton = "Carregar";
            public const string NgMultiSelectDropdown = "ng-multiselect-dropdown";
            public const string SelectDropdownText = "Selecione";
            public const string StartDateText = "Data inicial";
            public const string EndDateText = "Data final";
            public const string StartDateAndTimeText = "Data e hora inicial:";
            public const string StartDateAndTimeText2 = "Data e hora inicial";
            public const string EndDateAndTimeText = "Data e hora final:";
            public const string EndDateAndTimeText2 = "Data e hora final";
            public const string GenerateChartButtonText = "Gera<PERSON> gr<PERSON>fico";
            public const string MapFullscreenButtonLabel = "Ativar a visualização em tela cheia";
            public const string AppHierarchy = "app-hierarchy";
            public const int FirstItemIndex = 0;
            public const int SecondItemIndex = 1;
            public const int ThirdItemIndex = 2;
            public const int FourthItemIndex = 3;
            public const int FifthItemIndex = 4;
        }

        public static class Login
        {
            public const string UsernameInput = "Nome de usuário ou e-mail";
            public const string PasswordInput = "Senha";
            public const string SignInButton = "Entrar";
        }

        public static class Structure
        {
            public const string StructureMapFullscreenButton = "#mapStructure";
            public const string InstrumentsMapFullscreenButton = "#mapInstruments";
        }

        public static class Instrument
        {
            public const string ChartConfigurationButton = "Configurações do gráfico";
            public const string StructureWaterLevelToggleButton = "NA Reservatório (m) - Opcional";
            public const string PluviometryToggleButton = "Pluviometria (mm) - Opcional";
            public const string MultiSelectDropdown = ".dropdown-multiselect__caret";
            public const string SelectAllDropdownText = "Selecionar todos";
            public const string PluviometerText = "Pluviômetro-";
            public const string LinimetricRulerText = "RL-";
            public const string LineWidthRegex = "^Espessura da linha:$";
            public const string XAxisIntervalRegex = "^Intervalo eixo X:$";
            public const string NoDataToGenerateChart = "Não há dados para gerar gráfico";
            public const string FourthFormSelectChild = "div:nth-child(4) > .form-select";
            public const string EighthListItemChild = ".active > li:nth-child(8) > a";
            public const string SearchRegex = "^.*Buscar";
            public const string CloseButton = "Fechar";
            public const string SeeSectionsButton = "Ver Seções";
            public const string SelectTheSubtypesText = "Selecione o(s) subtipo(s)";
            public const string Item2Class = ".item2";
            public const string FirstRowItemClass = "tr:nth-child(1) > .d-flex";
            public const string NullId = "#null";
            public const string DataNotFound = "Nenhum registro encontrado.";
        }

        public static class StabilityCharts
        {
            public const string UndrainedSafetyFactors = "Fatores de Segurança para a condição Não Drenada";
            public const string DrainedSafetyFactors = "Fatores de Segurança para a condição Drenada";
            public const string PseudoStaticSafetyFactors = "Fatores de Segurança para a condição Pseudo estática";
            public const string EmptySafetyFactors = "Não há fatores de segurança";
            public const string SeeAnalysisButton = "Ver análise";
            public const string ShowImageButton = "Exibir imagem";
            public const string DownloadPngButton = "^.*Download PNG";
            public const string CloseButton = "Close";
        }
    }
}
