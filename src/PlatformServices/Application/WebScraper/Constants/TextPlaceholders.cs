namespace Application.WebScraper.Constants
{
    public static class TextPlaceholders
    {
        public static class Eor
        {
            public const string ClientLogo = "ClientLogo";
            public const string ClientName = "ClientName";

            public const string StructureName = "StructureName";
            public const string StructureDatum = "StructureDatum";
            public const string StructureCoordinateEasting = "StructureCoordinateEasting";
            public const string StructureCoordinateNorthing = "StructureCoordinateNorthing";
            public const string StructureConstructionStages = "StructureConstructionStages";
            public const string StructureCrestDimensionWidth = "StructureCrestDimensionWidth";
            public const string StructureCrestDimensionLength = "StructureCrestDimensionLength";
            public const string StructureCrestQuota = "StructureCrestQuota";
            public const string StructureTotalHeight = "StructureTotalHeight";
            public const string StructureYearOfConstructionInitialDike = "StructureYearOfConstructionInitialDike";
            public const string StructureSectionType = "StructureSectionType";
            public const string StructureFoundationType = "StructureFoundationType";
            public const string StructureRaisingMethod = "StructureRaisingMethod";
            public const string StructureExpectedElevations = "StructureExpectedElevations";
            public const string StructureElevationsMade = "StructureElevationsMade";
            public const string StructureUpstreamSlope = "StructureUpstreamSlope";
            public const string StructureDownstreamSlope = "StructureDownstreamSlope";
            public const string StructureInternalDrainage = "StructureInternalDrainage";
            public const string StructureSuperficialDrainage = "StructureSuperficialDrainage";
            public const string StructureReservoirDesignVolume = "StructureReservoirDesignVolume";
            public const string StructureCurrentReservoirVolume = "StructureCurrentReservoirVolume";
            public const string StructureProjectPrecipitation = "StructureProjectPrecipitation";
            public const string StructureFullOfProject = "StructureFullOfProject";
            public const string StructureProjectFlow = "StructureProjectFlow";
            public const string StructureMaximumInfluentFlow = "StructureMaximumInfluentFlow";
            public const string StructureNormalMaximumWaterLevel = "StructureNormalMaximumWaterLevel";
            public const string StructureMaximumWaterLevelMaximorum = "StructureMaximumWaterLevelMaximorum";
            public const string StructureFreeboardNormalMaximumWaterLevel = "StructureFreeboardNormalMaximumWaterLevel";
            public const string StructureFreeboardMaximumWaterLevelMaximorum = "StructureFreeboardMaximumWaterLevelMaximorum";
            public const string StructureSpillway = "StructureSpillway";
            public const string StructureBigPictureMap = "StructureBigPictureMap";
            public const string StructurePercolationInstrumentsMap = "StructurePercolationInstrumentsMap";
            public const string StructureDisplacementInstrumentsMap = "StructureDisplacementInstrumentsMap";
            public const string StructurePurpose = "StructurePurpose";
            public const string StructureStatus = "StructureStatus";
            public const string StructureBasinAreaInSquareKilometers = "StructureBasinAreaInSquareKilometers";
            public const string StructureNumberOfPiezometers = "StructureNumberOfPiezometers";
            public const string StructureNumberOfPluviographs = "StructureNumberOfPluviographs";
            public const string StructureNumberOfPluviometers = "StructureNumberOfPluviometers";
            public const string StructureNumberOfWaterLevelIndicators = "StructureNumberOfWaterLevelIndicators";
            public const string StructureNumberOfSurfaceLandmarks = "StructureNumberOfSurfaceLandmarks";
            public const string StructureNumberOfInclinometers = "StructureNumberOfInclinometers";
            public const string StructureNumberSettlementGauges = "StructureNumberSettlementGauges";

            public const string StructureResponsibles = "StructureResponsibles";
            public const string StructureResponsiblesName = "Name";
            public const string StructureResponsiblesNameText = "StructureResponsibles.Name";
            public const string StructureResponsiblesIndex = "Index";
            public const string StructureResponsiblesIndexText = "StructureResponsibles.Index";
            public const string StructureResponsiblesProfessionalRecord = "ProfessionalRecord";
            public const string StructureResponsiblesProfessionalRecordText = "StructureResponsibles.ProfessionalRecord";
            public const string StructureResponsiblesRoleName = "RoleName";
            public const string StructureResponsiblesRoleNameText = "StructureResponsibles.RoleName";
            public const string StructureResponsiblesPositionName = "PositionName";
            public const string StructureResponsiblesPositionNameText = "StructureResponsibles.PositionName";
            public const string StructureResponsiblesEmailAddress = "EmailAddress";
            public const string StructureResponsiblesEmailAddressText = "StructureResponsibles.EmailAddress";
            public const string StructureResponsiblesCompanyName = "CompanyName";
            public const string StructureResponsiblesCompanyNameText = "StructureResponsibles.CompanyName";
            public const string StructureResponsiblesResponsibilities = "Responsibilities";
            public const string StructureResponsiblesResponsibilitiesText = "StructureResponsibles.Responsibilities";
            public const string StructureResponsiblesPhoneNumber = "PhoneNumber";
            public const string StructureResponsiblesPhoneNumberText = "StructureResponsibles.PhoneNumber";

            public const string ClientUnitName = "ClientUnitName";
            public const string ClientUnitCity = "ClientUnitCity";

            public const string ReadingsNumberOfPiezometers = "ReadingsNumberOfPiezometers";
            public const string ReadingsNumberOfPluviographs = "ReadingsNumberOfPluviographs";
            public const string ReadingsNumberOfPluviometers = "ReadingsNumberOfPluviometers";
            public const string ReadingsNumberOfWaterLevelIndicators = "ReadingsNumberOfWaterLevelIndicators";
            public const string ReadingsNumberOfSurfaceLandmarks = "ReadingsNumberOfSurfaceLandmarks";
            public const string ReadingsNumberOfInclinometers = "ReadingsNumberOfInclinometers";
            public const string ReadingsNumberSettlementGauges = "ReadingsNumberSettlementGauges";

            public const string ReportStartDate = "ReportStartDate";
            public const string ReportMonthOfStartDate = "ReportMonthOfStartDate";
            public const string ReportMonthOfEndDate = "ReportMonthOfEndDate";
            public const string ReportMonthAndYearOfStartDate = "ReportMonthAndYearOfStartDate";
            public const string ReportEndDate = "ReportEndDate";
            public const string ReportYearOfEndDate = "ReportYearOfEndDate";
            public const string ReportPreviousYearOfEndDate = "ReportPreviousYearOfEndDate";
            public const string ReportResponsibleName = "ReportResponsibleName";

            public const string PercolationReadingCharts = "PercolationReadingCharts";
            public const string PercolationReadingChartsImage = "PercolationReadingChartsImage{0}";
            public const string PercolationReadingChartsText = "PercolationReadingChartsText{0}";

            public const string PluviometryReportPeriodAccumulated = "PluviometryReportPeriodAccumulated";
            public const string PluviometryCurrentYearAccumulated = "PluviometryCurrentYearAccumulated";
            public const string PluviometryLastYearAccumulated = "PluviometryLastYearAccumulated";
            public const string PluviometryLastYearRelativeAverage = "PluviometryLastYearRelativeAverage";
            public const string PluviometryAllTimeRelativeAverage = "PluviometryAllTimeRelativeAverage";
            public const string PluviometryAllTimeAverage = "PluviometryAllTimeAverage";
            public const string PluviometryAndWaterLevelCharts = "PluviometryAndWaterLevelCharts";
            public const string PluviometryAndWaterLevelChartsImage = "PluviometryAndWaterLevelChartsImage{0}";
            public const string PluviometryAndWaterLevelChartsText = "PluviometryAndWaterLevelChartsText{0}";

            public const string SettlementGaugeReadingCharts = "SettlementGaugeReadingCharts";
            public const string SettlementGaugeReadingChartsImage = "SettlementGaugeReadingChartsImage{0}";
            public const string SettlementGaugeReadingChartsText = "SettlementGaugeReadingChartsText{0}";

            public const string SurfaceLandmarkAndPrismReadingCharts = "SurfaceLandmarkAndPrismReadingCharts";
            public const string SurfaceLandmarkAndPrismReadingChartsImage = "SurfaceLandmarkAndPrismReadingChartsImage{0}";
            public const string SurfaceLandmarkAndPrismReadingChartsText = "SurfaceLandmarkAndPrismReadingChartsText{0}";

            public const string InclinometerReadingCharts = "InclinometerReadingCharts";
            public const string InclinometerReadingChartsImage = "InclinometerReadingChartsImage{0}";
            public const string InclinometerReadingChartsText = "InclinometerReadingChartsText{0}";

            public const string StabilityAnalisysResults = "StabilityAnalisysResults";
            public const string StabilityAnalisysResultsImage = "StabilityAnalisysResultsImage{0}";
            public const string StabilityAnalisysResultsText = "StabilityAnalisysResultsText{0}";

            public const string HistorycalStabilityAnalisysCharts = "HistorycalStabilityAnalisysCharts";
            public const string HistorycalStabilityAnalisysChartsImage = "HistorycalStabilityAnalisysChartsImage{0}";
            public const string HistorycalStabilityAnalisysChartsText = "HistorycalStabilityAnalisysChartsText{0}";

            public const string EoRInspectionPhotos = "EoRInspectionPhotos";

            public const string ReportEmissionDate = "ReportEmissionDate";

            public const string TableOfContents = "TableOfContents";
            public const string ResponsiblesTable = "{{ResponsiblesTable}}";
            public const string PercolationReadingsTable = "PercolationReadingsTable";
            public const string SurfaceLandmarkInfoTable = "SurfaceLandmarkInfoTable";
            public const string SurfaceLandmarkReadingsTable = "SurfaceLandmarkReadingsTable";
            public const string SettlementGaugeInfoTable = "SettlementGaugeInfoTable";
            public const string SettlementGaugeReadingsTable = "SettlementGaugeReadingsTable";
            public const string InclinometerInfoTable = "InclinometerInfoTable";
            public const string InclinometerReadingsTable = "InclinometerReadingsTable";

            public const string StabilityTable = "StabilityTable";
            public const string StabilityTableOKImage = "StabilityTableOKImage";
            public const string StabilityTableAlertImage = "StabilityTableAlertImage";
        }
    }
}
