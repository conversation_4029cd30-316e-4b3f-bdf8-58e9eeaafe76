using Domain.Enums;

namespace Application.WebScraper.Extensions
{
    public static class SoilConditionTypeExtensions
    {
        public static string ToStabilityPageSelectValue(
            this SoilConditionType surfaceType)
        {
            return surfaceType switch
            {
                SoilConditionType.Drained => "1: 1",
                SoilConditionType.Undrained => "2: 2",
                SoilConditionType.PseudoStatic => "3: 3",
                _ => throw new NotSupportedException("The selected soil condition type is not supported.")
            };
        }
    }
}
