using Domain.Enums;

namespace Application.WebScraper.Extensions
{
    internal static class InstrumentSubtypeExtensions
    {
        public static string ToInstrumentPageSelectValue(
            this InstrumentSubtype instrumentSubtype)
        {
            return instrumentSubtype switch
            {
                InstrumentSubtype.Percolation => "1: 1",
                InstrumentSubtype.Displacement => "2: 2",
                _ => throw new NotSupportedException("The selected instrument subtype is not supported.")
            };
        }

        public static string ToInstrumentPageMultiSelectValue(
           this InstrumentSubtype instrumentSubtype)
        {
            return instrumentSubtype switch
            {
                InstrumentSubtype.Percolation => "Percolação",
                InstrumentSubtype.Displacement => "Deslocamentos",
                _ => throw new NotSupportedException("The selected instrument subtype is not supported.")
            };
        }
    }
}
