using Domain.Enums;

namespace Application.WebScraper.Extensions
{
    public static class SurfaceTypeExtensions
    {
        public static string ToChartPageSelectValue(
            this SurfaceType surfaceType)
        {
            return surfaceType switch
            {
                SurfaceType.Circular => "1: 1",
                SurfaceType.NonCircular => "2: 2",
                _ => throw new NotSupportedException("The selected surface type is not supported.")
            };
        }
    }
}
