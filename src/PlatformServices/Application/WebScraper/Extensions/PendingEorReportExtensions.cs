using Application.Report;
using Domain.Enums;

namespace Application.WebScraper.Extensions
{
    public static class PendingEorReportExtensions
    {
        public static string GetStructureMapPath(
            this PendingReport report,
            string fileRootPath)
        {
            return Path.Combine(
                report.GetOutputImageDirectory(fileRootPath),
                $"structure-map.png");
        }

        public static string GetPercolationMapPath(
            this PendingReport report,
            string fileRootPath)
        {
            return Path.Combine(
                report.GetOutputImageDirectory(fileRootPath),
                $"percolation-map.png");
        }

        public static string GetDisplacementMapPath(
            this PendingReport report,
            string fileRootPath)
        {
            return Path.Combine(
                report.GetOutputImageDirectory(fileRootPath),
                $"displacement-map.png");
        }

        public static string GetInstrumentChartPath(
            this PendingReport report,
            string fileRootPath,
            Guid id)
        {
            return Path.Combine(
                report.GetOutputImageDirectory(fileRootPath),
                $"instrument-chart_{id}.png");
        }

        public static string GetInstrumentChartPath(
            this PendingReport report,
            string fileRootPath,
            string id)
        {
            return Path.Combine(
                report.GetOutputImageDirectory(fileRootPath),
                $"instrument-chart_{id}.png");
        }

        public static string GetStabilityChartPath(
            this PendingReport report,
            string fileRootPath,
            string surfaceType,
            SoilConditionType soilConditionType)
        {
            return Path.Combine(
                report.GetOutputImageDirectory(fileRootPath),
                $"stability-chart_{surfaceType}_{soilConditionType}.png");
        }

        public static string GetStabilityHistoryChartPath(
            this PendingReport report,
            string fileRootPath,
            string surfaceType,
            SoilConditionType soilConditionType,
            Guid sectionId,
            DateTime startDate)
        {
            return Path.Combine(
                report.GetOutputImageDirectory(fileRootPath),
                $"stability-chart_{surfaceType}_{soilConditionType}_{sectionId}_{startDate:ddMMyyy}.png");
        }

        public static string GetClientLogoPath(
            this PendingReport report,
            string fileRootPath)
        {
            return Path.Combine(
                report.GetOutputImageDirectory(fileRootPath),
                report.RawData.Client.LogoName);
        }
    }
}
