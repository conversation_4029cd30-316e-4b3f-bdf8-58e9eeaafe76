using Microsoft.Playwright;
using static Application.WebScraper.Constants.Selectors.Shared;

namespace Application.WebScraper.PageObjectModels
{
    public sealed class HomePage
    {
        private readonly IPage _page;
        private readonly WebScraperOptions _options;
        private readonly ILocator _structureMenu;
        private readonly ILocator _clientCombobox;
        private readonly ILocator _clientUnitCombobox;
        private readonly ILocator _structureCombobox;
        private readonly ILocator _loadButton;

        public HomePage(
            IPage page,
            WebScraperOptions options)
        {
            _page = page;
            _options = options;
            _structureMenu = _page.Locator(StructureMenu);
            _clientCombobox = _page.GetByRole(AriaRole.Combobox).First;
            _clientUnitCombobox = _page.GetByRole(AriaRole.Combobox).Nth(SecondItemIndex);
            _structureCombobox = _page.GetByRole(AriaRole.Combobox).Nth(ThirdItemIndex);
            _loadButton = _page.GetByRole(AriaRole.Button).GetByText(LoadButton);
        }

        public async Task GotoAsync(
            Guid structureId)
        {
            var url = $"{_options.LogisoilFrontendUri}";
            await _page.GotoAsync(url);
        }

        public async Task FillStructureAsync(
            string clientId,
            string clientUnitId,
            string structureId)
        {
            await _structureMenu.ClickAsync();
            await _clientCombobox.First.SelectOptionAsync(clientId);
            await _clientUnitCombobox.SelectOptionAsync(clientUnitId);
            await _structureCombobox.SelectOptionAsync(structureId);
            await _loadButton.ClickAsync();
        }
    }
}
