using Domain.Enums;
using Microsoft.Playwright;
using System.Text.RegularExpressions;
using static Application.WebScraper.Constants.InputValues.Instrument;
using static Application.WebScraper.Constants.RequestUrls;
using static Application.WebScraper.Constants.Selectors.HtmlTags;
using static Application.WebScraper.Constants.Selectors.Instrument;
using static Application.WebScraper.Constants.Selectors.Shared;

namespace Application.WebScraper.PageObjectModels
{
    public sealed class InstrumentDetailsPage
    {
        private readonly IPage _page;
        private readonly WebScraperOptions _options;
        private readonly Dictionary<Axis, string> _axisSelectValues;
        private readonly ILocator _monthFilterCombobox;
        private readonly ILocator _chartConfigurationButton;
        private readonly ILocator _lineWidthSpinButton;
        private readonly ILocator _xAxisIntervalSpinButton;
        private readonly ILocator _structureWaterLevelToggleButton;
        private readonly ILocator _structureWaterLevelMultiSelect;
        private readonly ILocator _pluviometryToggleButton;
        private readonly ILocator _pluviometryMultiSelect;
        private readonly ILocator _generateChartButton;
        private readonly ILocator _emptyChartDiv;
        private readonly ILocator _chartCanvas;
        private readonly ILocator _aChartCanvas;
        private readonly ILocator _bChartCanvas;
        private readonly ILocator _startDateInput;
        private readonly ILocator _endDateInput;
        private readonly ILocator _axisCombobox;

        public InstrumentDetailsPage(
            IPage page,
            WebScraperOptions options)
        {
            _page = page;
            _options = options;
            _axisSelectValues = new Dictionary<Axis, string>
            {
                [Axis.A] = "0: 1",
                [Axis.B] = "1: 2",
                [Axis.Z] = "2: 3",
                [Axis.N] = "3: 4",
                [Axis.E] = "4: 5",
                [Axis.Planimetric] = "5: 6"
            };

            _monthFilterCombobox = _page.GetByRole(AriaRole.Main)
                .GetByRole(AriaRole.Combobox);

            _chartConfigurationButton = _page.GetByRole(AriaRole.Button,
                new PageGetByRoleOptions
                {
                    Name = ChartConfigurationButton
                });

            _lineWidthSpinButton = _page.Locator(Div)
                .Filter(new LocatorFilterOptions
                {
                    HasTextRegex = new Regex(LineWidthRegex)
                })
                .GetByRole(AriaRole.Spinbutton);

            _xAxisIntervalSpinButton = _page.Locator(Div)
                .Filter(new LocatorFilterOptions
                {
                    HasTextRegex = new Regex(XAxisIntervalRegex)
                })
                .GetByRole(AriaRole.Spinbutton);

            _structureWaterLevelToggleButton = _page.GetByText(StructureWaterLevelToggleButton);

            _structureWaterLevelMultiSelect = _page.Locator(NgMultiSelectDropdown)
                .Filter(new LocatorFilterOptions
                {
                    HasText = SelectDropdownText
                })
                .Locator(Span)
                .Nth(FourthItemIndex);

            _pluviometryToggleButton = _page.GetByText(PluviometryToggleButton);

            _pluviometryMultiSelect = _page.Locator(NgMultiSelectDropdown)
                .Filter(new LocatorFilterOptions
                {
                    HasText = SelectDropdownText
                })
                .Locator(Span)
                .Nth(FourthItemIndex);

            _generateChartButton = _page.GetByRole(AriaRole.Button,
                new PageGetByRoleOptions
                {
                    Name = GenerateChartButtonText
                });

            _emptyChartDiv = _page.GetByText(NoDataToGenerateChart);

            _chartCanvas = _page.Locator(Canvas);
            _aChartCanvas = _page.Locator(Canvas).First;
            _bChartCanvas = _page.Locator(Canvas).Last;

            _startDateInput = _page.Locator(Div)
                .Filter(new LocatorFilterOptions
                {
                    HasText = StartDateAndTimeText,
                    HasNotText = EndDateAndTimeText
                })
                .GetByRole(AriaRole.Textbox);

            _endDateInput = _page.Locator(Div)
                .Filter(new LocatorFilterOptions
                {
                    HasText = EndDateAndTimeText,
                    HasNotText = StartDateAndTimeText
                })
                .GetByRole(AriaRole.Textbox);

            _axisCombobox = _page.GetByRole(AriaRole.Main)
                .GetByRole(AriaRole.Combobox);
        }

        public async Task GotoAsync(
            Guid structureId,
            Guid instrumentId,
            InstrumentType instrumentType)
        {
            var url =
                $"{_options.LogisoilFrontendUri}/instruments/{instrumentId}/chart?typeInstrument={instrumentType.GetHashCode()}&structure={structureId}";
            await _page.GotoAsync(url);
        }

        public async Task<bool> CaptureWaterLevelIndicatorOrPiezometerAsync(
            string linimetricRulerName,
            string pluviometerName,
            string path)
        {
            await _monthFilterCombobox.SelectOptionAsync(OneMonthSelectValue);
            await _chartConfigurationButton.ClickAsync();
            await _page.WaitForTimeoutAsync(100);

            await _lineWidthSpinButton.FillAsync(LineWidth);
            await _xAxisIntervalSpinButton.FillAsync(XAxisInterval);

            await _structureWaterLevelToggleButton.ClickAsync();
            await _structureWaterLevelMultiSelect.ClickAsync();

            if (!string.IsNullOrEmpty(linimetricRulerName))
            {
                await _page.GetByText(linimetricRulerName).ClickAsync();
            }

            await _structureWaterLevelToggleButton.ClickAsync();

            await _pluviometryToggleButton.ClickAsync();
            await _pluviometryMultiSelect.ClickAsync();

            if (!string.IsNullOrEmpty(pluviometerName))
            {
                await _page.GetByText(pluviometerName).ClickAsync();
            }

            await _pluviometryToggleButton.ClickAsync();

            var response = await _page.RunAndWaitForResponseAsync(
                async () => await _generateChartButton.ClickAsync(),
                GetPercolationCharts);

            if (response.Status != 200)
            {
                return false;
            }

            var isEmptyData = await _emptyChartDiv.IsVisibleAsync();
            if (isEmptyData)
            {
                return false;
            }

            await _chartCanvas.WaitForAsync(
                new LocatorWaitForOptions
                {
                    State = WaitForSelectorState.Visible
                });

            var byteArray = await _chartCanvas
                .ScreenshotAsync(new LocatorScreenshotOptions
                {
                    Path = path
                });

            return byteArray is { Length: > 0 };
        }

        public async Task<bool> CapturePluviometryAndWaterLevelAsync(
            string linimetricRulerName,
            string pluviometerName,
            string path)
        {
            await _page.Locator(MultiSelectDropdown).ClickAsync();
            await _page.GetByText(SelectAllDropdownText).ClickAsync();
            await _page.Locator(MultiSelectDropdown).ClickAsync();

            await _monthFilterCombobox.SelectOptionAsync(OneMonthSelectValue);
            
            await _chartConfigurationButton.ClickAsync();
            await _page.WaitForTimeoutAsync(100);

            await _lineWidthSpinButton.FillAsync(LineWidth);
            await _xAxisIntervalSpinButton.FillAsync(XAxisInterval);
            
            await _structureWaterLevelToggleButton.ClickAsync();
            await _structureWaterLevelMultiSelect.ClickAsync();

            if (!string.IsNullOrEmpty(linimetricRulerName))
            {
                await _page.GetByText(linimetricRulerName).ClickAsync();
            }

            await _structureWaterLevelToggleButton.ClickAsync();

            await _pluviometryToggleButton.ClickAsync();
            await _pluviometryMultiSelect.ClickAsync();

            if (!string.IsNullOrEmpty(pluviometerName))
            {
                await _page.GetByText(pluviometerName).ClickAsync();
            }

            await _pluviometryToggleButton.ClickAsync();

            var response = await _page.RunAndWaitForResponseAsync(
                async () => await _generateChartButton.ClickAsync(),
                GetPluviometryAndWaterLevelCharts);

            if (response.Status != 200)
            {
                return false;
            }

            var isEmptyData = await _emptyChartDiv.IsVisibleAsync();

            if (isEmptyData)
            {
                return false;
            }

            await _chartCanvas.WaitForAsync(
                new LocatorWaitForOptions
                {
                    State = WaitForSelectorState.Visible
                });

            var byteArray = await _chartCanvas
                .ScreenshotAsync(new LocatorScreenshotOptions
                {
                    Path = path
                });

            return byteArray is { Length: > 0 };
        }

        public async Task<bool> CaptureSurfaceLandmarkOrPrismAsync(
            DateTime startDate,
            DateTime endDate,
            Axis axis,
            string path)
        {
            await _startDateInput
                .PressSequentiallyAsync($"{startDate:MMdd00yyyyhhmmtt}");

            await _endDateInput
                .PressSequentiallyAsync($"{endDate:MMdd00yyyyhhmmtt}");

            await _axisCombobox.SelectOptionAsync(_axisSelectValues[axis]);

            var response = await _page.RunAndWaitForResponseAsync(
                async () => await _generateChartButton.ClickAsync(),
                GetSurfaceLandmarkOrPrismCharts);

            if (response.Status != 200)
            {
                return false;
            }

            var isEmptyData = await _emptyChartDiv.IsVisibleAsync();

            if (isEmptyData)
            {
                return false;
            }

            await _chartCanvas.WaitForAsync(
                new LocatorWaitForOptions
                {
                    State = WaitForSelectorState.Visible
                });

            var byteArray = await _chartCanvas
                .ScreenshotAsync(new LocatorScreenshotOptions
                {
                    Path = path
                });

            return byteArray is { Length: > 0 };
        }

        public async Task<bool> CaptureInclinometerAsync(
            DateTime startDate,
            DateTime endDate,
            string aPath,
            string bPath)
        {
            await _startDateInput
                .PressSequentiallyAsync($"{startDate:MMdd00yyyyhhmmtt}");

            await _endDateInput
                .PressSequentiallyAsync($"{endDate:MMdd00yyyyhhmmtt}");

            var response = await _page.RunAndWaitForResponseAsync(
                async () => await _generateChartButton.ClickAsync(),
                GetInclinometerCharts);

            if (response.Status == 200)
            {
                return false;
            }

            var isEmptyData = await _emptyChartDiv.IsVisibleAsync();

            if (isEmptyData)
            {
                return false;
            }

            await _aChartCanvas.WaitForAsync(
                new LocatorWaitForOptions
                {
                    State = WaitForSelectorState.Visible
                });

            var byteArray = await _aChartCanvas
                .ScreenshotAsync(new LocatorScreenshotOptions
                {
                    Path = aPath
                });

            await _bChartCanvas.WaitForAsync(
                new LocatorWaitForOptions
                {
                    State = WaitForSelectorState.Visible
                });

            byteArray = await _bChartCanvas
                .ScreenshotAsync(new LocatorScreenshotOptions
                {
                    Path = bPath
                });

            return byteArray is { Length: > 0 };
        }

        public async Task<bool> CaptureSettlementGaugeAsync(
            DateTime startDate,
            DateTime endDate,
            string path)
        {
            await _startDateInput
                .PressSequentiallyAsync($"{startDate:MMdd00yyyyhhmmtt}");

            await _endDateInput
                .PressSequentiallyAsync($"{endDate:MMdd00yyyyhhmmtt}");

            await _page.GetByRole(AriaRole.Main).GetByRole(AriaRole.Combobox).SelectOptionAsync("3: 3");

            var response = await _page.RunAndWaitForResponseAsync(
                async () => await _generateChartButton.ClickAsync(),
                GetSettlementGaugeCharts);

            if (response.Status == 200)
            {
                return false;
            }

            var isEmptyData = await _emptyChartDiv.IsVisibleAsync();

            if (isEmptyData)
            {
                return false;
            }

            await _chartCanvas.WaitForAsync(
                new LocatorWaitForOptions
                {
                    State = WaitForSelectorState.Visible
                });

            var byteArray = await _chartCanvas
                .ScreenshotAsync(new LocatorScreenshotOptions
                {
                    Path = path
                });

            return byteArray is { Length: > 0 };
        }
    }
}