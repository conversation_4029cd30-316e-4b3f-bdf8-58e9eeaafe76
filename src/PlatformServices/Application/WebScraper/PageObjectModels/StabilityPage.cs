using Microsoft.Playwright;

namespace Application.WebScraper.PageObjectModels
{
    public sealed class StabilityPage
    {
        private readonly IPage _page;
        private readonly WebScraperOptions _options;

        public StabilityPage(
            IPage page,
            WebScraperOptions options)
        {
            _page = page;
            _options = options;
        }

        public async Task GotoAsync(
            Guid structureId)
        {
            var url = $"{_options.LogisoilFrontendUri}/stability";
            await _page.GotoAsync(url);
        }
    }
}
