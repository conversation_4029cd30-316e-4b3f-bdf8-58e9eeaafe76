using Application.WebScraper.Extensions;
using Domain.Enums;
using Microsoft.Playwright;
using System.Net;
using System.Text.RegularExpressions;
using static Application.WebScraper.Constants.RequestUrls;
using static Application.WebScraper.Constants.Selectors.HtmlTags;
using static Application.WebScraper.Constants.Selectors.Shared;
using static Application.WebScraper.Constants.Selectors.StabilityCharts;

namespace Application.WebScraper.PageObjectModels
{
    public sealed class StabilityChartsPage
    {
        public List<string> SurfaceTypeFilters { get; } = new();
        private readonly IPage _page;
        private readonly WebScraperOptions _options;
        private readonly ILocator _startDateInput;
        private readonly ILocator _endDateInput;
        private readonly ILocator _surfaceTypeCombobox;
        private readonly ILocator _calculationMethodCombobox;
        private readonly ILocator _generateChartButton;
        private readonly ILocator _undrainedChartCanvas;
        private readonly ILocator _drainedChartCanvas;
        private readonly ILocator _pseudoStaticChartCanvas;
        private readonly ILocator _emptyChartDiv;
        private readonly ILocator _seeAnalysisButton;
        private readonly ILocator _sectionMultiSelect;
        private readonly ILocator _soilConditionTypeCombobox;
        private readonly ILocator _safetyFactorDateCombobox;
        private readonly ILocator _showImageButton;
        private readonly ILocator _downloadPngButton;
        private readonly ILocator _closeDialogButton;

        public StabilityChartsPage(
            IPage page,
            WebScraperOptions options)
        {
            _page = page;
            _options = options;

            _startDateInput = _page.Locator(Div)
                .Filter(new LocatorFilterOptions
                {
                    HasText = StartDateAndTimeText2,
                    HasNotText = EndDateAndTimeText2
                })
                .GetByRole(AriaRole.Textbox);

            _endDateInput = _page.Locator(Div)
                .Filter(new LocatorFilterOptions
                {
                    HasText = EndDateAndTimeText2,
                    HasNotText = StartDateAndTimeText2
                })
                .GetByRole(AriaRole.Textbox);

            _surfaceTypeCombobox = _page.GetByRole(AriaRole.Combobox)
                .Nth(FourthItemIndex);

            _calculationMethodCombobox = _page.GetByRole(AriaRole.Combobox)
                .Nth(FifthItemIndex);

            _generateChartButton = _page.GetByRole(AriaRole.Button,
                new PageGetByRoleOptions
                {
                    Name = GenerateChartButtonText
                });

            _undrainedChartCanvas = _page.GetByLabel(UndrainedSafetyFactors)
                .Locator(Canvas);

            _drainedChartCanvas = _page.GetByLabel(DrainedSafetyFactors)
                .Locator(Canvas);

            _pseudoStaticChartCanvas = _page
                .GetByLabel(PseudoStaticSafetyFactors)
                .Locator(Canvas);

            _emptyChartDiv = _page.GetByText(EmptySafetyFactors);


            _seeAnalysisButton = _page.GetByRole(AriaRole.Button,
                new PageGetByRoleOptions
                {
                    Name = SeeAnalysisButton
                });

            _sectionMultiSelect = _page.GetByRole(AriaRole.Dialog).Locator(Span)
                .Nth(FourthItemIndex);

            _soilConditionTypeCombobox =
                _page.GetByRole(AriaRole.Combobox).First;

            _safetyFactorDateCombobox = _page.GetByRole(AriaRole.Combobox)
                .Nth(SecondItemIndex);

            _showImageButton = _page.GetByRole(AriaRole.Button,
                new PageGetByRoleOptions
                {
                    Name = ShowImageButton
                });

            _downloadPngButton = _page.GetByRole(AriaRole.Button,
                new PageGetByRoleOptions
                {
                    NameRegex = new Regex(DownloadPngButton)
                });

            _closeDialogButton = _page.GetByLabel(CloseButton);
        }

        public async Task GotoAsync()
        {
            var url =
                $"{_options.LogisoilFrontendUri}/stability/stabilityCharts";
            await _page.GotoAsync(url);
        }

        public async Task InterceptRequestsForFiltersAsync()
        {
            var calculationMethodsTask =
                _page.WaitForResponseAsync(GetStructureCalculationMethods);
            var surfaceTypesTask =
                _page.WaitForResponseAsync(GetStructureSurfaceTypes);
            var responses =
                await Task.WhenAll(calculationMethodsTask, surfaceTypesTask);

            if (responses.Any(response => response.Status != 200))
            {
                return;
            }

            var surfaceTypesResponse = await responses[1].JsonAsync();

            if (!surfaceTypesResponse.HasValue)
            {
                return;
            }

            using var arrayEnumerator = surfaceTypesResponse.Value
                .EnumerateArray().GetEnumerator();
            var surfaceTypes = arrayEnumerator
                .Select(surfaceType =>
                    surfaceType.GetProperty(propertyName: "name").GetString())
                .ToList();

            SurfaceTypeFilters.AddRange(surfaceTypes);
        }

        public async Task ClearFormAsync()
        {
            await _startDateInput.ClearAsync();
            await _endDateInput.ClearAsync();
        }

        public async Task<bool> ShowStabilityHistoryChartsAsync(
            string surfaceType,
            DateTime startDate,
            DateTime endDate)
        {
            await _surfaceTypeCombobox.SelectOptionAsync(new[] { surfaceType });
            
            _ = await _page.RunAndWaitForResponseAsync(
                async () => await _calculationMethodCombobox.SelectOptionAsync(new[] { "Menor FS" }),
                GetStabilityAnalysisFirstDate);
            
            await _page.WaitForTimeoutAsync(100);
            await _startDateInput.ClearAsync();
            await _page.WaitForTimeoutAsync(100);

            await _startDateInput
                .PressSequentiallyAsync($"{startDate:MMdd00yyyyhhmmtt}",
                    new LocatorPressSequentiallyOptions { Delay = 200 });

            await _endDateInput
                .PressSequentiallyAsync($"{endDate:MMdd00yyyyhhmmtt}",
                    new LocatorPressSequentiallyOptions { Delay = 100 });

            var response =
                await _page.RunAndWaitForResponseAsync(
                    async () => await _generateChartButton.ClickAsync(),
                    GetStabilityAnalysisChart);

            var isEmptyData = await _emptyChartDiv.IsVisibleAsync() ||
                              response.Status ==
                              HttpStatusCode.NoContent.GetHashCode();

            return !isEmptyData;
        }

        public async Task<bool> GetStabilityHistoryChartAsync(
            SoilConditionType soilCondition,
            string path)
        {
            var canvas = soilCondition switch
            {
                SoilConditionType.Undrained => _undrainedChartCanvas,
                SoilConditionType.Drained => _drainedChartCanvas,
                SoilConditionType.PseudoStatic => _pseudoStaticChartCanvas,
                _ => throw new NotSupportedException(
                    "The selected soil condition type is not supported.")
            };

            var byteArray = await canvas
                .ScreenshotAsync(new LocatorScreenshotOptions
                {
                    Path = path
                });

            return byteArray is { Length: > 0 };
        }

        public async Task<bool> GetLastSafetyFactorImageAsync(
            string sectionName,
            SoilConditionType soilCondition,
            string path)
        {
            await _seeAnalysisButton.ClickAsync();
            await _page.WaitForTimeoutAsync(100);

            await _sectionMultiSelect.ClickAsync();

            var sectionIsVisible = await _page.GetByRole(AriaRole.Dialog)
                .GetByText(sectionName)
                .IsVisibleAsync();

            if (!sectionIsVisible)
            {
                await _closeDialogButton.ClickAsync();
                return false;
            }

            await _page.GetByRole(AriaRole.Dialog)
                .GetByText(sectionName)
                .ClickAsync();

            var selectedValue = soilCondition.ToStabilityPageSelectValue();
            await _soilConditionTypeCombobox.SelectOptionAsync(selectedValue);

            var dateComboboxIsDisabled =
                await _safetyFactorDateCombobox.IsDisabledAsync();

            if (dateComboboxIsDisabled)
            {
                await _closeDialogButton.ClickAsync();
                return false;
            }

            await _safetyFactorDateCombobox.SelectOptionAsync(
                new SelectOptionValue
                {
                    Index = 1
                });

            var response = await _page.RunAndWaitForResponseAsync(
                async () => await _showImageButton.ClickAsync(),
                GetSafetyFactorImageById);

            if (response.Status != 200)
            {
                await _closeDialogButton.ClickAsync();
                return false;
            }

            await _downloadPngButton.ClickAsync();
            var download = await _page.WaitForDownloadAsync();
            await download.SaveAsAsync(path);

            await _closeDialogButton.ClickAsync();

            return true;
        }
    }
}