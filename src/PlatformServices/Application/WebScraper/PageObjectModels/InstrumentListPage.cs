using Application.WebScraper.Extensions;
using Domain.Enums;
using Microsoft.Playwright;
using System.Text.RegularExpressions;
using static Application.WebScraper.Constants.RequestUrls;
using static Application.WebScraper.Constants.Selectors.HtmlTags;
using static Application.WebScraper.Constants.Selectors.Instrument;
using static Application.WebScraper.Constants.Selectors.Shared;

namespace Application.WebScraper.PageObjectModels
{
    public sealed class InstrumentListPage
    {
        private readonly IPage _page;
        private readonly WebScraperOptions _options;
        private readonly Clip _screenshotClip;
        private readonly ILocator _clientsMultiSelect;
        private readonly ILocator _clientUnitsMultiSelect;
        private readonly ILocator _structureMultiSelect;
        private readonly ILocator _instrumentSubtypeSelect;
        private readonly ILocator _searchButton;
        private readonly ILocator _emptyDataDiv;
        private readonly ILocator _firstTableRowMenuButton;
        private readonly ILocator _seeLocationMenuItem;
        private readonly ILocator _instrumentSubtypeMapMultiSelect;
        private readonly ILocator _seeSectionsInMapButton;
        private readonly ILocator _closeInfoWindowInMapButton;
        private readonly ILocator _mapFullscreenButton;

        public InstrumentListPage(
            IPage page,
            WebScraperOptions options)
        {
            _page = page;
            _options = options;
            _screenshotClip = _options.ViewportSize.CreateSquareClip();

            _clientsMultiSelect = _page.Locator(NgMultiSelectDropdown)
                .Filter(new LocatorFilterOptions
                {
                    HasText = SelectDropdownText
                })
                .Locator(Span)
                .Nth(FourthItemIndex);

            _clientUnitsMultiSelect = _page.Locator(NgMultiSelectDropdown)
                .Filter(new LocatorFilterOptions
                {
                    HasText = SelectDropdownText
                })
                .Locator(Span)
                .Nth(FourthItemIndex);

            _structureMultiSelect = _page.Locator(NgMultiSelectDropdown)
                .Filter(new LocatorFilterOptions
                {
                    HasText = SelectDropdownText
                })
                .Locator(Span)
                .Nth(FourthItemIndex);

            _instrumentSubtypeSelect = _page.Locator(FourthFormSelectChild);

            _searchButton = _page.GetByRole(AriaRole.Button,
                new PageGetByRoleOptions
                {
                    NameRegex = new Regex(SearchRegex)
                });

            _emptyDataDiv = _page.GetByText(DataNotFound);

            _firstTableRowMenuButton = _page
                .Locator(FirstRowItemClass)
                .Locator(NullId);

            _seeLocationMenuItem = _page.Locator(EighthListItemChild);

            _instrumentSubtypeMapMultiSelect = _page.Locator(NgMultiSelectDropdown)
                .Filter(new LocatorFilterOptions
                {
                    HasText = SelectTheSubtypesText
                })
                .Locator(Span)
                .Nth(FourthItemIndex);

            _seeSectionsInMapButton = _page.GetByRole(AriaRole.Button,
                new PageGetByRoleOptions
                {
                    Name = SeeSectionsButton
                });

            _closeInfoWindowInMapButton = _page.GetByLabel(CloseButton);

            _mapFullscreenButton = _page.GetByLabel(MapFullscreenButtonLabel);
        }

        public async Task GotoAsync()
        {
            var url = $"{_options.LogisoilFrontendUri}/instruments?showPin=false&showSectionInfoWindow=true";

            await _page.GotoAsync(url);
        }

        public async Task<bool> CaptureInstrumentsMapAsync(
            InstrumentSubtype instrumentSubtype,
            IEnumerable<string> instrumentNames,
            string path)
        {
            var subtypeAsOption = instrumentSubtype.ToInstrumentPageSelectValue();
            await _instrumentSubtypeSelect.SelectOptionAsync(subtypeAsOption);

            var response = await _page.RunAndWaitForResponseAsync(
                async () => await _searchButton.ClickAsync(),
                GetInstruments);

            if (response.Status != 200)
            {
                return false;
            }

            var isEmptyData = await _emptyDataDiv.IsVisibleAsync();
            if (isEmptyData)
            {
                return false;
            }

            await _firstTableRowMenuButton.ClickAsync();

            response = await _page.RunAndWaitForResponseAsync(
                async () => await _seeLocationMenuItem.ClickAsync(),
                GetInstrumentsMap);

            if (response.Status != 200)
            {
                return false;
            }

            var subtypeAsText = instrumentSubtype.ToInstrumentPageMultiSelectValue();
            await _instrumentSubtypeMapMultiSelect.ClickAsync();

            await _page
                .Locator(Item2Class)
                .GetByRole(AriaRole.Listitem)
                .Filter(new LocatorFilterOptions
                {
                    HasText = subtypeAsText
                })
                .ClickAsync();

            await _page.WaitForTimeoutAsync(100);

            response = await _page.RunAndWaitForResponseAsync(
                async () => await _seeSectionsInMapButton.ClickAsync(),
                GetSectionsMap);

            if (response.Status != 200)
            {
                return false;
            }

            foreach (var instrument in instrumentNames)
            {
                var instrumentIsVisible = await _page.GetByRole(AriaRole.Dialog,
                    new PageGetByRoleOptions
                    {
                        Name = instrument
                    }).IsVisibleAsync();

                if (instrumentIsVisible)
                {
                    await _page.GetByRole(AriaRole.Dialog,
                            new PageGetByRoleOptions
                            {
                                Name = instrument
                            })
                        .GetByRole(AriaRole.Button,
                            new LocatorGetByRoleOptions
                            {
                                Name = CloseButton
                            })
                        .ClickAsync();
                }
            }

            await _page.WaitForTimeoutAsync(100);

            await _mapFullscreenButton.ClickAsync();
            await _page.WaitForTimeoutAsync(500);

            var byteArray = await _page.ScreenshotAsync(new PageScreenshotOptions
            {
                Path = path,
                Clip = _screenshotClip
            });

            await _mapFullscreenButton.ClickAsync();

            await _page.Locator(NgMultiSelectDropdown)
                .Filter(new LocatorFilterOptions
                {
                    HasText = subtypeAsText
                })
                .Locator(Span)
                .Nth(FourthItemIndex)
                .ClickAsync();

            await _page
                .Locator(Item2Class)
                .GetByRole(AriaRole.Listitem)
                .Filter(new LocatorFilterOptions
                {
                    HasText = subtypeAsText
                })
                .ClickAsync();

            return byteArray != null;
        }
    }
}