using Microsoft.Playwright;

namespace Application.WebScraper.PageObjectModels
{
    public sealed class InspectionPage
    {
        private readonly IPage _page;
        private readonly WebScraperOptions _options;

        public InspectionPage(
            IPage page,
            WebScraperOptions options)
        {
            _page = page;
            _options = options;
        }

        public async Task GotoAsync(
            Guid structureId)
        {
            var url = $"{_options.LogisoilFrontendUri}/inspection";
            await _page.GotoAsync(url);
        }
    }
}
