using Application.WebScraper.Extensions;
using Microsoft.Playwright;
using static Application.WebScraper.Constants.Selectors.Shared;
using static Application.WebScraper.Constants.Selectors.Structure;

namespace Application.WebScraper.PageObjectModels
{
    public sealed class StructurePage
    {
        private readonly IPage _page;
        private readonly WebScraperOptions _options;
        private readonly Clip _screenshotClip;
        private readonly ILocator _structureMapFullscreenButton;
        private readonly ILocator _instrumentsMapFullscreenButton;

        public StructurePage(
            IPage page,
            WebScraperOptions options)
        {
            _page = page;
            _options = options;
            _screenshotClip = _options.ViewportSize.CreateSquareClip();

            _structureMapFullscreenButton = page
                .Locator(StructureMapFullscreenButton)
                .GetByLabel(MapFullscreenButtonLabel);

            _instrumentsMapFullscreenButton = page
                .Locator(InstrumentsMapFullscreenButton)
                .GetByLabel(MapFullscreenButtonLabel);
        }

        public async Task GotoAsync(
            Guid structureId)
        {
            var url = $"{_options.LogisoilFrontendUri}/structures/{structureId}/view";
            await _page.GotoAsync(url);
        }

        public async Task<bool> CaptureStructureMapAsync(
            string path)
        {
            await _structureMapFullscreenButton.ClickAsync();
            await _page.WaitForTimeoutAsync(1000);

            var byteArray = await _page.ScreenshotAsync(new PageScreenshotOptions
            {
                Path = path,
                Clip = _screenshotClip
            });

            await _structureMapFullscreenButton.ClickAsync();

            return byteArray != null;
        }
    }
}
