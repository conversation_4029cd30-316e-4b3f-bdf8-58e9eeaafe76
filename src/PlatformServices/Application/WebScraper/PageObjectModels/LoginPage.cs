using Microsoft.Playwright;
using static Application.WebScraper.Constants.Selectors.Login;

namespace Application.WebScraper.PageObjectModels
{
    public sealed class LoginPage
    {
        private readonly IPage _page;
        private readonly WebScraperOptions _options;
        private readonly ILocator _usernameInput;
        private readonly ILocator _passwordInput;
        private readonly ILocator _signInButton;

        public LoginPage(
            IPage page,
            WebScraperOptions options)
        {
            _page = page;
            _options = options;
            _usernameInput = _page.GetByPlaceholder(UsernameInput);
            _passwordInput = _page.GetByPlaceholder(PasswordInput);
            _signInButton = _page.GetByRole(AriaRole.Button, new PageGetByRoleOptions { Name = SignInButton });
        }

        public async Task GotoAsync()
        {
            await _page.GotoAsync(_options.LogisoilFrontendUri);
            await _page.WaitForURLAsync($"{_options.LogisoilAuthUri}?**");
        }

        public async Task AuthenticateAsync()
        {
            await _usernameInput.FillAsync(_options.Username);
            await _passwordInput.FillAsync(_options.Password);
            await _signInButton.ClickAsync();
            await _page.WaitForTimeoutAsync(1000);
        }
    }
}
