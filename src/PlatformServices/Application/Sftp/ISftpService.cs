using Application.Sftp.Model.Request;
using Application.Sftp.Model.Response;

namespace Application.Sftp
{
    public interface ISftpService
    {
        Task<ResultResponse<DownloadFilesResponse>> BulkDownloadFilesAsync(DownloadFilesRequest request);
        Task<ResultResponse<DeleteFilesResponse>> DeleteFilesAsync(DeleteFilesRequest request);
        Task<ResultResponse<GetAllFilesResponse>> GetAllFilesAsync(GetAllFilesRequest request);
        Task<ResultResponse<MoveFilesResponse>> MoveFilesAsync(MoveFilesRequest request);
    }
}