namespace Application.Sftp.Model.Response
{
    public class ResultResponse<T>
    {
        public bool IsSuccessful { get; set; }

        public Exception Exception { get; set; }

        public string Message { get; set; }

        public T Data { get; set; }

        public static ResultResponse<T> Error()
        {
            return new ResultResponse<T>
            {
                IsSuccessful = false
            };
        }

        public static ResultResponse<T> Error(string message)
        {
            return new ResultResponse<T>
            {
                IsSuccessful = false,
                Message = message
            };
        }

        public static ResultResponse<T> Error(string message, Exception exception)
        {
            return new ResultResponse<T>
            {
                IsSuccessful = false,
                Message = message,
                Exception = exception
            };
        }

        public static ResultResponse<T> Error(Exception exception)
        {
            return new ResultResponse<T>
            {
                IsSuccessful = false,
                Exception = exception
            };
        }

        public static ResultResponse<T> Error(T data)
        {
            return new ResultResponse<T>
            {
                IsSuccessful = false,
                Data = data
            };
        }

        public static ResultResponse<T> Success()
        {
            return new ResultResponse<T>
            {
                IsSuccessful = true
            };
        }

        public static ResultResponse<T> Success(T data)
        {
            return new ResultResponse<T>
            {
                IsSuccessful = true,
                Data = data
            };
        }

        public static ResultResponse<T> Success(T data, string message)
        {
            return new ResultResponse<T>
            {
                IsSuccessful = true,
                Data = data,
                Message = message
            };
        }
    }
}
