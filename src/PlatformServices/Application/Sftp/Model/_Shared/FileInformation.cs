namespace Application.Sftp.Model._Shared
{
    public sealed record FileInformation
    {
        public string Name { get; set; }
        public string FullPath { get; set; }
        public string Extension { get; set; }

        public FileInformation(
            string name,
            string fullPath)
        {
            Name = name;
            FullPath = fullPath;
            Extension = Path.GetExtension(fullPath);
        }
    }
}
