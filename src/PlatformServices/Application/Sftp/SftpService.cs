using Application.Sftp.Model._Shared;
using Application.Sftp.Model.Request;
using Application.Sftp.Model.Response;
using Renci.SshNet;

namespace Application.Sftp
{
    public class SftpService : ISftpService
    {
        private readonly SftpClient _sftpClient;

        public SftpService(SftpClient sftpClient)
        {
            _sftpClient = sftpClient;
        }

        public async Task<ResultResponse<DownloadFilesResponse>> BulkDownloadFilesAsync(
            DownloadFilesRequest request)
        {
            var response = await HandleAsync(
                async (req) =>
                {
                    var downloadedFiles = new DownloadFilesResponse();

                    var tasks = request
                    .Files
                    .Select(fileRequest =>
                    {
                        var output = new MemoryStream();

                        return Task.Factory.FromAsync(
                            beginMethod: (callback, obj) =>
                                _sftpClient.BeginDownloadFile(
                                    path: fileRequest.FullPath,
                                    output: output,
                                    asyncCallback: callback,
                                    state: obj),
                            endMethod: result =>
                            {
                                _sftpClient.EndDownloadFile(result);

                                if (result.IsCompleted)
                                {
                                    downloadedFiles.Files.Add(
                                        new DownloadFileResponse(
                                            File: fileRequest,
                                            FileContentBytes: output.ToArray()));
                                }

                                output.Dispose();
                            },
                            null);
                    });

                    await Task
                        .WhenAll(tasks)
                        .ConfigureAwait(false);

                    return downloadedFiles;
                },
                request,
                nameof(BulkDownloadFilesAsync))
                .ConfigureAwait(false);

            return response;
        }

        public async Task<ResultResponse<DeleteFilesResponse>> DeleteFilesAsync(
            DeleteFilesRequest request)
        {
            var response = await HandleAsync(
                operation: async (req) =>
                {
                    var deleteFilesResponse = new DeleteFilesResponse();

                    var sftpFiles = await Task.Factory.FromAsync(
                        asyncResult: _sftpClient.BeginListDirectory(
                            request.SourcePath,
                            null,
                            null),
                        endMethod: _sftpClient.EndListDirectory)
                        .ConfigureAwait(false);

                    var filesToBeDeleted = sftpFiles
                        .Where(file =>
                            !file.IsDirectory
                            && !file.IsSymbolicLink
                            && file.LastWriteTimeUtc <= request.SftpFilesPurgeDate);

                    if (filesToBeDeleted.Any())
                    {
                        var tasks = filesToBeDeleted
                            .Select(file =>
                            {
                                deleteFilesResponse.Filenames.Add(file.Name);
                                return Task.Run(() =>
                                {
                                    _sftpClient.DeleteFile(file.FullName);
                                });
                            });

                        await Task
                            .WhenAll(tasks)
                            .ConfigureAwait(false);
                    }

                    return deleteFilesResponse;
                },
                request: request,
                nameOfOperation: nameof(DeleteFilesAsync))
                .ConfigureAwait(false);

            return response;
        }

        public async Task<ResultResponse<GetAllFilesResponse>> GetAllFilesAsync(
            GetAllFilesRequest request)
        {
            var response = await HandleAsync(
                operation: async (req) =>
                {
                    var fileInformations = new List<FileInformation>();

                    foreach (var sourcePath in request.SourcePaths)
                    {
                        var sftpFiles = await Task.Factory.FromAsync(
                        asyncResult: _sftpClient.BeginListDirectory(
                            sourcePath,
                            null,
                            null),
                        endMethod: _sftpClient.EndListDirectory)
                        .ConfigureAwait(false);

                        var fileResults = sftpFiles
                            .Where(f =>
                                !f.IsDirectory
                                && !f.IsSymbolicLink
                                && f.LastWriteTimeUtc <= request.FilesLastFetchDate);

                        if (!fileResults.Any())
                        {
                            continue;
                        }

                        fileInformations.AddRange(
                            fileResults
                                .Select(file =>
                                    new FileInformation(
                                        name: file.Name,
                                        fullPath: file.FullName)));
                    }

                    if (!fileInformations.Any())
                    {
                        return null;
                    }

                    return new GetAllFilesResponse(fileInformations);
                },
                request: request,
                nameOfOperation: nameof(GetAllFilesAsync))
                .ConfigureAwait(false);

            return response;
        }

        public async Task<ResultResponse<MoveFilesResponse>> MoveFilesAsync(
            MoveFilesRequest request)
        {
            var response = await HandleAsync(
                operation: async (req) =>
                {
                    var existsDirectory = _sftpClient
                        .Exists(path: request.DestinationPath);
                    if (!existsDirectory)
                    {
                        _sftpClient.CreateDirectory(
                            path: request.DestinationPath);
                    }

                    var filenames = new List<string>();

                    foreach (var sourcePath in request.SourcePaths)
                    {
                        var sftpFiles = await Task.Factory.FromAsync(
                        asyncResult: _sftpClient.BeginListDirectory(
                            sourcePath,
                            null,
                            null),
                        endMethod: _sftpClient.EndListDirectory)
                        .ConfigureAwait(false);

                        var filesToBeMoved = sftpFiles
                            .Where(file =>
                                !file.IsDirectory
                                && !file.IsSymbolicLink
                                && file.LastWriteTimeUtc <= request.FilesLastFetchDate
                                && request.Files.Any(f => f.Name == file.Name));

                        if (!filesToBeMoved.Any())
                        {
                            continue;
                        }

                        var tasks = filesToBeMoved
                            .Select(file =>
                            {
                                filenames.Add(file.Name);
                                return Task.Run(() =>
                                {
                                    file.MoveTo(request.GetFullDestinationPath(file.Name));
                                });
                            });

                        await Task
                            .WhenAll(tasks)
                            .ConfigureAwait(false);
                    }

                    if (!filenames.Any())
                    {
                        return null;
                    }

                    return new MoveFilesResponse(
                        filenames);
                },
                request: request,
                nameOfOperation: nameof(MoveFilesAsync))
                .ConfigureAwait(false);

            return response;
        }

        private async Task<ResultResponse<TResponse>> HandleAsync<TRequest, TResponse>(
            Func<TRequest, Task<TResponse>> operation,
            TRequest request,
            string nameOfOperation)
            where TRequest : class
            where TResponse : class
        {
            if (request == null)
            {
                return ResultResponse<TResponse>
                    .Error("Request is null");
            }

            if (operation == null)
            {
                return ResultResponse<TResponse>
                    .Error("Please specify the response to handle");
            }

            if (string.IsNullOrWhiteSpace(nameOfOperation))
            {
                return ResultResponse<TResponse>
                    .Error("Please specify a name for the operation");
            }

            try
            {
                _sftpClient.Connect();

                var response = await operation(request)
                    .ConfigureAwait(false);
                if (response == null)
                {
                    return ResultResponse<TResponse>
                        .Error($"No data found for: {nameOfOperation}");
                }

                return ResultResponse<TResponse>
                    .Success(response);
            }
            catch (Exception exception)
            {
                return ResultResponse<TResponse>
                    .Error(
                        $"Error occured in {nameOfOperation}",
                        exception);
            }
            finally
            {
                _sftpClient.Disconnect();
            }
        }
    }
}
