using Application.WebScraper;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Playwright;

namespace Application
{
    public static class WebScraperCfg
    {
        public static IServiceCollection AddWebScraper(
            this IServiceCollection services)
        {
            services
                .AddTransient<IBrowser>(services =>
                {
                    var playwright = Playwright
                        .CreateAsync()
                        .GetAwaiter()
                        .GetResult();

                    var chromium = playwright.Chromium
                        .LaunchAsync(new BrowserTypeLaunchOptions
                        {
                            Headless = false
                        })
                        .GetAwaiter()
                        .GetResult();

                    return chromium;
                })
                .AddTransient<IEoRReportScraper, EoRReportScraper>();

            return services;
        }
    }
}