using Application.Apis.Clients.Response.Report;
using Application.Extensions;
using Application.Report.Extensions;
using Application.WebScraper.Constants;
using Domain.Entities.Report;
using Domain.Enums;
using Domain.Extensions;
using NPOI.XWPF.UserModel;
using System.Text.Json;
using static Application.Docx.Constants.TableIndexes;
using static Application.WebScraper.Constants.TextPlaceholders.Eor;
using Axis = Domain.Enums.Axis;

namespace Application.Docx.Extensions
{
    public static class MosaicXWPFDocumentExtensions
    {
        public static void AddMosaicResponsiblesTable(
            this XWPFDocument document,
            IEnumerable<Responsible> data)
        {
            if (!data.Any())
            {
                return;
            }

            var table = document.Tables.FirstOrDefault(t => t.TableCaption == TextPlaceholders.Eor.ResponsiblesTable);
            var positionInText = document.GetPosOfTable(table);
            table = document.CreateTable(rows: 3 * data.Count(), cols: 5, positionInText + 1);
            document.RemoveBodyElement(positionInText);

            var rowIndex = 0;
            var itemIndex = 1;

            foreach (var item in data)
            {
                table.MergeCellsVertically(Zero, rowIndex, rowIndex + 2);

                var whiteColor = "#FFFFFF";
                var darkBlueColor = "#003399";

                table.Rows[rowIndex].SetFirstColumn($"{itemIndex}");
                table.Rows[rowIndex].SetCell(First, "Nome", textColor: whiteColor, backgroundColor: darkBlueColor);
                table.Rows[rowIndex].SetCell(Second, item.Name);
                table.Rows[rowIndex].SetCell(Third, "Reg. Profissional", textColor: whiteColor, backgroundColor: darkBlueColor);
                table.Rows[rowIndex].SetCell(Fourth, item.ProfessionalRecord);
                ++rowIndex;

                table.Rows[rowIndex].SetCell(First, "Função", textColor: whiteColor, backgroundColor: darkBlueColor);
                table.Rows[rowIndex].SetCell(Second, item.RoleName);
                table.Rows[rowIndex].SetCell(Third, "Cargo", textColor: whiteColor, backgroundColor: darkBlueColor);
                table.Rows[rowIndex].SetCell(Fourth, item.PositionName);
                ++rowIndex;

                table.Rows[rowIndex].SetCell(First, "Email", textColor: whiteColor, backgroundColor: darkBlueColor);
                table.Rows[rowIndex].SetCell(Second, item.EmailAddress);
                table.Rows[rowIndex].SetCell(Third, "Empresa", textColor: whiteColor, backgroundColor: darkBlueColor);
                table.Rows[rowIndex].SetCell(Fourth, string.Empty);
                ++rowIndex;
                itemIndex++;
            }

            table.SetColumnWidth(Zero, 20);
            table.SetColumnWidth(First, 100);
            table.SetColumnWidth(Second, 350);
            table.SetColumnWidth(Third, 180);
            table.SetColumnWidth(Fourth, 350);
            table.SetCellMargins(150, 150, 150, 150);
        }

        public static void AddMosaicPercolationReadingsTable(
           this XWPFDocument document,
           IEnumerable<Apis.Clients.Response.Report.Section> data,
           IDictionary<SecurityLevelType, string> images)
        {
            if (!data.Any())
            {
                return;
            }

            var table = document.Tables.FirstOrDefault(table => table.TableCaption == TextPlaceholders.Eor.PercolationReadingsTable);

            var rowIndex = 0;

            foreach (var section in data)
            {
                var instruments = section.Instruments.Where(instrument => instrument.Subtype == Domain.Enums.InstrumentSubtype.Percolation);

                foreach (var instrument in instruments)
                {
                    var statisticData = JsonSerializer.Deserialize<PercolationStatisticData>(instrument.StatisticData) as PercolationStatisticData;

                    var maxReadingPicture = new SimplePicture
                    {
                        Path = images[statisticData.MaxReadingValue?.SecurityLevelType ?? SecurityLevelType.None],
                        Height = 12,
                        Width = 12
                    };

                    var lastReadingPicture = new SimplePicture
                    {
                        Path = images[statisticData.LastReadingValue?.SecurityLevelType ?? SecurityLevelType.None],
                        Height = 12,
                        Width = 12
                    };

                    var newRow = table.CreateRow();
                    newRow.AddNewTableCell();
                    newRow.AddNewTableCell();
                    newRow.AddNewTableCell();
                    newRow.AddNewTableCell();
                    newRow.AddNewTableCell();
                    newRow.AddNewTableCell();
                    newRow.AddNewTableCell();
                    newRow.AddNewTableCell();
                    newRow.AddNewTableCell();
                    newRow.AddNewTableCell();
                    newRow.AddNewTableCell();
                    newRow.AddNewTableCell();
                    newRow.AddNewTableCell();
                    newRow.AddNewTableCell();

                    newRow.SetCell(Zero, section.Name, fontSize: 8);
                    newRow.SetCell(First, instrument.Name, fontSize: 8);
                    newRow.SetCell(Second, instrument.UtmCoordinatesNorthing.RoundNumber(2).ToString(), fontSize: 8);
                    newRow.SetCell(Third, instrument.UtmCoordinatesEasting.RoundNumber(2).ToString(), fontSize: 8);
                    newRow.SetCell(Fourth, instrument.TopQuota?.RoundNumber(2).ToString() ?? "-", fontSize: 8);
                    newRow.SetCell(Fifth, instrument.BaseQuota?.RoundNumber(2).ToString() ?? "-", fontSize: 8);
                    newRow.SetCell(Sixth, instrument.SecurityLevels?.FirstOrDefault()?.Attention?.RoundNumber(2).ToString() ?? "-", fontSize: 8);
                    newRow.SetCell(Seventh, instrument.SecurityLevels?.FirstOrDefault()?.Alert?.RoundNumber(2).ToString() ?? "-", fontSize: 8);
                    newRow.SetCell(Eighth, instrument.SecurityLevels?.FirstOrDefault()?.Emergency?.RoundNumber(2).ToString() ?? "-", fontSize: 8);
                    newRow.SetCell(Ninth, statisticData?.MaxReadingValue?.Quota?.RoundNumber(2).ToString() ?? "-", fontSize: 8, picture: maxReadingPicture);
                    newRow.SetCell(Tenth, statisticData?.MinReadingValue?.Quota?.RoundNumber(2).ToString() ?? "-", fontSize: 8);
                    newRow.SetCell(Eleventh, statisticData?.Average.RoundNumber(2).ToString() ?? "-", fontSize: 8);
                    newRow.SetCell(Twelfth, statisticData?.Median.RoundNumber(2).ToString() ?? "-", fontSize: 8);
                    newRow.SetCell(Thirteenth, statisticData?.AbsoluteVariation.RoundNumber(2).ToString() ?? "-", fontSize: 8);
                    newRow.SetCell(Fourteenth, statisticData?.StandardDeviation.RoundNumber(2).ToString() ?? "-", fontSize: 8);
                    newRow.SetCell(Fifteenth, statisticData?.LastReadingValue?.Quota?.RoundNumber(2).ToString() ?? "-", fontSize: 8, picture: lastReadingPicture);
                    newRow.SetCell(Sixteenth, statisticData?.LastReadingValue?.Date.ToString("dd/MM/yyyy") ?? "-", fontSize: 8);

                    rowIndex++;
                }
            }

            table.SetCellMargins(5, 5, 5, 5);
        }

        public static void AddMosaicSurfaceLandmarkInfoTable(
            this XWPFDocument document,
            IEnumerable<Instrument> data)
        {
            if (!data.Any())
            {
                return;
            }

            data = data.Where(instrument => instrument.Type == InstrumentType.SurfaceLandmark);

            var table = document.Tables.FirstOrDefault(t => t.TableCaption == SurfaceLandmarkInfoTable);

            var rowIndex = 1;

            var allowedAxis = new[] { Axis.A, Axis.B, Axis.Z };

            foreach (var instrument in data)
            {
                var securityLevels = instrument.SecurityLevels
                    .Where(level => level.Axis.HasValue && allowedAxis.Contains(level.Axis.Value))
                    .OrderBy(level => level.Axis);

                foreach (var securityLevel in securityLevels)
                {
                    var newRow = table.CreateRow();

                    newRow.AddNewTableCell();
                    newRow.AddNewTableCell();
                    newRow.AddNewTableCell();

                    newRow.SetCell(Zero, instrument.Name, fontSize: 10);
                    newRow.SetCell(First, instrument.UtmCoordinatesEasting.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.SetCell(Second, instrument.UtmCoordinatesNorthing.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.SetCell(Third, instrument.TopQuota?.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.SetCell(Fourth, securityLevel.Axis.ToString(), fontSize: 10);
                    newRow.SetCell(Fifth, securityLevel?.Attention?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                    newRow.SetCell(Sixth, securityLevel?.Alert?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                    newRow.SetCell(Seventh, securityLevel?.Emergency?.RoundNumber(2).ToString() ?? "-", fontSize: 10);

                    rowIndex++;
                }

                if (securityLevels.Count() < 2)
                {
                    continue;
                }

                table.MergeCellsVertically(
                    Zero,
                    rowIndex - (securityLevels.Count() - 1),
                    rowIndex);

                table.MergeCellsVertically(
                    First,
                    rowIndex - (securityLevels.Count() - 1),
                    rowIndex);

                table.MergeCellsVertically(
                    Second,
                    rowIndex - (securityLevels.Count() - 1),
                    rowIndex);

                table.MergeCellsVertically(
                    Third,
                    rowIndex - (securityLevels.Count() - 1),
                    rowIndex);
            }

            table.SetCellMargins(0, 0, 0, 0);
        }

        public static void AddMosaicSettlementGaugeInfoTable(
            this XWPFDocument document,
            IEnumerable<Instrument> data)
        {
            if (!data.Any())
            {
                return;
            }

            data = data.Where(instrument => instrument.Type == InstrumentType.SettlementGauge);

            var table = document.Tables.FirstOrDefault(t => t.TableCaption == SettlementGaugeInfoTable);

            var rowIndex = 0;

            foreach (var instrument in data)
            {
                foreach (var measurement in instrument.Measurements)
                {
                    var newRow = table.CreateRow();

                    newRow.SetCell(Zero, instrument.Name, fontSize: 10);
                    newRow.SetCell(First, instrument.UtmCoordinatesEasting.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.SetCell(Second, instrument.UtmCoordinatesNorthing.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.SetCell(Third, instrument.Depth?.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.SetCell(Fourth, measurement.Identifier, fontSize: 10);
                    newRow.SetCell(Fifth, measurement.Quota?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                    newRow.SetCell(Sixth, measurement.Lithotype?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                    newRow.SetCell(Seventh, measurement.Limit?.RoundNumber(2).ToString() ?? "-", fontSize: 10);

                    rowIndex++;
                }

                if (instrument.Measurements.Count() < 2)
                {
                    continue;
                }

                table.MergeCellsVertically(
                    Zero,
                    rowIndex - (instrument.Measurements.Count() - 1),
                    rowIndex);

                table.MergeCellsVertically(
                    First,
                    rowIndex - (instrument.Measurements.Count() - 1),
                    rowIndex);

                table.MergeCellsVertically(
                    Second,
                    rowIndex - (instrument.Measurements.Count() - 1),
                    rowIndex);

                table.MergeCellsVertically(
                    Third,
                    rowIndex - (instrument.SecurityLevels.Count() - 1),
                    rowIndex);
            }

            table.SetCellMargins(0, 0, 0, 0);
        }

        public static void AddMosaicInclinometerInfoTable(
            this XWPFDocument document,
            IEnumerable<Instrument> data)
        {
            if (!data.Any())
            {
                return;
            }

            data = data.Where(instrument => instrument.Type == InstrumentType.IPIInclinometer || instrument.Type == InstrumentType.IPIInclinometer);

            var table = document.Tables.FirstOrDefault(t => t.TableCaption == InclinometerInfoTable);

            var rowIndex = 0;

            foreach (var instrument in data)
            {
                foreach (var measurement in instrument.Measurements)
                {
                    var newRow = table.CreateRow();

                    newRow.SetCell(Zero, instrument.Name, fontSize: 10);
                    newRow.SetCell(First, instrument.UtmCoordinatesEasting.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.SetCell(Second, instrument.UtmCoordinatesNorthing.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.SetCell(Third, instrument.Depth?.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.AddNewTableCell();
                    newRow.SetCell(Fourth, measurement.Identifier, fontSize: 10);

                    rowIndex++;
                }

                if (instrument.Measurements.Count() < 2)
                {
                    continue;
                }

                table.MergeCellsVertically(
                    Zero,
                    rowIndex - (instrument.Measurements.Count() - 1),
                    rowIndex);

                table.MergeCellsVertically(
                    First,
                    rowIndex - (instrument.Measurements.Count() - 1),
                    rowIndex);

                table.MergeCellsVertically(
                    Second,
                    rowIndex - (instrument.Measurements.Count() - 1),
                    rowIndex);
            }

            table.SetCellMargins(0, 0, 0, 0);
        }

        public static void AddMosaicSurfaceLandmarkReadingsTable(
            this XWPFDocument document,
            IEnumerable<Instrument> data,
            IDictionary<SecurityLevelType, string> images)
        {
            if (!data.Any())
            {
                return;
            }

            data = data.Where(instrument => instrument.Type == InstrumentType.SurfaceLandmark);

            var table = document.Tables.FirstOrDefault(t => t.TableCaption == SurfaceLandmarkReadingsTable);

            var rowIndex = 1;

            foreach (var instrument in data)
            {
                var statisticData = JsonSerializer.Deserialize<SurfaceLandmarkStatisticCollection>(instrument.StatisticData) as SurfaceLandmarkStatisticCollection;

                if (statisticData == null)
                {
                    continue;
                }

                var verticalMaxNegativeReadingPicture = new SimplePicture
                {
                    Path = images[statisticData.Vertical?.MaxNegativeReadingValue?.SecurityLevelType ?? SecurityLevelType.None],
                    Width = 12,
                    Height = 12
                };

                var verticalMaxPositiveReadingPicture = new SimplePicture
                {
                    Path = images[statisticData.Vertical?.MaxPositiveReadingValue?.SecurityLevelType ?? SecurityLevelType.None],
                    Width = 12,
                    Height = 12
                };

                var verticalAxisRow = table.CreateRow();

                verticalAxisRow.AddNewTableCell();
                verticalAxisRow.AddNewTableCell();
                verticalAxisRow.AddNewTableCell();
                verticalAxisRow.AddNewTableCell();
                verticalAxisRow.AddNewTableCell();
                verticalAxisRow.AddNewTableCell();
                verticalAxisRow.AddNewTableCell();
                verticalAxisRow.AddNewTableCell();

                verticalAxisRow.SetCell(Zero, instrument.Name, fontSize: 10);
                verticalAxisRow.SetCell(First, "Vertical (mm)", fontSize: 10);
                verticalAxisRow.SetCell(Second, statisticData?.Vertical?.NegativeAverage?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                verticalAxisRow.SetCell(Third, statisticData?.Vertical?.PositiveAverage?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                verticalAxisRow.SetCell(Fourth, statisticData?.Vertical?.AbsoluteAverage?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                verticalAxisRow.SetCell(Fifth, statisticData?.Vertical?.NegativeMedian?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                verticalAxisRow.SetCell(Sixth, statisticData?.Vertical?.PositiveMedian?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                verticalAxisRow.SetCell(Seventh, statisticData?.Vertical?.AbsoluteMedian?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                verticalAxisRow.SetCell(Eighth, statisticData?.Vertical?.AbsoluteMedian?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                verticalAxisRow.SetCell(Ninth, statisticData?.Vertical?.NegativeStandardDeviation?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                verticalAxisRow.SetCell(Tenth, statisticData?.Vertical?.PositiveStandardDeviation?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                verticalAxisRow.SetCell(Eleventh, statisticData?.Vertical?.AbsoluteStandardDeviation?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                verticalAxisRow.SetCell(Twelfth, statisticData?.Vertical?.Amplitude?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                verticalAxisRow.SetCell(Thirteenth, statisticData?.Vertical?.MaxNegativeReadingValue?.ZDisplacement?.RoundNumber(2).ToString() ?? "-", fontSize: 10, picture: verticalMaxNegativeReadingPicture);
                verticalAxisRow.SetCell(Fourteenth, statisticData?.Vertical?.MaxPositiveReadingValue?.ZDisplacement?.RoundNumber(2).ToString() ?? "-", fontSize: 10, picture: verticalMaxPositiveReadingPicture);

                rowIndex++;

                var aMaxNegativeReadingPicture = new SimplePicture
                {
                    Path = images[statisticData.AAxis?.MaxNegativeReadingValue?.SecurityLevelType ?? SecurityLevelType.None],
                    Width = 12,
                    Height = 12
                };

                var aMaxPositiveReadingPicture = new SimplePicture
                {
                    Path = images[statisticData.AAxis?.MaxPositiveReadingValue?.SecurityLevelType ?? SecurityLevelType.None],
                    Width = 12,
                    Height = 12
                };

                var aAxisRow = table.CreateRow();

                aAxisRow.AddNewTableCell();
                aAxisRow.AddNewTableCell();
                aAxisRow.AddNewTableCell();
                aAxisRow.AddNewTableCell();
                aAxisRow.AddNewTableCell();
                aAxisRow.AddNewTableCell();
                aAxisRow.AddNewTableCell();
                aAxisRow.AddNewTableCell();

                aAxisRow.SetCell(Zero, instrument.Name, fontSize: 10);
                aAxisRow.SetCell(First, "A", fontSize: 10);
                aAxisRow.SetCell(Second, statisticData?.Vertical?.NegativeAverage?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                aAxisRow.SetCell(Third, statisticData?.Vertical?.PositiveAverage?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                aAxisRow.SetCell(Fourth, statisticData?.Vertical?.AbsoluteAverage?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                aAxisRow.SetCell(Fifth, statisticData?.Vertical?.NegativeMedian?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                aAxisRow.SetCell(Sixth, statisticData?.Vertical?.PositiveMedian?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                aAxisRow.SetCell(Seventh, statisticData?.Vertical?.AbsoluteMedian?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                aAxisRow.SetCell(Eighth, statisticData?.Vertical?.AbsoluteMedian?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                aAxisRow.SetCell(Ninth, statisticData?.Vertical?.NegativeStandardDeviation?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                aAxisRow.SetCell(Tenth, statisticData?.Vertical?.PositiveStandardDeviation?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                aAxisRow.SetCell(Eleventh, statisticData?.Vertical?.AbsoluteStandardDeviation?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                aAxisRow.SetCell(Twelfth, statisticData?.Vertical?.Amplitude?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                aAxisRow.SetCell(Thirteenth, statisticData?.Vertical?.MaxNegativeReadingValue?.ZDisplacement?.RoundNumber(2).ToString() ?? "-", fontSize: 10, picture: aMaxNegativeReadingPicture);
                aAxisRow.SetCell(Fourteenth, statisticData?.Vertical?.MaxPositiveReadingValue?.ZDisplacement?.RoundNumber(2).ToString() ?? "-", fontSize: 10, picture: aMaxPositiveReadingPicture);

                rowIndex++;

                var bMaxNegativeReadingPicture = new SimplePicture
                {
                    Path = images[statisticData.BAxis?.MaxNegativeReadingValue?.SecurityLevelType ?? SecurityLevelType.None],
                    Width = 12,
                    Height = 12
                };

                var bMaxPositiveReadingPicture = new SimplePicture
                {
                    Path = images[statisticData.BAxis?.MaxPositiveReadingValue?.SecurityLevelType ?? SecurityLevelType.None],
                    Width = 12,
                    Height = 12
                };

                var bAxisRow = table.CreateRow();

                bAxisRow.AddNewTableCell();
                bAxisRow.AddNewTableCell();
                bAxisRow.AddNewTableCell();
                bAxisRow.AddNewTableCell();
                bAxisRow.AddNewTableCell();
                bAxisRow.AddNewTableCell();
                bAxisRow.AddNewTableCell();
                bAxisRow.AddNewTableCell();

                bAxisRow.SetCell(Zero, instrument.Name, fontSize: 10);
                bAxisRow.SetCell(First, "B", fontSize: 10);
                bAxisRow.SetCell(Second, statisticData?.Vertical?.NegativeAverage?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                bAxisRow.SetCell(Third, statisticData?.Vertical?.PositiveAverage?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                bAxisRow.SetCell(Fourth, statisticData?.Vertical?.AbsoluteAverage?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                bAxisRow.SetCell(Fifth, statisticData?.Vertical?.NegativeMedian?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                bAxisRow.SetCell(Sixth, statisticData?.Vertical?.PositiveMedian?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                bAxisRow.SetCell(Seventh, statisticData?.Vertical?.AbsoluteMedian?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                bAxisRow.SetCell(Eighth, statisticData?.Vertical?.AbsoluteMedian?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                bAxisRow.SetCell(Ninth, statisticData?.Vertical?.NegativeStandardDeviation?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                bAxisRow.SetCell(Tenth, statisticData?.Vertical?.PositiveStandardDeviation?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                bAxisRow.SetCell(Eleventh, statisticData?.Vertical?.AbsoluteStandardDeviation?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                bAxisRow.SetCell(Twelfth, statisticData?.Vertical?.Amplitude?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                bAxisRow.SetCell(Thirteenth, statisticData?.Vertical?.MaxNegativeReadingValue?.ZDisplacement?.RoundNumber(2).ToString() ?? "-", fontSize: 10, picture: bMaxNegativeReadingPicture);
                bAxisRow.SetCell(Fourteenth, statisticData?.Vertical?.MaxPositiveReadingValue?.ZDisplacement?.RoundNumber(2).ToString() ?? "-", fontSize: 10, picture: bMaxPositiveReadingPicture);

                rowIndex++;

                var planimetricMaxNegativeReadingPicture = new SimplePicture
                {
                    Path = images[statisticData.Planimetric?.MaxNegativeReadingValue?.SecurityLevelType ?? SecurityLevelType.None],
                    Width = 12,
                    Height = 12
                };

                var planimetricMaxPositiveReadingPicture = new SimplePicture
                {
                    Path = images[statisticData.Planimetric?.MaxPositiveReadingValue?.SecurityLevelType ?? SecurityLevelType.None],
                    Width = 12,
                    Height = 12
                };

                var planimetricRow = table.CreateRow();

                planimetricRow.AddNewTableCell();
                planimetricRow.AddNewTableCell();
                planimetricRow.AddNewTableCell();
                planimetricRow.AddNewTableCell();
                planimetricRow.AddNewTableCell();
                planimetricRow.AddNewTableCell();
                planimetricRow.AddNewTableCell();
                planimetricRow.AddNewTableCell();

                planimetricRow.SetCell(Zero, instrument.Name, fontSize: 10);
                planimetricRow.SetCell(First, "Planimétrico", fontSize: 10);
                planimetricRow.SetCell(Second, statisticData?.Vertical?.NegativeAverage?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                planimetricRow.SetCell(Third, statisticData?.Vertical?.PositiveAverage?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                planimetricRow.SetCell(Fourth, statisticData?.Vertical?.AbsoluteAverage?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                planimetricRow.SetCell(Fifth, statisticData?.Vertical?.NegativeMedian?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                planimetricRow.SetCell(Sixth, statisticData?.Vertical?.PositiveMedian?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                planimetricRow.SetCell(Seventh, statisticData?.Vertical?.AbsoluteMedian?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                planimetricRow.SetCell(Eighth, statisticData?.Vertical?.AbsoluteMedian?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                planimetricRow.SetCell(Ninth, statisticData?.Vertical?.NegativeStandardDeviation?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                planimetricRow.SetCell(Tenth, statisticData?.Vertical?.PositiveStandardDeviation?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                planimetricRow.SetCell(Eleventh, statisticData?.Vertical?.AbsoluteStandardDeviation?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                planimetricRow.SetCell(Twelfth, statisticData?.Vertical?.Amplitude?.RoundNumber(2).ToString() ?? "-", fontSize: 10);
                planimetricRow.SetCell(Thirteenth, statisticData?.Vertical?.MaxNegativeReadingValue?.ZDisplacement?.RoundNumber(2).ToString() ?? "-", fontSize: 10, picture: planimetricMaxNegativeReadingPicture);
                planimetricRow.SetCell(Fourteenth, statisticData?.Vertical?.MaxPositiveReadingValue?.ZDisplacement?.RoundNumber(2).ToString() ?? "-", fontSize: 10, picture: planimetricMaxPositiveReadingPicture);

                rowIndex++;

                table.MergeCellsVertically(
                    Zero,
                    rowIndex - 3,
                    rowIndex);
            }

            table.SetCellMargins(0, 0, 0, 0);
        }

        public static void AddMosaicSettlementGaugeReadingsTable(
            this XWPFDocument document,
            IEnumerable<Instrument> data,
            IDictionary<SecurityLevelType, string> images)
        {
            if (!data.Any())
            {
                return;
            }

            data = data.Where(instrument => instrument.Type == InstrumentType.SettlementGauge);

            var table = document.Tables.FirstOrDefault(t => t.TableCaption == SettlementGaugeReadingsTable);

            var rowIndex = 0;

            foreach (var instrument in data)
            {
                var statisticData = JsonSerializer.Deserialize<SettlementGaugeStatisticCollection>(instrument.StatisticData) as SettlementGaugeStatisticCollection;

                if (statisticData == null)
                {
                    continue;
                }

                foreach (var statistic in statisticData.Data)
                {
                    var lastReadingPicture = new SimplePicture
                    {
                        Path = images[statistic.LastReadingValue.SecurityLevelType],
                        Width = 12,
                        Height = 12
                    };

                    var newRow = table.CreateRow();

                    newRow.SetCell(Zero, instrument.Name, fontSize: 10);
                    newRow.SetCell(First, instrument.Depth?.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.SetCell(Second, statistic.MeasurementName, fontSize: 10);
                    newRow.SetCell(Third, statistic.MaxReadingValue.RelativeSettlement?.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.SetCell(Fourth, statistic.MinReadingValue.RelativeSettlement?.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.SetCell(Fifth, statistic.Average.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.SetCell(Sixth, statistic.Median.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.SetCell(Seventh, statistic.AbsoluteVariation.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.SetCell(Eighth, statistic.StandardDeviation.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.SetCell(Ninth, statistic.LastReadingValue.RelativeSettlement?.RoundNumber(2).ToString(), fontSize: 10, picture: lastReadingPicture);
                    newRow.SetCell(Tenth, statistic.LastReadingValue.Date.ToString("dd/MM/yyyy"), fontSize: 10);

                    rowIndex++;
                }

                if (instrument.Measurements.Count() < 2)
                {
                    continue;
                }

                table.MergeCellsVertically(
                    Zero,
                    rowIndex - (instrument.Measurements.Count() - 1),
                    rowIndex);

                table.MergeCellsVertically(
                    First,
                    rowIndex - (instrument.Measurements.Count() - 1),
                    rowIndex);
            }

            table.SetCellMargins(0, 0, 0, 0);
        }

        public static void AddMosaicInclinometerReadingsTable(
            this XWPFDocument document,
            IEnumerable<Instrument> data,
            IDictionary<SecurityLevelType, string> images)
        {
            if (!data.Any())
            {
                return;
            }

            data = data.Where(instrument => instrument.Type == InstrumentType.ConventionalInclinometer || instrument.Type == InstrumentType.IPIInclinometer);

            var table = document.Tables.FirstOrDefault(t => t.TableCaption == InclinometerReadingsTable);

            var rowIndex = 0;

            foreach (var instrument in data)
            {
                var statisticData = JsonSerializer.Deserialize<SettlementGaugeStatisticCollection>(instrument.StatisticData) as SettlementGaugeStatisticCollection;

                foreach (var statistic in statisticData.Data)
                {
                    var lastReadingPicture = new SimplePicture
                    {
                        Path = images[statistic.LastReadingValue.SecurityLevelType],
                        Width = 12,
                        Height = 12
                    };

                    var newRow = table.CreateRow();

                    newRow.SetCell(Zero, instrument.Name, fontSize: 10);
                    newRow.SetCell(First, instrument.Depth?.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.SetCell(Second, statistic.MeasurementName, fontSize: 10);
                    newRow.SetCell(Third, statistic.MaxReadingValue.DeviationA?.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.SetCell(Fourth, statistic.MinReadingValue.DeviationA?.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.SetCell(Fifth, statistic.Average.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.SetCell(Sixth, statistic.Median.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.SetCell(Seventh, statistic.AbsoluteVariation.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.SetCell(Eighth, statistic.StandardDeviation.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.SetCell(Ninth, statistic.LastReadingValue.DeviationA?.RoundNumber(2).ToString(), fontSize: 10, picture: lastReadingPicture);
                    newRow.SetCell(Tenth, statistic.LastReadingValue.Date.ToString("dd/MM/yyyy"), fontSize: 10);

                    rowIndex++;
                }

                if (instrument.Measurements.Count() < 2 || statisticData.Data.Count == 0)
                {
                    continue;
                }

                table.MergeCellsVertically(
                    Zero,
                    rowIndex - (instrument.Measurements.Count() - 1),
                    rowIndex);

                table.MergeCellsVertically(
                    First,
                    rowIndex - (instrument.Measurements.Count() - 1),
                    rowIndex);
            }

            table.SetCellMargins(0, 0, 0, 0);
        }

        public static void AddMosaicStabilityTable(
            this XWPFDocument document,
            GetEoRReportDataResponse data,
            IDictionary<SecurityLevelType, string> images)
        {
            if (data.StabilityStatistics is null)
            {
                return;
            }

            var stabilityData = JsonSerializer.Deserialize<IEnumerable<MosaicStabilityData>>(data.StabilityStatistics);

            var items = (stabilityData as IEnumerable<MosaicStabilityData>).ConvertToEorReportTable(data.Sections);

            var okPicture = new SimplePicture
            {
                Path = images[SecurityLevelType.None],
                Width = 12,
                Height = 12
            };

            var alertPicture = new SimplePicture
            {
                Path = images[SecurityLevelType.Alert],
                Width = 12,
                Height = 12
            };

            var table = document.Tables.FirstOrDefault(t => t.TableCaption == TextPlaceholders.Eor.StabilityTable);

            var rowIndex = 0;

            foreach (var section in items.Sections.OrderBy(section => section.SectionName))
            {
                foreach (var value in section.Values)
                {
                    var picture = value.LastValueHasAlert
                        ? alertPicture
                        : okPicture;

                    var newRow = table.CreateRow();

                    newRow.AddNewTableCell();
                    newRow.AddNewTableCell();

                    newRow.SetCell(Zero, section.SectionName, fontSize: 10);
                    newRow.SetCell(First, value.SoilConditionType.GetDescription(), fontSize: 10);
                    newRow.SetCell(Second, value.MaxValue.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.SetCell(Third, value.MinValue.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.SetCell(Fourth, value.AverageValue.RoundNumber(2).ToString(), fontSize: 10);
                    newRow.SetCell(Fifth, value.LastValue.RoundNumber(2).ToString(), fontSize: 10, picture: picture);

                    rowIndex++;
                }

                if (section.Values.Count() < 2)
                {
                    continue;
                }

                table.MergeCellsVertically(
                    Zero,
                    rowIndex - (section.Values.Count() - 1),
                    rowIndex);
            }
        }
    }
}
