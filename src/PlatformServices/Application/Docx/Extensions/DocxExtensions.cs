using Domain.Entities.Report;
using NPOI.XWPF.UserModel;
using System.Text;
using System.Text.RegularExpressions;

namespace Application.Docx.Extensions
{
    public static class XWPFDocumentExtensions
    {
        public static void ReplaceText(
            this XWPFDocument document,
            IDictionary<string, object> items)
        {
            foreach (var item in items)
            {
                var placeholder = "{{" + item.Key + "}}";

                foreach (XWPFParagraph paragraph in document.Paragraphs)
                {
                    ReplaceTextInParagraph(paragraph, placeholder, item.Value?.ToString());
                }

                foreach (XWPFHeader header in document.HeaderList)
                {
                    foreach (XWPFParagraph paragraph in header.Paragraphs)
                    {
                        ReplaceTextInParagraph(paragraph, placeholder, item.Value?.ToString());
                    }

                    foreach (var table in header.Tables)
                    {
                        foreach (XWPFTableRow row in table.Rows)
                        {
                            foreach (XWPFTableCell cell in row.GetTableCells())
                            {
                                foreach (XWPFParagraph paragraph in cell.Paragraphs)
                                {
                                    ReplaceTextInParagraph(paragraph, placeholder, item.Value?.ToString());
                                }
                            }
                        }
                    }
                }

                foreach (XWPFTable table in document.Tables)
                {
                    foreach (XWPFTableRow row in table.Rows)
                    {
                        foreach (XWPFTableCell cell in row.GetTableCells())
                        {
                            foreach (XWPFParagraph paragraph in cell.Paragraphs)
                            {
                                ReplaceTextInParagraph(paragraph, placeholder, item.Value?.ToString());
                            }
                        }
                    }
                }

                document.FindAndReplaceText(placeholder, item.Value?.ToString());
            }
        }

        public static void ReplacePictures(
           this XWPFDocument document,
           IDictionary<string, ReportPicture> items)
        {
            const string pattern = "{{(.*?)}}";

            foreach (var paragraph in document.Paragraphs.ToList())
            {
                if (!Regex.IsMatch(paragraph.Text, pattern))
                {
                    continue;
                }

                var runs = paragraph.Runs.ToList();

                StringBuilder concatenatedTextBuilder = new StringBuilder();

                foreach (var run in runs)
                {
                    concatenatedTextBuilder.Append(run.Text);
                }

                var concatenatedText = concatenatedTextBuilder.ToString();

                for (int i = runs.Count - 1; i >= 0; i--)
                {
                    paragraph.RemoveRun(i);
                }

                CreateRunsWithText(paragraph, concatenatedText, runs.FirstOrDefault());
            }

            foreach (var paragraph in document.Paragraphs.ToList())
            {
                foreach (var item in items)
                {
                    var placeholder = "{{" + item.Key + "}}";

                    foreach (var run in paragraph.Runs.ToList())
                    {
                        if (!run.Text.Contains(placeholder))
                        {
                            continue;
                        }

                        var newText = run.Text.Replace(placeholder, string.Empty);
                        run.ReplaceText(run.Text, newText);

                        var picture = item.Value;
                        using var stream = new FileStream(picture.Path, FileMode.Open, FileAccess.Read);

                        var pic = run.AddPicture(stream, PictureType.PNG.GetHashCode(), picture.Path, picture.Width * 9525, picture.Height * 9525);
                        run.AddBreak(BreakType.TEXTWRAPPING);

                        var captionText = picture.GetCaption();

                        if (!string.IsNullOrEmpty(captionText))
                        {
                            paragraph.Alignment = ParagraphAlignment.CENTER;

                            run.AppendText(captionText);
                            run.IsBold = false;
                            run.IsItalic = true;
                            run.AddBreak(BreakType.TEXTWRAPPING);
                            run.FontSize = 10;
                        }
                    }
                }
            }
        }

        private static void ReplaceTextInParagraph(XWPFParagraph paragraph, string placeholder, string newValue)
        {
            List<XWPFRun> runs = paragraph.Runs.ToList();
            StringBuilder concatenatedTextBuilder = new StringBuilder();

            foreach (var run in runs)
            {
                concatenatedTextBuilder.Append(run.Text);
            }

            string concatenatedText = concatenatedTextBuilder.ToString();

            if (!concatenatedText.Contains(placeholder))
            {
                return;
            }

            // Use regex to find all placeholders
            Regex regex = new Regex(Regex.Escape(placeholder));
            MatchCollection matches = regex.Matches(concatenatedText);

            if (matches.Count > 0)
            {
                // Clear all the runs
                for (int i = runs.Count - 1; i >= 0; i--)
                {
                    paragraph.RemoveRun(i);
                }

                // For each match, replace the placeholder and retain the text and styles
                int lastIndex = 0;
                foreach (Match match in matches)
                {
                    // Get text before the current placeholder
                    string beforePlaceholder = concatenatedText.Substring(lastIndex, match.Index - lastIndex);
                    CreateRunsWithText(paragraph, beforePlaceholder, runs.FirstOrDefault());

                    // Replace the placeholder with newValue
                    CreateRunsWithText(paragraph, newValue, runs.FirstOrDefault());

                    // Update lastIndex to after the current placeholder
                    lastIndex = match.Index + match.Length;
                }

                // Handle text after the last placeholder
                string afterLastPlaceholder = concatenatedText.Substring(lastIndex);
                CreateRunsWithText(paragraph, afterLastPlaceholder, runs.FirstOrDefault());

                return;
            }

            // Handle split placeholders across multiple runs and line breaks
            StringBuilder currentTextBuilder = new StringBuilder();
            int splitPlaceholderIndex = -1;

            for (int i = 0; i < runs.Count; i++)
            {
                currentTextBuilder.Append(runs[i].Text);

                if (currentTextBuilder.ToString().Contains(placeholder))
                {
                    splitPlaceholderIndex = i;
                    break;
                }
            }

            if (splitPlaceholderIndex != -1)
            {
                string beforePlaceholder = concatenatedText.Substring(0, concatenatedText.IndexOf(placeholder));
                string afterPlaceholder = concatenatedText.Substring(concatenatedText.IndexOf(placeholder) + placeholder.Length);

                // Clear all the runs
                for (int i = runs.Count - 1; i >= 0; i--)
                {
                    paragraph.RemoveRun(i);
                }

                // Create new runs and retain line breaks
                CreateRunsWithText(paragraph, beforePlaceholder, runs.First());
                CreateRunsWithText(paragraph, newValue, runs[splitPlaceholderIndex]);
                CreateRunsWithText(paragraph, afterPlaceholder, runs.Last());
            }
        }

        private static void CreateRunsWithText(XWPFParagraph paragraph, string text, XWPFRun templateRun)
        {
            if (string.IsNullOrEmpty(text))
                return;

            string[] lines = text.Split(new[] { "\r\n", "\n" }, StringSplitOptions.None);
            for (int i = 0; i < lines.Length; i++)
            {
                if (i > 0)
                {
                    XWPFRun breakRun = paragraph.CreateRun();
                    breakRun.AddCarriageReturn();
                }

                if (!string.IsNullOrEmpty(lines[i]))
                {
                    XWPFRun run = paragraph.CreateRun();
                    run.SetText(lines[i], 0);
                    CloneRunProperties(templateRun, run);
                }
            }
        }

        private static void CloneRunProperties(XWPFRun sourceRun, XWPFRun targetRun)
        {
            if (sourceRun == null || targetRun == null)
            {
                return;
            }

            targetRun.IsBold = sourceRun.IsBold;
            targetRun.IsItalic = sourceRun.IsItalic;
            targetRun.IsStrikeThrough = sourceRun.IsStrikeThrough;
            targetRun.Underline = sourceRun.Underline;

            targetRun.FontSize = sourceRun.FontSize > 0
                ? sourceRun.FontSize
                : 12;

            targetRun.FontFamily = !string.IsNullOrEmpty(sourceRun.FontFamily)
                ? sourceRun.FontFamily
                : "Arial";

            var targetColor = !string.IsNullOrEmpty(sourceRun.GetColor())
                ? sourceRun.GetColor()
                : "#000000";

            targetRun.SetColor(targetColor);
        }

        public static void MergeCellsVertically(
            this XWPFTable table,
            int columnIndex,
            int startRowIndex,
            int endRowIndex)
        {
            for (int i = startRowIndex; i <= endRowIndex; i++)
            {
                var row = table.GetRow(i);
                var cell = row.GetCell(columnIndex);
                if (i == startRowIndex)
                {
                    cell.GetCTTc().AddNewTcPr().AddNewVMerge().val = NPOI.OpenXmlFormats.Wordprocessing.ST_Merge.restart;
                }
                else
                {
                    cell.GetCTTc().AddNewTcPr().AddNewVMerge().val = NPOI.OpenXmlFormats.Wordprocessing.ST_Merge.@continue;
                }
            }
        }
    }
}
