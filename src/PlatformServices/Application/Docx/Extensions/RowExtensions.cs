using Domain.Entities.Report;
using NPOI.OpenXmlFormats.Wordprocessing;
using NPOI.XWPF.UserModel;
using static Application.Docx.Constants.TableIndexes;

namespace Application.Docx.Extensions
{
    public static class XWPFTableRowExtensions
    {
        public static void SetFirstColumn(
            this XWPFTableRow row,
            string text = "")
        {
            row.GetCell(Zero).SetColor("#00008B");
            row.SetCell(Zero, text, textColor: "#FFFFFF", backgroundColor: "#00008B");
        }

        public static void SetCell(
            this XWPFTableRow row,
            int index,
            string text,
            int fontSize = 12,
            bool isBold = false,
            string textColor = "#000000",
            string backgroundColor = "#FFFFFF",
            ReportPicture picture = null)
        {
            row.GetCell(index).Paragraphs.Clear();
            row.GetCell(index).SetVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            row.GetCell(index).SetColor(backgroundColor);

            var paragraph = row.GetCell(index).AddParagraph();
            paragraph.Alignment = ParagraphAlignment.CENTER;
            paragraph.FillPattern = ST_Shd.nil;

            var cellRun = paragraph.CreateRun();
            cellRun.SetColor(textColor);
            cellRun.SetFontFamily("Arial", FontCharRange.Ascii);
            cellRun.FontFamily = "Arial";
            cellRun.IsBold = isBold;
            cellRun.FontSize = fontSize;
            cellRun.CharacterSpacing = 0;

            cellRun.SetText(text);

            if (picture != null)
            {
                cellRun.AppendText(" ");

                using var fs = new FileStream(picture.Path, FileMode.Open, FileAccess.Read);

                cellRun.AddPicture(fs, (int)PictureType.PNG, picture.Path, picture.Width * 9525, picture.Height * 9525); // Size in EMUs (1 pixel = 9525 EMUs)
            }

            row.GetCell(index).RemoveParagraph(Zero);
        }
    }
}
