using Application.Apis.Clients.Response.Report;
using Application.Report.Extensions;
using Application.WebScraper.Constants;
using Domain.Enums;
using NPOI.XWPF.UserModel;
using System.Text.Json;
using static Application.Docx.Constants.TableIndexes;

namespace Application.Docx.Extensions
{
    public static class ValeXWPFDocumentExtensions
    {
        public static void AddValeResponsiblesTable(
            this XWPFDocument document,
            IEnumerable<Responsible> data)
        {
            var table = document.Tables.FirstOrDefault(t => t.TableCaption == TextPlaceholders.Eor.ResponsiblesTable);
            table.SetColumnWidth(Zero, 15);
            table.SetColumnWidth(First, 15);
            table.SetColumnWidth(Second, 10);
            table.SetColumnWidth(Third, 15);
            table.SetColumnWidth(Fourth, 10);
            table.SetColumnWidth(Fifth, 20);
            table.SetColumnWidth(Sixth, 155);

            foreach (var item in data)
            {
                var newRow = table.CreateRow();
                newRow.AddNewTableCell();
                newRow.AddNewTableCell();
                newRow.AddNewTableCell();
                newRow.AddNewTableCell();
                newRow.AddNewTableCell();
                newRow.AddNewTableCell();

                newRow.SetCell(Zero, item.Name);
                newRow.SetCell(Constants.TableIndexes.First, "XXXX");
                newRow.SetCell(Second, item.PositionName);
                newRow.SetCell(Third, "XXXX");
                newRow.SetCell(Fourth, item.ProfessionalRecord);
                newRow.SetCell(Fifth, item.EmailAddress);
                newRow.SetCell(Sixth, "XXXX");

                table.AddRow(newRow);
            }

            table.SetCellMargins(5, 5, 5, 5);
        }

        public static void AddValePercolationReadingsTable(
            this XWPFDocument document,
            IEnumerable<Instrument> data)
        {
            var items = data
                .Where(item => item.Subtype == InstrumentSubtype.Percolation)
                .Select(item => item.ConvertToEorReportTable());

            var table = document.Tables.FirstOrDefault(t => t.TableCaption == TextPlaceholders.Eor.PercolationReadingsTable);

            foreach (var item in items)
            {
                var newRow = table.CreateRow();
                newRow.AddNewTableCell();
                newRow.AddNewTableCell();
                newRow.AddNewTableCell();
                newRow.AddNewTableCell();

                var color = item.IsOffline ? "808080" : "000000";

                newRow.SetCell(Zero, item.Instrument, fontSize: 6, textColor: color);
                newRow.SetCell(Constants.TableIndexes.First, item.TopQuotaValue, fontSize: 6, textColor: color);
                newRow.SetCell(Second, item.BaseQuotaValue, fontSize: 6, textColor: color);
                newRow.SetCell(Third, item.SafetyFactorNormalValue, fontSize: 6, textColor: color);
                newRow.SetCell(Fourth, item.SafetyFactorAttentionValue, fontSize: 6, textColor: color);
                newRow.SetCell(Fifth, item.SafetyFactorAlertValue, fontSize: 6, textColor: color);
                newRow.SetCell(Sixth, item.SafetyFactorEmergencyValue, fontSize: 6, textColor: color);
                newRow.SetCell(Seventh, item.LastReadingValue, fontSize: 6, textColor: color);
                newRow.SetCell(Eighth, item.MaxReadingValue, fontSize: 6, textColor: color);

                table.AddRow(newRow);
            }

            table.SetCellMargins(0, 0, 0, 0);
        }

        public static void AddValeStabilityTable(
            this XWPFDocument document,
            GetEoRReportDataResponse data)
        {
            if (data.StabilityStatistics is null)
            {
                return;
            }

            var stabilityData = JsonSerializer.Deserialize<IEnumerable<ValeStabilityData>>(data.StabilityStatistics);

            var items = (stabilityData as IEnumerable<ValeStabilityData>)
                .ConvertToEorReportTable(data.Sections);

            var table = document.Tables.FirstOrDefault(t => t.TableCaption == TextPlaceholders.Eor.StabilityTable);

            var row = 0;

            foreach (var section in items.Sections)
            {
                foreach (var fortnight in section.Fortnights?.OrderBy(item => item.Month).ThenBy(item => item.Fortnight))
                {
                    var newRow = table.CreateRow();

                    newRow.SetCell(Zero, section.SectionName, fontSize: 10);
                    newRow.SetCell(Constants.TableIndexes.First, fortnight.Name, fontSize: 10);
                    newRow.SetCell(Second, fortnight.DrainedValue.ToString(), fontSize: 10);
                    newRow.SetCell(Third, fortnight.UndrainedValue.ToString(), fontSize: 10);
                    newRow.SetCell(Fourth, fortnight.PseudoStaticValue.ToString(), fontSize: 10);

                    row++;
                }

                if (section.Fortnights.Count() < 2)
                {
                    continue;
                }

                table.MergeCellsVertically(
                    Zero,
                    row - (section.Fortnights.Count() - 1),
                    row);
            }

            table.SetCellMargins(0, 0, 0, 0);
        }
    }
}
