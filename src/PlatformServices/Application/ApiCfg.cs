using Application.Apis.Clients;
using Application.Apis.Keycloak;
using Application.Apis.Slide2;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Refit;
using System.Net;
using System.Text.Json;
using Application.Apis.AITextGeneration;
using Application.Apis.AITranscription;

namespace Application
{
    public static class ApiCfg
    {
        public static IServiceCollection AddApis(this IServiceCollection services, IConfiguration configuration)
        {
            HttpMessageHandler httpHandler = new HttpClientHandler
            {
                AutomaticDecompression = GetDecompressionMethods(),
            };

            static DecompressionMethods GetDecompressionMethods() =>
                       DecompressionMethods.Deflate | DecompressionMethods.GZip;

            var serializerOptions = new JsonSerializerOptions()
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            serializerOptions.Converters.Add(new ObjectToInferredTypesConverter());

            var refitSettings = new RefitSettings()
            {
                ContentSerializer = new SystemTextJsonContentSerializer(serializerOptions)
            };

            services
               .AddRefitClient<IKeycloakApi>()
               .ConfigureHttpClient(x => x.BaseAddress = new Uri(configuration["Apis:Keycloak:Url"]))
               .ConfigurePrimaryHttpMessageHandler(_ => httpHandler);

            services.AddSingleton<IKeycloakApiService, KeycloakApiService>(x =>
               new KeycloakApiService(
                   x.GetService<IKeycloakApi>()!,
                   configuration["Auth:Workflows:ClientId"],
                   configuration["Auth:Workflows:ClientSecret"],
                   configuration["Apis:Keycloak:Realm"]));

            services
               .AddRefitClient<IClientsApi>(refitSettings)
               .ConfigureHttpClient(x => x.BaseAddress = new Uri(configuration["Apis:Clients:Url"]))
               .ConfigurePrimaryHttpMessageHandler(_ => httpHandler);

            services.AddSingleton<IClientsApiService, ClientsApiService>();

            services
               .AddRefitClient<ISlide2Api>(refitSettings)
               .ConfigureHttpClient(x =>
               {
                   x.BaseAddress = new Uri(configuration["Apis:Slide2:Url"]);
                   x.Timeout = TimeSpan.FromMinutes(20);
                   x.MaxResponseContentBufferSize = int.MaxValue;
                   x.DefaultRequestHeaders.Add("Accept-Encoding", "gzip, deflate");
                   x.DefaultRequestHeaders.Add("Connection", "keep-alive");
               })
               .ConfigurePrimaryHttpMessageHandler(_ => httpHandler);

            services.AddSingleton<ISlide2ApiService, Slide2ApiService>();
            
            services
                .AddRefitClient<ITranscriptionApi>().ConfigureHttpClient(options =>
                {
                    options.BaseAddress = 
                        new Uri(configuration["Apis:Transcription:Url"]);
                    
                    options.DefaultRequestHeaders.Add(
                        "x-functions-key",
                        configuration["Apis:Transcription:ApiKey"]);
                    
                    var timeout = configuration.GetValue<int>("Apis:Transcription:TimeoutInSeconds");
                    
                    options.Timeout = TimeSpan.FromSeconds(timeout);
                });
            
            services.AddSingleton<ITranscriptionApiService, TranscriptionApiService>();

            services
                .AddRefitClient<ITextGenerationApi>()
                .ConfigureHttpClient(options =>
                {
                    options.BaseAddress = 
                        new Uri(configuration["Apis:TextGeneration:Url"]);
                    
                    options.DefaultRequestHeaders.Add(
                        "x-functions-key",
                        configuration["Apis:TextGeneration:ApiKey"]);
                    
                    var timeout = configuration.GetValue<int>("Apis:TextGeneration:TimeoutInSeconds");
                    
                    options.Timeout = TimeSpan.FromSeconds(timeout);
                });
                
            services.AddSingleton<ITextGenerationApiService, TextGenerationApiService>();

            return services;
        }
    }
}
