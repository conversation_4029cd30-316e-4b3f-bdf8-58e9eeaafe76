namespace Application.BlobStorage
{
    public interface IBlobStorageService
    {
        Task<string> UploadAsync(byte[] arrayByte, string fileName, string containerName);
        Task<string> UploadAsync(Stream stream, string fileName, string containerName);
        Task<byte[]> GetAsync(string fileName, string containerName);
        Task<bool> DeleteAsync(string fileName, string containerName);
    }
}
