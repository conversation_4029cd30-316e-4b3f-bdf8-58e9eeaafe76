using Azure.Storage.Blobs;
using Microsoft.Extensions.Options;

namespace Application.BlobStorage
{
    public sealed class BlobStorageService : IBlobStorageService
    {
        private readonly BlobServiceClient _blobClient;

        public BlobStorageService(IOptions<BlobStorageOptions> options)
        {
            _blobClient = new BlobServiceClient(options.Value.ConnectionString);
        }

        private async Task<BlobContainerClient> GetContainer(string containerName)
        {
            var container = _blobClient.GetBlobContainerClient(containerName);

            var exists =
                await container
                    .ExistsAsync();

            if (!exists)
            {
                container =
                    await _blobClient
                        .CreateBlobContainerAsync(containerName);
            }

            return container;
        }

        public async Task<string> UploadAsync(byte[] arrayByte, string fileName, string containerName)
        {
            await using var file = new MemoryStream(arrayByte);

            var container = await GetContainer(containerName).ConfigureAwait(false);

            var blockBlobReference = container.GetBlobClient(fileName);

            await blockBlobReference.UploadAsync(file, true).ConfigureAwait(false);

            return blockBlobReference.Uri.AbsoluteUri;
        }

        public async Task<string> UploadAsync(Stream stream, string fileName, string containerName)
        {
            var container = await GetContainer(containerName).ConfigureAwait(false);
            var blockBlobReference = container.GetBlobClient(fileName);

            await blockBlobReference.UploadAsync(stream, true).ConfigureAwait(false);

            return blockBlobReference.Uri.AbsoluteUri;
        }

        public async Task<byte[]> GetAsync(string fileName, string containerName)
        {
            var container = await GetContainer(containerName).ConfigureAwait(false);
            var blockBlobReference = container.GetBlobClient(fileName);

            if (!await blockBlobReference.ExistsAsync().ConfigureAwait(false))
            {
                return null;
            }

            using var ms = new MemoryStream();
            await blockBlobReference.DownloadToAsync(ms).ConfigureAwait(false);

            return ms.ToArray();
        }

        public async Task<bool> DeleteAsync(string fileName, string containerName)
        {
            var container = await GetContainer(containerName).ConfigureAwait(false);
            var blockBlobReference = container.GetBlobClient(fileName);

            return await blockBlobReference.DeleteIfExistsAsync().ConfigureAwait(false);
        }
    }
}
