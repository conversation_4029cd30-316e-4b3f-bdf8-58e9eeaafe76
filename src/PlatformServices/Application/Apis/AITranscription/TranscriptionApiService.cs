using Microsoft.Extensions.Logging;
using Refit;
using static Application.Apis.AITranscription.Response.ProcessingStatus;

namespace Application.Apis.AITranscription;

public sealed class TranscriptionApiService : ITranscriptionApiService
{
    private readonly ITranscriptionApi _transcriptionApi;
    private readonly ILogger<TranscriptionApiService> _logger;

    public TranscriptionApiService(
        ITranscriptionApi transcriptionApi,
        ILogger<TranscriptionApiService> logger)
    {
        _transcriptionApi = transcriptionApi;
        _logger = logger;
    }

    public async Task<string> SendAudioAsync(byte[] byteArray, string fileName)
    {
        var response = await _transcriptionApi
            .SendAudioAsync(new ByteArrayPart(byteArray, fileName));

        if (!response.IsSuccessStatusCode)
        {
            _logger.LogWarning(
                response.Error,
                "Transcription API call failed with status code {0}.",
                response.StatusCode);

            return string.Empty;
        }

        return response.Content!.Id;
    }

    public async Task<string> GetTextAsync(string transactionId)
    {
        var transcription = string.Empty;

        do
        {
            var response = await _transcriptionApi.GetTextAsync(transactionId);
            
            if (!response.IsSuccessStatusCode || 
                response.Content!.Status == Failed)
            {
                _logger.LogWarning(
                    response.Error,
                    "Transcription API call for ID {0} failed with status code {1}.",
                    transactionId,
                    response.StatusCode);

                await Task.Delay(1000);
            }
            else if (response.Content!.Status == Processing)
            {
                _logger.LogInformation(
                    "Waiting for transcription to complete for ID {0}.",
                    transactionId);
                
                await Task.Delay(1000);
            }
            else
            {
                transcription = response.Content!.Transcription;
            }
        } while (string.IsNullOrEmpty(transcription));

        return transcription;
    }
}