using Application.Apis.AITranscription.Response;
using Refit;

namespace Application.Apis.AITranscription;

public interface ITranscriptionApi
{
    [Multipart]
    [Post("/api/azure-speech/transcription")]
    public Task<IApiResponse<SendAudioResponse>> SendAudioAsync([AliasAs("audio")] ByteArrayPart audio);

    [Get("/api/azure-speech/transcription")]
    public Task<IApiResponse<GetTextResponse>> GetTextAsync([AliasAs("id")] string id);
}