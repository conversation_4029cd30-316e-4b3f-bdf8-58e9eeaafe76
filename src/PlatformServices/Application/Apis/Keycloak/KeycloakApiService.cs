using Application.Apis._Shared.AccessToken;
using Application.Apis._Shared.ClientCredential;
using Refit;

namespace Application.Apis.Keycloak
{
    public sealed class KeycloakApiService : IKeycloakApiService
    {
        private readonly IKeycloakApi _keycloakApi;
        private readonly ClientCredential _clientCredential;
        private readonly string _realm;

        public KeycloakApiService(
           IKeycloakApi keycloakApi,
           string clientId,
           string clientSecret,
           string realm
           )
        {
            _keycloakApi = keycloakApi;
            _realm = realm;

            _clientCredential = new()
            {
                ClientId = clientId,
                ClientSecret = clientSecret
            };
        }

        public async Task<ApiResponse<AccessTokenResponse>> GetApiToken()
        {
            return await _keycloakApi.GetApiToken(_realm, _clientCredential);
        }
    }
}
