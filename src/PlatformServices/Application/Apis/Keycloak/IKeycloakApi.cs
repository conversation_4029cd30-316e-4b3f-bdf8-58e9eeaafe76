using Application.Apis._Shared.AccessToken;
using Application.Apis._Shared.ClientCredential;
using Refit;

namespace Application.Apis.Keycloak
{
    public interface IKeycloakApi
    {
        [Post("/realms/{realm}/protocol/openid-connect/token")]
        Task<ApiResponse<AccessTokenResponse>> GetApiToken(
            string realm,
            [Body(BodySerializationMethod.UrlEncoded)] ClientCredential clientCredential);
    }
}
