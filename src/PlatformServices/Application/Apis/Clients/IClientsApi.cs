using Application.Apis.Clients.Request;
using Application.Apis.Clients.Request.InspectionSheet;
using Application.Apis.Clients.Request.Instrument.GetAutomatedReadingsData;
using Application.Apis.Clients.Request.Notification;
using Application.Apis.Clients.Request.Occurrence;
using Application.Apis.Clients.Request.Simulation.List;
using Application.Apis.Clients.Request.Simulation.Patch;
using Application.Apis.Clients.Response;
using Application.Apis.Clients.Response.Clients;
using Application.Apis.Clients.Response.InspectionSheet;
using Application.Apis.Clients.Response.Instrument;
using Application.Apis.Clients.Response.Instrument.GetAutomatedReadingsData;
using Application.Apis.Clients.Response.Occurrence;
using Application.Apis.Clients.Response.OccurrenceListReport;
using Application.Apis.Clients.Response.Package.GetById;
using Application.Apis.Clients.Response.Reading;
using Application.Apis.Clients.Response.Report;
using Application.Apis.Clients.Response.Section.GetById;
using Application.Apis.Clients.Response.Simulation.GetById;
using Application.Apis.Clients.Response.Simulation.List;
using Application.Apis.Clients.Response.StabilityAnalysis.GetByIdWithSafetyFactorsMetrics;
using Application.Apis.Clients.Response.StaticMaterial.GetBySearchId;
using Application.Apis.Clients.Response.Structure.GetById;
using Application.Apis.Clients.Response.Structure.GetStabilityAnalysisInfo;
using Application.Apis.Clients.Response.User;
using Application.Apis.Users.Request;
using Application.Apis.Users.Response;
using Refit;

namespace Application.Apis.Clients
{
    public interface IClientsApi
    {
        [Get("/api/v1/clients/expiring")]
        Task<ApiResponse<List<GetExpiringClientsResponse>>> GetExpiringClients(
            [Authorize("Bearer")] string token);

        [Get("/api/v1/users")]
        Task<ApiResponse<List<ListUserResponse>>> GetUsers(
            [Query] ListUserRequest request,
            [Authorize("Bearer")] string token);

        [Get("/api/v1/simulations/{id}")]
        Task<ApiResponse<GetSimulationByIdResponse>> GetSimulationById(
            [AliasAs("id")] Guid id,
            [Authorize("Bearer")] string token);

        [Get("/api/v1/users/emails")]
        Task<ApiResponse<IEnumerable<GetUserEmailResponse>>> GetUsersEmails(
            [Query] GetUserEmailRequest request,
            [Authorize("Bearer")] string token);

        [Delete("/api/v1/notifications")]
        Task<HttpResponseMessage> DeleteNotifications(
            [Authorize("Bearer")] string token);

        [Get("/api/v1/readings/{id}")]
        Task<ApiResponse<GetReadingByIdResponse>> GetReadingById(
            [AliasAs("id")] Guid id,
            [Authorize("Bearer")] string token);

        [Get("/api/v1/packages/{id}")]
        Task<ApiResponse<GetPackageByIdResponse>> GetPackageById(
            [AliasAs("id")] Guid id,
            [Authorize("Bearer")] string token);

        [Patch("/api/v1/simulations/{id}/status")]
        Task<ApiResponse<Guid>> UpdateSimulationStatus(
            [AliasAs("id")] Guid id,
            [Body] UpdateSimulationStatusRequest request,
            [Authorize("Bearer")] string token);

        [Patch("/api/v1/packages/{id}/status")]
        Task<ApiResponse<Guid>> UpdatePackageStatus(
            [AliasAs("id")] Guid id,
            [Body] UpdatePackageStatusRequest request,
            [Authorize("Bearer")] string token);

        [Get("/api/v1/instruments/{id}")]
        Task<ApiResponse<GetInstrumentByIdResponse>> GetInstrumentById(
            [AliasAs("id")] Guid id,
            [Authorize("Bearer")] string token);
        
        [Get("/api/v1/instruments/multiple")]
        Task<ApiResponse<GetInstrumentsByIdsResponse>> GetInstrumentsByIds(
            [Query (CollectionFormat.Multi)] IEnumerable<Guid> ids,
            [Authorize("Bearer")] string token);

        [Get("/api/v1/reports/occurrence-list/{id}")]
        Task<ApiResponse<GetOccurrenceListReportByIdResponse>> GetOccurrenceListReportById(
            [AliasAs("id")] Guid id,
            [Authorize("Bearer")] string token);

        [Post("/api/v1/inspection-sheets/occurrences/search/unpaginated")]
        Task<ApiResponse<List<SearchOccurrenceUnpaginatedResponse>>> SearchOccurrenceUnpaginated(
            [Body] List<Guid> structures,
            [Query] SearchOccurrenceUnpaginatedRequest request,
            [Authorize("Bearer")] string token
            );

        [Get("/api/v1/sections/{id}/integration")]
        Task<ApiResponse<GetSectionByIdResponse>> GetSectionWithSliById(
            [AliasAs("id")] Guid id,
            [Authorize("Bearer")] string token);

        [Get("/api/v1/structures/{id}")]
        Task<ApiResponse<GetStructureByIdResponse>> GetStructureById(
            [AliasAs("id")] Guid id,
            [Authorize("Bearer")] string token);

        [Get("/api/v1/structures/{id}/stability-analysis-info")]
        Task<ApiResponse<GetStructureStabilityAnalysisInfoResponse>> GetStructureStabilityAnalysisInfo(
            [AliasAs("id")] Guid id,
            [Authorize("Bearer")] string token);

        [Get("/api/v1/instruments/{id}/readings/values")]
        Task<ApiResponse<GetAdjacentReadingValuesResponse>> GetAdjacentReadingValues(
            [AliasAs("id")] Guid id,
            [Query] Guid? measurementId,
            [Query] DateTime date,
            [Authorize("Bearer")] string token);

        [Post("/api/v1/instruments/{id}/security-level-alerts")]
        Task<ApiResponse<IEnumerable<Guid>>> AddSecurityLevelAlert(
            [AliasAs("id")] Guid id,
            [Body] AddInstrumentSecurityLevelAlertsRequest request,
            [Authorize("Bearer")] string token);

        [Patch("/api/v1/sections/{sectionId}/reviews/{reviewId}/sli")]
        Task<HttpResponseMessage> AddSliToSectionReview(
            [AliasAs("sectionId")] Guid sectionId,
            [AliasAs("reviewId")] Guid reviewId,
            [Body] AddSliFileRequest request,
            [Authorize("Bearer")] string token);

        [Post("/api/v1/static-materials/bulk")]
        Task<ApiResponse<List<GetStaticMaterialBySearchIdResponse>>> GetStaticMaterialsBySearchIds(
            [AliasAs("reviewDate")] DateTime? reviewDate,
            [Body] List<int> searchIds,
            [Authorize("Bearer")] string token);

        [Get("/api/v1/static-materials/{id}")]
        Task<ApiResponse<GetStaticMaterialBySearchIdResponse>> GetStaticMaterialById(
            [AliasAs("id")] Guid id,
            [Authorize("Bearer")] string token);

        [Get("/api/v1/instruments/{id}/readings/total-rainfall")]
        Task<ApiResponse<decimal?>> GetTotalRainfall(
            [AliasAs("id")] Guid id,
            [Query] DateTime readingValueDate,
            [Authorize("Bearer")] string token);

        [Get("/api/v1/reports/pending")]
        Task<ApiResponse<IEnumerable<GetPendingReportResponse>>> GetPendingReports(
            [AliasAs("startDate")] DateTime startDate,
            [AliasAs("endDate")] DateTime endDate,
            [Authorize("Bearer")] string token);

        [Patch("/api/v1/reports/{id}/emission-dates")]
        Task<ApiResponse<IEnumerable<GetPendingReportResponse>>> UpdateReportEmissionDates(
            [AliasAs("id")] Guid id,
            [Body] UpdateReportEmissionDatesRequest request,
            [Authorize("Bearer")] string token);

        [Post("/api/v1/notifications")]
        Task<HttpResponseMessage> AddNotification(
            [Body] AddNotificationRequest request,
            [Authorize("Bearer")] string token);

        [Post("/api/v1/notifications/exists")]
        Task<ApiResponse<bool>> CheckIfNotificationExists(
            [Body] ExistsNotificationRequest request,
            [Authorize("Bearer")] string token);

        [Get("/api/v1/users/{id}")]
        Task<ApiResponse<GetUserByIdResponse>> GetUserById(
           [AliasAs("id")] Guid id,
           [Authorize("Bearer")] string token);

        [Post("/api/v1/stability-analysis")]
        Task<ApiResponse<Guid>> CreateStabilityAnalysis(
            [Body] AddStabilityAnalysisRequest request,
            [Authorize("Bearer")] string token);

        [Get("/api/v1/stability-analysis/{id}/safety-factors-metrics")]
        Task<ApiResponse<GetStabilityAnalysisByIdWithSafetyFactorsMetricsResponse>>
            GetStabilityAnalysisWithSafetyFactorsMetricsAsync(
            [AliasAs("id")] Guid id,
            [Authorize("Bearer")] string token);

        [Post("/api/v1/packages")]
        Task<ApiResponse<IEnumerable<Guid>>> AddPackage(
            [Body] IEnumerable<Guid> readingValues,
            [Authorize("Bearer")] string token);

        [Post("/api/v1/packages/{id}/calculate")]
        Task<ApiResponse<Guid>> CalculatePackageStability(
            [AliasAs("id")] Guid id,
            [Body] CalculatePackageStabilityRequest request,
            [Authorize("Bearer")] string token);

        [Delete("/api/v1/packages/{id}")]
        Task<ApiResponse<Guid>> DeletePackage(
            [AliasAs("id")] Guid id,
            [AliasAs("sectionId")] Guid? sectionId,
            [Authorize("Bearer")] string token);

        [Get("/api/v1/readings/{id}/related-values")]
        Task<ApiResponse<IEnumerable<Guid>>> GetRelatedReadingValues(
             [AliasAs("id")] Guid id,
             [Authorize("Bearer")] string token);

        [Post("/api/v1/readings/statistical-calculations")]
        Task<ApiResponse<List<GetReadingStatsResponse>>> GetReadingStats(
            [Body] GetReadingStatsRequest request,
            [Authorize("Bearer")] string token);

        [Post("/api/v1/readings/beach-lengths/statistical-calculations")]
        Task<ApiResponse<List<GetBeachLengthStatsResponse>>> GetBeachLengthStats(
            [Body] GetBeachLengthStatsRequest request,
            [Authorize("Bearer")] string token);


        [Patch("/api/v1/simulations/{id}")]
        Task<HttpResponseMessage> PatchSimulation(
            [AliasAs("id")] Guid id,
            [Body] PatchSimulationRequest request,
            [Authorize("Bearer")] string token);

        [Get("/api/v1/clients/client-automated-readings-configurations")]
        Task<ApiResponse<IEnumerable<GetClientAutomatedReadingsConfigurationsResponse>>>
            GetClientAutomatedReadingsConfigurationsAsync(
             [Authorize("Bearer")] string token);

        [Post("/api/v1/readings")]
        Task<ApiResponse<IEnumerable<Guid>>> AddReadingsAsync(
            [Body] IEnumerable<AddReadingRequest> readings,
            [Authorize("Bearer")] string token);

        [Get("/api/v1/simulations")]
        Task<ApiResponse<List<ListSimulationResponse>>> ListSimulations(
            [Query] ListSimulationRequest request,
            [Authorize("Bearer")] string token);

        [Delete("/api/v1/simulations/{id}")]
        Task<HttpResponseMessage> DeleteSimulation(
            [AliasAs("id")] Guid id,
            [Authorize("Bearer")] string token);

        [Post("/api/v1/instruments/automated-readings/data")]
        Task<ApiResponse<List<GetAutomatedReadingsDataResponse>>> GetAutomatedReadingsDataAsync(
            [Body] GetAutomatedReadingsDataRequest request,
            [Authorize("Bearer")] string token);

        [Get("/api/v1/reports/{id}/data")]
        Task<ApiResponse<T>> GetReportData<T>(
            [AliasAs("id")] Guid id,
            [Authorize("Bearer")] string token);
        
        [Get("/api/v1/inspection-sheets/{id}")]
        Task<ApiResponse<GetInspectionSheetByIdResponse>> GetInspectionSheet(
            [AliasAs("id")] Guid id,
            [Authorize("Bearer")] string token);
        
        [Put("/api/v1/inspection-sheets/{id}")]
        Task<IApiResponse> UpdateInspectionSheet(
            [AliasAs("id")] Guid id,
            [Query("userCompletedTheInspection")] bool userCompletedTheInspection,
            [Query("transcriptionHasFinished")] bool transcriptionHasFinished,
            [Body] UpdateInspectionSheetRequest request,
            [Authorize("Bearer")] string token);

        [Get("/api/v1/clients/{id}/logo")]
        Task<ApiResponse<GetClientLogoResponse>> GetClientLogo(
            [AliasAs("id")] Guid id,
            [Authorize("Bearer")] string token);
    }
}
