using Application.Apis.Clients.Request;
using Domain.Enums;

namespace Application.Apis.Clients.Extensions
{
    public static class UsersExtensions
    {
        public static ListUserRequest GetListUserRequestForNotification(
            Guid structureId)
        {
            return new()
            {
                StructureId = structureId,
                Active = true,
                Roles = new()
                {
                    (int)Role.SuperSupport,
                    (int)Role.Support,
                    (int)Role.SuperAdministrator,
                    (int)Role.Administrator,
                    (int)Role.Operator,
                    (int)Role.Auditor
                }
            };
        }
    }
}
