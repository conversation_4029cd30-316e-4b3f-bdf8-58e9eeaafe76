using System.Text.Json.Serialization;
using Application.Apis.Clients.Response.InspectionSheet;
using Domain.Enums;

namespace Application.Apis.Clients.Request.InspectionSheet;

public sealed record UpdateInspectionSheetRequest
{
    [JsonPropertyName("id")]
    public Guid Id { get; set; }

    [JsonPropertyName("type")]
    public InspectionSheetType Type { get; set; }
    
    [JsonPropertyName("start_date")]
    public DateTime? StartDate { get; set; }
    
    [JsonPropertyName("end_date")]
    public DateTime? EndDate { get; set; }

    [JsonPropertyName("executed_by")]
    public string ExecutedBy { get; set; }
    
    [JsonPropertyName("responsibles")]
    public IEnumerable<InspectionSheetResponsible> Responsibles { get; set; }

    [JsonPropertyName("evaluator_name")]
    public string EvaluatorName { get; set; }

    [JsonPropertyName("evaluator_position")]
    public string EvaluatorPosition { get; set; }

    [JsonPropertyName("evaluator_crea")]
    public string EvaluatorCrea { get; set; }

    [JsonPropertyName("evaluator_art")]
    public string EvaluatorArt { get; set; }
    
    [JsonPropertyName("file")]
    public Application.Apis._Shared.File File { get; set; }

    [JsonPropertyName("spillway_structures_reliability")]
    public ScoringTypeSpillwayAndPercolation? SpillwayStructuresReliability { get; set; }

    [JsonPropertyName("percolation")]
    public ScoringTypeSpillwayAndPercolation? Percolation { get; set; }

    [JsonPropertyName("deformations_and_settlements")]
    public ScoringTypeDeformationsAndSlope? DeformationsAndSettlements { get; set; }

    [JsonPropertyName("slope_deterioration")]
    public ScoringTypeDeformationsAndSlope? SlopeDeterioration { get; set; }

    [JsonPropertyName("surface_drainage")]
    public ScoringTypeSurfaceDrainage? SurfaceDrainage { get; set; }
    
    [JsonPropertyName("notes")]
    public List<InspectionSheetNote> Notes { get; set; }
    
    [JsonPropertyName("identified_anomalies")]
    public List<IdentifiedAnomaly> IdentifiedAnomalies { get; set; }
    
    [JsonPropertyName("areas")]
    public IEnumerable<InspectionSheetArea> Areas { get; set; }
    
    [JsonIgnore]
    public bool UserCompletedTheInspection { get; set; }
    
    [JsonIgnore]
    public bool TranscriptionHasFinished { get; set; }
}