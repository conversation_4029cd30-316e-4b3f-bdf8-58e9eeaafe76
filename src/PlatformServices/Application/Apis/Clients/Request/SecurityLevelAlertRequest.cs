using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Request
{
    public sealed record SecurityLevelAlertRequest
    {
        [JsonPropertyName("reading_value_date")]
        public DateTime ReadingValueDate { get; set; }

        [JsonPropertyName("instrument_id")]
        public Guid InstrumentId { get; set; }

        [JsonPropertyName("measurement_id")]
        public Guid? MeasurementId { get; set; }

        [JsonPropertyName("difference")]
        public decimal Difference { get; set; }

        [JsonPropertyName("security_level")]
        public SecurityLevelType SecurityLevel { get; set; }

        [JsonPropertyName("displaced_axis")]
        public Axis? DisplacedAxis { get; set; }
    }
}
