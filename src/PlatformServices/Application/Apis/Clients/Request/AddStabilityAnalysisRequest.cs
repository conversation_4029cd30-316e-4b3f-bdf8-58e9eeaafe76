using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Request
{
    public sealed record AddStabilityAnalysisRequest
    {
        [JsonPropertyName("structure_id")]
        public Guid StructureId { get; init; }

        [JsonPropertyName("section_id")]
        public Guid SectionId { get; init; }

        [JsonPropertyName("section_review_id")]
        public Guid SectionReviewId { get; init; }

        [JsonPropertyName("construction_stage_id")]
        public Guid? ConstructionStageId { get; init; }

        [JsonPropertyName("reading_created_date")]
        public DateTime ReadingCreatedDate { get; init; }

        public bool Piping { get; init; }

        [JsonPropertyName("calculated_by_id")]
        public Guid? CalculatedById { get; init; }

        [JsonPropertyName("safety_factor_results")]
        public List<AddSafetyFactorRequest> SafetyFactorResults { get; init; }

        [JsonPropertyName("warnings")]
        public List<string> Warnings { get; init; }
    }
}
