using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Request
{
    public sealed record UpdateSimulationStatusRequest
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("status")]
        public SimulationStatus Status { get; set; }

        [JsonPropertyName("event")]
        public string Event { get; set; }
    }
}
