using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Request
{
    public sealed record AddSliFileRequest
    {
        [JsonPropertyName("section_id")]
        public Guid SectionId { get; set; }

        [JsonPropertyName("review_id")]
        public Guid ReviewId { get; set; }

        [JsonPropertyName("construction_stage_id")]
        public Guid? ConstructionStageId { get; set; }

        [JsonPropertyName("sli")]
        public Apis._Shared.File Sli { get; set; }
    }
}
