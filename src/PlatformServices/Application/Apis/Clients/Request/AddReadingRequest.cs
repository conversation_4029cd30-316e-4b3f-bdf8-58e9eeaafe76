using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Request
{
    public sealed record AddReadingRequest
    {
        [JsonPropertyName("instrument")]
        public InstrumentRequest Instrument { get; set; }

        [JsonPropertyName("is_referential")]
        public bool? IsReferential { get; set; }

        [JsonPropertyName("is_automated")]
        public bool? IsAutomated { get; set; }

        [JsonPropertyName("values")]
        public List<ReadingValueRequest> Values { get; set; } = new();
    }
}