using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Request.Simulation.Patch
{
    public sealed record PatchSimulationInstrument
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("instrument_id")]
        public Guid InstrumentId { get; init; }

        [JsonPropertyName("measurement_id")]
        public Guid? MeasurementId { get; init; }

        [JsonPropertyName("quota")]
        public decimal Quota { get; init; }
    }
}
