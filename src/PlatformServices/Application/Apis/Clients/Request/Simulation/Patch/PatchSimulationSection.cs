using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Request.Simulation.Patch
{
    public sealed record PatchSimulationSection
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("section_id")]
        public Guid SectionId { get; init; }

        [JsonPropertyName("beach_length")]
        public double? BeachLength { get; init; }

        [Json<PERSON>ropertyName("ignored_instruments")]
        public string IgnoredInstruments { get; init; }

        [JsonPropertyName("instruments")]
        public List<PatchSimulationInstrument> Instruments { get; init; }

        [JsonPropertyName("results")]
        public List<PatchSimulationResult> Results { get; set; }

        [JsonPropertyName("warnings")]
        public List<string> Warnings { get; set; }
    }
}
