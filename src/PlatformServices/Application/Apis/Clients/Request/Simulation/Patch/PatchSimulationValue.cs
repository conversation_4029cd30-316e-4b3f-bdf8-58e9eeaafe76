using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Request.Simulation.Patch
{
    public sealed record PatchSimulationValue
    {
        [JsonPropertyName("calculation_method")]
        public CalculationMethod CalculationMethod { get; init; }

        public double Value { get; init; }

        [JsonPropertyName("dxf_file")]
        public _Shared.File DxfFile { get; init; }

        [JsonPropertyName("png_file")]
        public _Shared.File PngFile { get; init; }
    }
}
