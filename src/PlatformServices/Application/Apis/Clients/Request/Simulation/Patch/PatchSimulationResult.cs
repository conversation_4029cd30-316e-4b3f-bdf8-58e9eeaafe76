using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Request.Simulation.Patch
{
    public sealed record PatchSimulationResult
    {
        [JsonPropertyName("sli_file_type")]
        public SliFileType SliFileType { get; init; }

        public List<PatchSimulationValue> Values { get; init; }

        [JsonPropertyName("sli_file")]
        public _Shared.File SliFile { get; init; }

        [JsonPropertyName("sltm_file")]
        public _Shared.File SltmFile { get; init; }
    }
}
