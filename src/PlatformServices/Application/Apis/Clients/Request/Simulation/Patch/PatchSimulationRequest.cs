using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Request.Simulation.Patch
{
    public sealed record PatchSimulationRequest
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("status")]
        public SimulationStatus Status { get; set; }

        [JsonPropertyName("upstream_linimetric_ruler_quota")]
        public decimal? UpstreamLinimetricRulerQuota { get; init; }

        [JsonPropertyName("downstream_linimetric_ruler_quota")]
        public decimal? DownstreamLinimetricRulerQuota { get; init; }

        [JsonPropertyName("sections")]
        public List<PatchSimulationSection> Sections { get; init; }
    }
}
