using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Request.Instrument.GetAutomatedReadingsData
{
    public sealed record GetAutomatedReadingsDataRequest
    {
        [JsonPropertyName("instrument_identifiers")]
        public IEnumerable<string> InstrumentIdentifiers { get; set; }

        [JsonPropertyName("instrument_types")]
        public IEnumerable<InstrumentType> InstrumentTypes { get; set; }

        [JsonPropertyName("client_id")]
        public Guid? ClientId { get; set; }

        [JsonPropertyName("structure_id")]
        public Guid? StructureId { get; set; }

        [JsonPropertyName("structure_name")]
        public string StructureName { get; set; }

        [JsonPropertyName("client_unit_name")]
        public string ClientUnitName { get; set; }
    }
}
