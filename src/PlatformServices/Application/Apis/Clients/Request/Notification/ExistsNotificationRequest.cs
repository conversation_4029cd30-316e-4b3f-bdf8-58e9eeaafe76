using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Request.Notification
{
    public sealed record ExistsNotificationRequest
    {
        [JsonPropertyName("message")]
        public string Message { get; set; }
        [JsonPropertyName("theme")]
        public NotificationTheme Theme { get; set; }
        [JsonPropertyName("start_date")]
        public DateTime StartDate { get; set; }
        [JsonPropertyName("end_date")]
        public DateTime EndDate { get; set; }
    }
}
