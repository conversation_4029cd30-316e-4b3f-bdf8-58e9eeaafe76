using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Request
{
    public sealed record GetReadingStatsRequest
    {
        [JsonPropertyName("instruments")]
        public List<GetReadingStatsInstrument> Instruments { get; set; }

        [JsonPropertyName("start_date")]
        public DateTime? StartDate { get; set; }

        [JsonPropertyName("end_date")]
        public DateTime? EndDate { get; set; }

        [JsonPropertyName("reading_statistical_measure")]
        public ReadingStatisticalMeasure ReadingStatisticalMeasure { get; set; }
    }
}
