using Domain.Enums;
using System.Text.Json.Serialization;
using File = Application.Apis._Shared.File;

namespace Application.Apis.Clients.Request
{
    public sealed record AddSafetyFactorRequest
    {
        [JsonPropertyName("sli_file_type")]
        public SliFileType SliFileType { get; init; }

        public List<AddSafetyFactorValue> Values { get; init; }

        [JsonPropertyName("sli_file")]
        public File SliFile { get; init; }

        [JsonPropertyName("sltm_file")]
        public File SltmFile { get; init; }
    }
}
