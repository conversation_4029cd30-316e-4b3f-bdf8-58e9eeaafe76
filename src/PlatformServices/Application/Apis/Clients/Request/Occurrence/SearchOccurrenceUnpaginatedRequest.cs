using Domain.Enums;

namespace Application.Apis.Clients.Request.Occurrence
{
    public sealed record SearchOccurrenceUnpaginatedRequest
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public OccurrenceStatus? Status { get; set; }
        public InspectionSheetType? InspectionSheetType { get; set; }
        public bool? WithActionPlan { get; set; }
        public OccurrenceDateFilter? DateFilter { get; set; }
        public int? DaysRemaining { get; set; }
    }
}
