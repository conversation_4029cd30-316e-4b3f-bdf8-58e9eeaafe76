using Domain.Enums;
using System.Text.Json.Serialization;
using File = Application.Apis._Shared.File;

namespace Application.Apis.Clients.Request
{
    public sealed record AddSafetyFactorValue
    {
        [JsonPropertyName("calculation_method")]
        public CalculationMethod CalculationMethod { get; init; }

        public double Value { get; init; }

        [JsonPropertyName("dxf_file")]
        public File DxfFile { get; init; }

        [JsonPropertyName("png_file")]
        public File PngFile { get; init; }
    }
}
