using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Request
{
    public sealed record GetBeachLengthStatsRequest
    {
        [JsonPropertyName("section_ids")]
        public List<Guid> SectionIds { get; init; }

        [JsonPropertyName("start_date")]
        public DateTime? StartDate { get; init; }

        [JsonPropertyName("end_date")]
        public DateTime? EndDate { get; init; }

        [JsonPropertyName("reading_statistical_measure")]
        public ReadingStatisticalMeasure ReadingStatisticalMeasure { get; init; }
    }
}
