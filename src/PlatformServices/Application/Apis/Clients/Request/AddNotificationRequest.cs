using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Request
{
    public sealed record AddNotificationRequest
    {
        [JsonPropertyName("notification_theme")]
        public NotificationTheme NotificationTheme { get; set; }

        [JsonPropertyName("notification_message")]
        public string NotificationMessage { get; set; }

        [JsonPropertyName("users")]
        public List<Guid> Users { get; set; }
    }
}
