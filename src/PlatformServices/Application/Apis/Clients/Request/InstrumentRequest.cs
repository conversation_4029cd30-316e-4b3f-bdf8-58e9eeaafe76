using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Request
{
    public sealed record InstrumentRequest
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("identifier")]
        public string Identifier { get; set; }

        [JsonPropertyName("type")]
        public InstrumentType Type { get; set; }
    }
}