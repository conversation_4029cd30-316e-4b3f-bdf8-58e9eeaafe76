using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.OccurrenceListReport
{
    public sealed record GetOccurrenceListReportByIdUser
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [Json<PERSON>ropertyName("username")]
        public string Username { get; set; }

        [Json<PERSON>ropertyName("first_name")]
        public string FirstName { get; set; }

        [Json<PERSON>ropertyName("surname")]
        public string Surname { get; set; }

        [JsonPropertyName("email")]
        public string Email { get; set; }
    }
}
