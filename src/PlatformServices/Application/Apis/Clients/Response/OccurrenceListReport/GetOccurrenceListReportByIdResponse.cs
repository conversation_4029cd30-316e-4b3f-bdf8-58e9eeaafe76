using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.OccurrenceListReport
{
    public sealed record GetOccurrenceListReportByIdResponse
    {
        public Guid Id { get; set; }

        [JsonPropertyName("clients")]
        public List<_Shared.ClientResponse> Clients { get; set; } = new();

        [JsonPropertyName("client_units")]
        public List<_Shared.ClientUnitResponse> ClientUnits { get; set; } = new();

        [JsonPropertyName("structures")]
        public List<_Shared.StructureResponse> Structures { get; set; } = new();

        [JsonPropertyName("created_by")]
        public GetOccurrenceListReportByIdUser CreatedBy { get; set; }

        [JsonPropertyName("start_date")]
        public DateTime? StartDate { get; set; }

        [JsonPropertyName("end_date")]
        public DateTime? EndDate { get; set; }

        [JsonPropertyName("status")]
        public OccurrenceStatus? Status { get; set; }

        [JsonPropertyName("inspection_sheet_type")]
        public InspectionSheetType? InspectionSheetType { get; set; }

        [JsonPropertyName("with_action_plan")]
        public bool? WithActionPlan { get; set; }

        [JsonPropertyName("date_filter")]
        public OccurrenceDateFilter? DateFilter { get; set; }

        [JsonPropertyName("days_remaining")]
        public int? DaysRemaining { get; set; }

        [JsonPropertyName("report_status")]
        public OccurrenceListReportStatus ReportStatus { get; set; }

        [JsonPropertyName("created_date")]
        public DateTime CreatedDate { get; set; }
    }
}
