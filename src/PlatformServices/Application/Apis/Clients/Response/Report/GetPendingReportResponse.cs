using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Report
{
    public sealed record GetPendingReportResponse
    {
        public Guid Id { get; init; }

        [JsonPropertyName("structure_id")]
        public Guid StructureId { get; init; }

        public string Title { get; init; }

        [JsonPropertyName("responsible_name")]
        public string ResponsibleName { get; set; }

        [JsonPropertyName("user_locale")]
        public Locale UserLocale { get; init; }

        [JsonPropertyName("destination_emails")]
        public IEnumerable<string> DestinationEmails { get; init; }

        [JsonPropertyName("subject_type")]
        public ReportSubjectType SubjectType { get; init; }

        [JsonPropertyName("periodicity_type")]
        public ReportPeriodicityType PeriodicityType { get; init; }

        [JsonPropertyName("days_to_analyze")]
        public int DaysToAnalyze { get; init; }

        [JsonPropertyName("next_emission_date")]
        public DateTime NextEmissionDate { get; init; }

        [JsonPropertyName("last_emission_date")]
        public DateTime LastEmissionDate { get; init; }

        [JsonPropertyName("cron_expression")]
        public string CronExpression { get; init; }

        [JsonPropertyName("Instant_report_parameters")]
        public GetInstantReportResponse InstantReportParameters { get; set; }

        [JsonPropertyName("daily_report_parameters")]
        public GetDailyReportResponse DailyReportParameters { get; set; }

        [JsonPropertyName("weekly_report_parameters")]
        public GetWeeklyReportResponse WeeklyReportParameters { get; set; }

        [JsonPropertyName("monthly_report_parameter")]
        public GetMonthlyReportResponse MonthlyReportParameter { get; set; }

        [JsonPropertyName("template")]
        public ReportTemplateType Template { get; set; }

        [JsonIgnore]
        public DateTime StartDate =>
            PeriodicityType switch
            {
                ReportPeriodicityType.Once => InstantReportParameters.StartDate,
                _ => DateTime.Today.AddDays(-DaysToAnalyze)
            };

        [JsonIgnore]
        public DateTime EndDate =>
            PeriodicityType switch
            {
                ReportPeriodicityType.Once => InstantReportParameters.EndDate,
                _ => DateTime.Today
            };

        public DateTime? GetNextEmissionDate(DateTime lastEmissionDate)
        {
            if (PeriodicityType == ReportPeriodicityType.Once)
            {
                return lastEmissionDate;
            }

            return Cronos.CronExpression
                .Parse(this.CronExpression)
                .GetNextOccurrence(lastEmissionDate);
        }
    }
}
