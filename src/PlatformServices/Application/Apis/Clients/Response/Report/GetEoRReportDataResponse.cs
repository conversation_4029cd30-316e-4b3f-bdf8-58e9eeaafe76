using Coordinate.Core.Enums;
using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Report
{
    public sealed class GetEoRReportDataResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("structure")]
        public Structure Structure { get; set; }

        [JsonPropertyName("client_unit")]
        public ClientUnit ClientUnit { get; set; }

        [JsonPropertyName("client")]
        public Client Client { get; set; }

        [JsonPropertyName("sections")]
        public List<Section> Sections { get; set; } = new();

        [JsonPropertyName("pluviometry_statistics")]
        public PluviometryStatistics PluviometryStatistics { get; set; }

        [JsonPropertyName("instrument_statistics")]
        public InstrumentStatistics InstrumentStatistics { get; set; }

        [JsonPropertyName("stability_statistics")]
        public dynamic StabilityStatistics { get; set; }
    }

    public sealed record Structure
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("city_name")]
        public string CityName { get; set; }

        [JsonPropertyName("utm_coordinates_zone_number")]
        public int UtmCoordinatesZoneNumber { get; set; }

        [JsonPropertyName("utm_coordinates_zone_letter")]
        public string UtmCoordinatesZoneLetter { get; set; }

        [JsonPropertyName("utm_coordinates_northing")]
        public double UtmCoordinatesNorthing { get; set; }

        [JsonPropertyName("utm_coordinates_easting")]
        public double UtmCoordinatesEasting { get; set; }

        [JsonPropertyName("construction_stages")]
        public int? ConstructionStages { get; set; }

        [JsonPropertyName("crest_width")]
        public double CrestWidth { get; set; }

        [JsonPropertyName("crest_length")]
        public double CrestLength { get; set; }

        [JsonPropertyName("crest_quota")]
        public decimal? CrestQuota { get; set; }

        [JsonPropertyName("total_height")]
        public double? TotalHeight { get; set; }

        [JsonPropertyName("construction_year")]
        public int? ConstructionYear { get; set; }

        [JsonPropertyName("section_type")]
        public string SectionType { get; set; }

        [JsonPropertyName("foundation_type")]
        public string FoundationType { get; set; }

        [JsonPropertyName("raising_method")]
        public string RaisingMethod { get; set; }

        [JsonPropertyName("expected_elevations")]
        public int? ExpectedElevations { get; set; }

        [JsonPropertyName("elevations_made")]
        public int? ElevationsMade { get; set; }

        [JsonPropertyName("upstream_slope")]
        public string UpstreamSlope { get; set; }

        [JsonPropertyName("downstream_slope")]
        public string DownstreamSlope { get; set; }

        [JsonPropertyName("internal_drainage")]
        public string InternalDrainage { get; set; }

        [JsonPropertyName("superficial_drainage")]
        public string SuperficialDrainage { get; set; }

        [JsonPropertyName("reservoir_design_volume")]
        public double? ReservoirDesignVolume { get; set; }

        [JsonPropertyName("current_reservoir_volume")]
        public double? CurrentReservoirVolume { get; set; }

        [JsonPropertyName("project_precipitation")]
        public double? ProjectPrecipitation { get; set; }

        [JsonPropertyName("full_of_project")]
        public int? FullOfProject { get; set; }

        [JsonPropertyName("project_flow")]
        public double? ProjectFlow { get; set; }

        [JsonPropertyName("maximum_influent_flow")]
        public double? MaximumInfluentFlow { get; set; }

        [JsonPropertyName("normal_maximum_water_level")]
        public double? NormalMaximumWaterLevel { get; set; }

        [JsonPropertyName("maximum_water_level_maximorum")]
        public double? MaximumWaterLevelMaximorum { get; set; }

        [JsonPropertyName("freeboard_normal_maximum_water_level")]
        public double? FreeboardNormalMaximumWaterLevel { get; set; }

        [JsonPropertyName("freeboard_maximum_water_level_maximorum")]
        public double? FreeboardMaximumWaterLevelMaximorum { get; set; }

        [JsonPropertyName("spillway")]
        public string Spillway { get; set; }

        [JsonPropertyName("purpose")]
        public string Purpose { get; set; }

        [JsonPropertyName("status")]
        public StructureStatus Status { get; set; }

        [JsonPropertyName("basin_area_in_square_kilometers")]
        public double? BasinAreaInSquareKilometers { get; set; }

        [JsonPropertyName("responsibles")]
        public List<Responsible> Responsibles { get; set; } = new();

        [JsonPropertyName("average_beach_length")]
        public decimal? AverageBeachLength { get; set; }

        [JsonPropertyName("datum")]
        public Datum Datum { get; set; }
    }

    public sealed record Responsible
    {
        [JsonPropertyName("email_address")]
        public string EmailAddress { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("position")]
        public string PositionName { get; set; }

        [JsonPropertyName("role")]
        public string RoleName { get; set; }

        [JsonPropertyName("professional_record")]
        public string ProfessionalRecord { get; set; }
    }

    public sealed record ClientUnit
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }
    }

    public sealed record Client
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("logo_unique_name")]
        public string LogoUniqueName { get; set; }

        [JsonPropertyName("logo_name")]
        public string LogoName { get; set; }
    }

    public sealed record Section
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("instruments")]
        public List<Instrument> Instruments { get; set; } = new();
    }

    public sealed record Instrument
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("type")]
        public InstrumentType Type { get; set; }

        [JsonPropertyName("subtype")]
        public InstrumentSubtype Subtype { get; set; }

        [JsonPropertyName("online")]
        public bool Online { get; set; }

        [JsonPropertyName("measurement_frequency")]
        public string MeasurementFrequency { get; set; }

        [JsonPropertyName("identifier")]
        public string Name { get; set; }

        [JsonPropertyName("top_quota")]
        public decimal? TopQuota { get; set; }

        [JsonPropertyName("depth")]
        public decimal? Depth { get; set; }

        [JsonPropertyName("base_quota")]
        public decimal? BaseQuota { get; set; }

        [JsonPropertyName("utm_coordinates_zone_number")]
        public int UtmCoordinatesZoneNumber { get; set; }

        [JsonPropertyName("utm_coordinates_zone_letter")]
        public string UtmCoordinatesZoneLetter { get; set; }

        [JsonPropertyName("utm_coordinates_northing")]
        public double UtmCoordinatesNorthing { get; set; }

        [JsonPropertyName("utm_coordinates_easting")]
        public double UtmCoordinatesEasting { get; set; }

        [JsonPropertyName("security_levels")]
        public List<SecurityLevel> SecurityLevels { get; set; } = new();

        [JsonPropertyName("measurements")]
        public List<Measurement> Measurements { get; set; } = new();

        [JsonPropertyName("statistic_data")]
        public dynamic StatisticData { get; set; }
    }

    public sealed record SecurityLevel
    {
        [JsonPropertyName("id")]
        public Guid? Id { get; set; }

        [JsonPropertyName("attention")]
        public decimal? Attention { get; set; }

        [JsonPropertyName("alert")]
        public decimal? Alert { get; set; }

        [JsonPropertyName("emergency")]
        public decimal? Emergency { get; set; }

        [JsonPropertyName("axis")]
        public Axis? Axis { get; set; }

        [JsonIgnore]
        public Guid InstrumentId { get; set; }
    }

    public sealed record Measurement
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("identifier")]
        public string Identifier { get; set; }

        [JsonPropertyName("quota")]
        public decimal? Quota { get; set; }

        [JsonPropertyName("lithotype")]
        public decimal? Lithotype { get; set; }

        [JsonPropertyName("limit")]
        public decimal? Limit { get; set; }
    }

    public sealed record Reading
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("is_referential")]
        public bool? IsReferential { get; set; }

        [JsonIgnore]
        public Guid InstrumentId { get; set; }

        [JsonPropertyName("values")]
        public List<ReadingValue> Values { get; set; } = new();
    }

    public sealed record ReadingValue
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonIgnore]
        public Guid ReadingId { get; set; }

        [JsonIgnore]
        public Guid MeasurementId { get; set; }

        [JsonPropertyName("security_level_type")]
        public SecurityLevelType SecurityLevelType { get; set; } = Domain.Enums.SecurityLevelType.None;

        [JsonPropertyName("date")]
        public DateTime Date { get; set; }

        [JsonPropertyName("quota")]
        public decimal? Quota { get; set; }

        [JsonPropertyName("relative_settlement")]
        public decimal? RelativeSettlement { get; set; }

        [JsonPropertyName("dry")]
        public bool? Dry { get; set; }

        [JsonPropertyName("pluviometry")]
        public decimal? Pluviometry { get; set; }

        [JsonPropertyName("a_displacement")]
        public decimal? ADisplacement { get; set; }

        [JsonPropertyName("b_displacement")]
        public decimal? BDisplacement { get; set; }

        [JsonPropertyName("planimetric_displacement")]
        public decimal? PlanimetricDisplacement { get; set; }

        [JsonPropertyName("z_displacement")]
        public decimal? ZDisplacement { get; set; }

        [JsonIgnore]
        public decimal? DeviationA { get; set; }

        [JsonIgnore]
        public decimal? DeviationB { get; set; }
    }

    public sealed record PercolationStatisticData
    {
        [JsonPropertyName("min_reading_value")]
        public ReadingValue MinReadingValue { get; set; } = new();

        [JsonPropertyName("max_reading_value")]
        public ReadingValue MaxReadingValue { get; set; } = new();

        [JsonPropertyName("last_reading_value")]
        public ReadingValue LastReadingValue { get; set; } = new();

        [JsonPropertyName("average")]
        public decimal Average { get; set; }

        [JsonPropertyName("median")]
        public decimal Median { get; set; }

        [JsonPropertyName("standard_deviation")]
        public decimal StandardDeviation { get; set; }

        [JsonPropertyName("absolute_variation")]
        public decimal AbsoluteVariation { get; set; }
    }

    public sealed record SettlementGaugeStatisticCollection
    {
        [JsonPropertyName("data")]
        public List<SettlementGaugeStatisticData> Data { get; set; } = new();
    }

    public sealed record SettlementGaugeStatisticData
    {
        [JsonPropertyName("measurement_name")]
        public string MeasurementName { get; set; }

        [JsonPropertyName("min_reading_value")]
        public ReadingValue MinReadingValue { get; set; }

        [JsonPropertyName("max_reading_value")]
        public ReadingValue MaxReadingValue { get; set; }

        [JsonPropertyName("last_reading_value")]
        public ReadingValue LastReadingValue { get; set; }

        [JsonPropertyName("average")]
        public decimal Average { get; set; }

        [JsonPropertyName("median")]
        public decimal Median { get; set; }

        [JsonPropertyName("standard_deviation")]
        public decimal StandardDeviation { get; set; }

        [JsonPropertyName("absolute_variation")]
        public decimal AbsoluteVariation { get; set; }
    }

    public sealed record SurfaceLandmarkStatisticCollection
    {
        [JsonPropertyName("a_axis")]
        public SurfaceLandmarkStatisticData AAxis { get; set; }

        [JsonPropertyName("b_axis")]
        public SurfaceLandmarkStatisticData BAxis { get; set; }

        [JsonPropertyName("planimetric")]
        public SurfaceLandmarkStatisticData Planimetric { get; set; }

        [JsonPropertyName("vertical")]
        public SurfaceLandmarkStatisticData Vertical { get; set; }
    }

    public sealed record SurfaceLandmarkStatisticData
    {
        [JsonPropertyName("max_positive_reading_value")]
        public ReadingValue MaxPositiveReadingValue { get; set; }

        [JsonPropertyName("max_negative_reading_value")]
        public ReadingValue MaxNegativeReadingValue { get; set; }

        [JsonPropertyName("positive_average")]
        public decimal? PositiveAverage { get; set; }

        [JsonPropertyName("negative_average")]
        public decimal? NegativeAverage { get; set; }

        [JsonPropertyName("absolute_average")]
        public decimal? AbsoluteAverage { get; set; }

        [JsonPropertyName("positive_median")]
        public decimal? PositiveMedian { get; set; }

        [JsonPropertyName("negative_median")]
        public decimal? NegativeMedian { get; set; }

        [JsonPropertyName("absolute_median")]
        public decimal? AbsoluteMedian { get; set; }

        [JsonPropertyName("positive_standard_deviation")]
        public decimal? PositiveStandardDeviation { get; set; }

        [JsonPropertyName("negative_standard_deviation")]
        public decimal? NegativeStandardDeviation { get; set; }

        [JsonPropertyName("absolute_standard_deviation")]
        public decimal? AbsoluteStandardDeviation { get; set; }

        [JsonPropertyName("amplitude")]
        public decimal? Amplitude { get; set; }
    }

    public sealed record PluviometryStatistics
    {
        [JsonPropertyName("data")]
        public IEnumerable<PluviometryData> Data { get; set; }

        [JsonPropertyName("last_year_relative_average")]
        public decimal? LastYearRelativeAverage { get; set; }

        [JsonPropertyName("all_time_relative_average")]
        public decimal? AllTimeRelativeAverage { get; set; }

        public decimal? ReportPeriodAccumulated => Data
            .FirstOrDefault(data => data.Type == PluviometryTimePeriod.ReportPeriod)?
            .TotalPluviometry;

        public decimal? AllTimeAverage => Data
            .FirstOrDefault(data => data.Type == PluviometryTimePeriod.AllTime)?
            .AveragePluviometry;
    }

    public sealed record PluviometryData
    {
        [JsonPropertyName("type")]
        public PluviometryTimePeriod Type { get; set; }

        [JsonPropertyName("average_pluviometry")]
        public decimal? AveragePluviometry { get; set; }

        [JsonPropertyName("total_pluviometry")]
        public decimal? TotalPluviometry { get; set; }

        [JsonPropertyName("start_year")]
        public int? StartYear { get; set; }

        [JsonPropertyName("end_year")]
        public int? EndYear { get; set; }
    }

    public sealed record InstrumentStatistics
    {
        [JsonPropertyName("number_of_piezometers")]
        public int NumberOfPiezometers { get; set; }

        [JsonPropertyName("number_of_piezometers_with_readings")]
        public int NumberOfPiezometersWithReadings { get; set; }

        [JsonPropertyName("number_of_water_level_indicators")]
        public int NumberOfWaterLevelIndicators { get; set; }

        [JsonPropertyName("number_of_water_level_indicators_with_readings")]
        public int NumberOfWaterLevelIndicatorsWithReadings { get; set; }

        [JsonPropertyName("number_of_surface_landmarks")]
        public int NumberOfSurfaceLandmarks { get; set; }

        [JsonPropertyName("number_of_surface_landmarks_with_readings")]
        public int NumberOfSurfaceLandmarksWithReadings { get; set; }

        [JsonPropertyName("number_of_settlement_gauges")]
        public int NumberOfSettlementGauges { get; set; }

        [JsonPropertyName("number_of_settlement_gauges_with_readings")]
        public int NumberOfSettlementGaugesWithReadings { get; set; }

        [JsonPropertyName("number_of_inclinometers")]
        public int NumberOfInclinometers { get; set; }

        [JsonPropertyName("number_of_inclinometers_with_readings")]
        public int NumberOfInclinometersWithReadings { get; set; }

        [JsonPropertyName("number_of_pluviometers")]
        public int NumberOfPluviometers { get; set; }

        [JsonPropertyName("number_of_pluviometers_with_readings")]
        public int NumberOfPluviometersWithReadings { get; set; }

        [JsonPropertyName("number_of_pluviographs")]
        public int NumberOfPluviographs { get; set; }

        [JsonPropertyName("number_of_pluviographs_with_readings")]
        public int NumberOfPluviographsWithReadings { get; set; }
    }

    public sealed record ValeStabilityData
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("section_id")]
        public Guid SectionId { get; set; }

        [JsonPropertyName("soil_condition_type")]
        public SoilConditionType SoilConditionType { get; set; }

        [JsonPropertyName("reading_created_date")]
        public DateTime ReadingCreatedDate { get; set; }

        [JsonPropertyName("fortnight")]
        public string Fortnight { get; set; }

        [JsonPropertyName("month")]
        public int Month { get; set; }

        [JsonPropertyName("value")]
        public decimal Value { get; set; }
    }

    public sealed record MosaicStabilityData
    {
        [JsonPropertyName("section_id")]
        public Guid SectionId { get; set; }

        [JsonPropertyName("sli_file_type")]
        public SliFileType SliFileType { get; set; }

        [JsonPropertyName("soil_condition_type")]
        public SoilConditionType SoilConditionType { get; set; }

        [JsonPropertyName("max_value")]
        public decimal MaxValue { get; set; }

        [JsonPropertyName("min_value")]
        public decimal MinValue { get; set; }

        [JsonPropertyName("average_value")]
        public decimal AverageValue { get; set; }

        [JsonPropertyName("last_safety_factor")]
        public MosaicLastSafetyFactor LastSafetyFactor { get; set; }
    }

    public sealed record MosaicLastSafetyFactor
    {
        [JsonPropertyName("section_id")]
        public Guid SectionId { get; set; }

        [JsonPropertyName("sli_file_type")]
        public SliFileType SliFileType { get; set; }

        [JsonPropertyName("soil_condition_type")]
        public SoilConditionType SoilConditionType { get; set; }

        [JsonPropertyName("reading_created_date")]
        public DateTime ReadingCreatedDate { get; set; }

        [JsonPropertyName("value")]
        public decimal Value { get; set; }

        [JsonPropertyName("has_alert")]
        public bool HasAlert { get; set; }
    }

    public enum PluviometryTimePeriod
    {
        ReportPeriod,
        CurrentYear,
        LastYear,
        AllTime
    }
}
