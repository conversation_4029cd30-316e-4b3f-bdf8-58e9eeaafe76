using Application.Apis.Clients.Response._Shared;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Section.GetById
{
    public sealed record GetSectionByIdResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("client")]
        public ClientResponse Client { get; set; }

        [JsonPropertyName("client_unit")]
        public ClientUnitResponse ClientUnit { get; set; }

        [JsonPropertyName("structure")]
        public StructureResponse Structure { get; set; }

        [JsonPropertyName("is_skew")]
        public bool IsSkew { get; set; }

        [JsonPropertyName("normal_line_azimuth")]
        public double NormalLineAzimuth { get; set; }

        [JsonPropertyName("skew_line_azimuth")]
        public double? SkewLineAzimuth { get; set; }

        [JsonPropertyName("coordinates")]
        public SectionCoordinates Coordinates { get; set; }

        [JsonPropertyName("map_line_setting")]
        public MapLineSetting MapLineSetting { get; set; }

        [JsonPropertyName("instruments")]
        public List<InstrumentInfo> Instruments { get; set; }

        [JsonPropertyName("reviews")]
        public List<SectionReview> Reviews { get; set; }

        [JsonPropertyName("minimum_drained_depth")]
        public double? MinimumDrainedDepth { get; set; }

        [JsonPropertyName("minimum_undrained_depth")]
        public double? MinimumUndrainedDepth { get; set; }

        [JsonPropertyName("minimum_pseudo_static_depth")]
        public double? MinimumPseudoStaticDepth { get; set; }
    }
}
