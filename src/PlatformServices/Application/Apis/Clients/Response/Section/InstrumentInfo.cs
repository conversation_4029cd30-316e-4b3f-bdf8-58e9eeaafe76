using Coordinate.Core.Classes;
using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Section
{
    public sealed record InstrumentInfo
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("identifier")]
        public string Identifier { get; set; }

        [JsonPropertyName("type")]
        public InstrumentType Type { get; set; }

        [JsonPropertyName("decimal_geodetic_coordinate")]
        public DecimalGeodetic DecimalGeodetic { get; set; }

        [JsonPropertyName("online")]
        public bool Online { get; set; }
    }
}
