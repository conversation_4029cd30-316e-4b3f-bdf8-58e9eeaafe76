using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Section
{
    public sealed record ConstructionStage
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("drawing")]
        public Apis._Shared.File Drawing { get; set; }

        [JsonPropertyName("sli")]
        public Apis._Shared.File Sli { get; set; }

        [JsonPropertyName("description")]
        public string Description { get; set; }

        [JsonPropertyName("stage")]
        public string Stage { get; set; }

        [JsonPropertyName("is_current_stage")]
        public bool IsCurrentStage { get; set; }

        [Json<PERSON>ropertyName("dxf_has_waterline")]
        public bool DxfHasWaterline { get; set; }
    }
}
