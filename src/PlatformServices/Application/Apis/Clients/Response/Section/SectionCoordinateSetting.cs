using Coordinate.Core.Classes;
using Coordinate.Core.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Section
{
    public sealed record SectionCoordinateSetting
    {
        [JsonPropertyName("coordinate_format")]
        public Format CoordinateFormat { get; set; }

        [JsonPropertyName("coordinate_systems")]
        public Systems CoordinateSystems { get; set; }
    }
}
