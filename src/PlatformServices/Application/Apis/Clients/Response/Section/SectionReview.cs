using Application.Apis.Clients.Response._Shared;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Section
{
    public sealed record SectionReview
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("start_date")]
        public DateTime StartDate { get; set; }

        [JsonPropertyName("structure_type")]
        public GetStructureTypeByIdResponse StructureType { get; set; }

        [JsonPropertyName("drawing")]
        public Apis._Shared.File Drawing { get; set; }

        [JsonPropertyName("sli")]
        public Apis._Shared.File Sli { get; set; }

        [JsonPropertyName("construction_stages")]
        public List<ConstructionStage> ConstructionStages { get; set; }

        [JsonPropertyName("dxf_has_waterline")]
        public bool DxfHasWaterline { get; set; }
    }
}
