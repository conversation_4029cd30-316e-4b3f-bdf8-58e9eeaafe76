using Application.Apis.Clients.Response._Shared;
using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Simulation.GetById
{
    public sealed record GetSimulationByIdResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; init; }

        [JsonPropertyName("status")]
        public SimulationStatus Status { get; init; }

        [JsonPropertyName("sections")]
        public List<GetSimulationByIdSection> Sections { get; init; }

        [JsonPropertyName("slide2_configuration")]
        public Slide2Configuration Slide2Configuration { get; init; }

        [JsonPropertyName("should_evaluate_drained_condition")]
        public bool ShouldEvaluateDrainedCondition { get; init; }

        [JsonPropertyName("should_evaluate_undrained_condition")]
        public bool ShouldEvaluateUndrainedCondition { get; init; }

        [JsonPropertyName("should_evaluate_pseudo_static_condition")]
        public bool ShouldEvaluatePseudoStaticCondition { get; init; }

        [JsonPropertyName("safety_factor_target")]
        public double? SafetyFactorTarget { get; init; }

        [JsonPropertyName("seismic_coefficient")]
        public Orientation SeismicCoefficient { get; init; }

        [JsonPropertyName("water_table_configuration")]
        public WaterTableConfiguration WaterTableConfiguration { get; init; }

        [JsonPropertyName("reading_statistical_measure")]
        public ReadingStatisticalMeasure? ReadingStatisticalMeasure { get; init; }

        [JsonPropertyName("water_table_variation")]
        public decimal? WaterTableVariation { get; init; }

        [JsonPropertyName("start_date")]
        public DateTime? StartDate { get; init; }

        [JsonPropertyName("end_date")]
        public DateTime? EndDate { get; init; }

        [JsonPropertyName("upstream_linimetric_ruler_statistical_measure")]
        public ReadingStatisticalMeasure? UpstreamLinimetricRulerStatisticalMeasure { get; init; }

        [JsonPropertyName("upstream_linimetric_ruler_id")]
        public Guid? UpstreamLinimetricRulerId { get; set; }

        [JsonPropertyName("upstream_linimetric_ruler_quota")]
        public decimal? UpstreamLinimetricRulerQuota { get; set; }

        [JsonPropertyName("downstream_linimetric_ruler_statistical_measure")]
        public ReadingStatisticalMeasure? DownstreamLinimetricRulerStatisticalMeasure { get; init; }

        [JsonPropertyName("downstream_linimetric_ruler_id")]
        public Guid? DownstreamLinimetricRulerId { get; set; }

        [JsonPropertyName("downstream_linimetric_ruler_quota")]
        public decimal? DownstreamLinimetricRulerQuota { get; set; }

        [JsonPropertyName("ignore_damaged_instruments")]
        public bool IgnoreDamagedInstruments { get; init; }

        [JsonPropertyName("need_to_do_statistical_calculations")]
        public bool NeedToDoStatisticalCalculations { get; set; }

        [JsonPropertyName("created_by")]
        public UserResponse CreatedBy { get; init; }
    }
}
