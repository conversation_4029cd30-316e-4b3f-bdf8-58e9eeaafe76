using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Simulation.GetById
{
    public sealed record GetSimulationByIdInstrument
    {
        [JsonPropertyName("id")]
        public Guid Id { get; init; }

        [JsonPropertyName("instrument_id")]
        public Guid InstrumentId { get; init; }

        [JsonPropertyName("instrument_identifier")]
        public string InstrumentIdentifier { get; init; }

        [Json<PERSON>ropertyName("instrument_type")]
        public InstrumentType InstrumentType { get; init; }

        [JsonPropertyName("instrument_dry_type")]
        public DryType? InstrumentDryType { get; init; }

        [JsonPropertyName("measurement_id")]
        public Guid? MeasurementId { get; init; }

        [JsonPropertyName("measurement_identifier")]
        public string MeasurementIdentifier { get; init; }

        [JsonPropertyName("quota")]
        public decimal? Quota { get; set; }

        [Json<PERSON>ropertyName("dry")]
        public bool Dry { get; init; }
    }
}
