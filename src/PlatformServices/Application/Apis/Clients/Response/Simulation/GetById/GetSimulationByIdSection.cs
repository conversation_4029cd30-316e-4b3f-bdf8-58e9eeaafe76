using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Simulation.GetById
{
    public sealed record GetSimulationByIdSection
    {
        [JsonPropertyName("id")]
        public Guid Id { get; init; }

        [JsonPropertyName("section_id")]
        public Guid SectionId { get; init; }

        [JsonPropertyName("section_name")]
        public string SectionName { get; init; }

        [JsonPropertyName("section_review_id")]
        public Guid? SectionReviewId { get; init; }

        [JsonPropertyName("section_review_index")]
        public int? SectionReviewIndex { get; init; }

        [JsonPropertyName("section_review_start_date")]
        public DateTime? SectionReviewStartDate { get; init; }

        [JsonPropertyName("construction_stage_id")]
        public Guid? ConstructionStageId { get; init; }

        [JsonPropertyName("construction_stage")]
        public string ConstructionStage { get; init; }

        [JsonPropertyName("instruments")]
        public List<GetSimulationByIdInstrument> Instruments { get; set; } = new();

        [JsonPropertyName("minimum_drained_depth")]
        public double? MinimumDrainedDepth { get; init; }

        [JsonPropertyName("minimum_undrained_depth")]
        public double? MinimumUndrainedDepth { get; init; }

        [JsonPropertyName("minimum_pseudo_static_depth")]
        public double? MinimumPseudoStaticDepth { get; init; }

        [JsonPropertyName("beach_length_statistical_measure")]
        public ReadingStatisticalMeasure? BeachLengthStatisticalMeasure { get; init; }

        [JsonPropertyName("beach_length")]
        public double? BeachLength { get; set; }

        [JsonPropertyName("ignored_instruments")]
        public string IgnoredInstruments { get; set; }

        [JsonPropertyName("results")]
        public List<GetSimulationByIdResult> Results { get; init; }

    }
}
