using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Simulation.GetById
{
    public sealed record GetSimulationByIdResult
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("calculation_method")]
        public CalculationMethod CalculationMethod { get; init; }

        [JsonPropertyName("sli_file_type")]
        public SliFileType SliFileType { get; init; }

        [JsonPropertyName("value")]
        public double Value { get; init; }

        [JsonPropertyName("dxf_file")]
        public Application.Apis._Shared.File DxfFile { get; init; }

        [JsonPropertyName("png_file")]
        public Application.Apis._Shared.File PngFile { get; init; }

        [JsonPropertyName("zip_file_download_url")]
        public string ZipFileDownloadUrl { get; init; }
    }
}
