using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Simulation.List
{
    public sealed record ListSimulationResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; init; }

        [JsonPropertyName("name")]
        public string Name { get; init; }

        [JsonPropertyName("created_date")]
        public DateTime CreatedDate { get; init; }

        [JsonPropertyName("should_keep")]
        public bool ShouldKeep { get; init; }
    }
}
