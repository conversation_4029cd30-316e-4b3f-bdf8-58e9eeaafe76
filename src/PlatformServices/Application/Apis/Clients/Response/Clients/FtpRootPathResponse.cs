using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Clients
{
    public sealed record FtpRootPathResponse
    {
        [JsonPropertyName("root")]
        public string Root { get; set; }

        [JsonPropertyName("subfolders")]
        public List<string> Subfolders { get; set; }

        public List<string> GetSourcePaths()
        {
            var sourcePaths = new List<string>()
            {
                Root
            };

            if (Subfolders != null && Subfolders.Any())
            {
                sourcePaths.AddRange(
                    Subfolders.Select(folder => $"{Root}/{folder}"));
            }

            return sourcePaths;
        }
    }
}
