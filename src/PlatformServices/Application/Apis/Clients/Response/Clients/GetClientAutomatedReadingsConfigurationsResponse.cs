using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Clients
{
    public sealed record GetClientAutomatedReadingsConfigurationsResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("created_date")]
        public DateTime CreatedDate { get; set; }

        [JsonPropertyName("client_id")]
        public Guid ClientId { get; set; }

        [JsonPropertyName("client_name")]
        public string ClientName { get; set; }

        [JsonPropertyName("template_type")]
        public AutomatedReadingTemplateType TemplateType { get; set; }

        [JsonPropertyName("active")]
        public bool Active { get; set; }

        [JsonPropertyName("ftp_root_path")]
        public FtpRootPathResponse FtpRootPath { get; set; }

        [JsonPropertyName("created_by")]
        public Guid CreatedBy { get; set; }
    }
}