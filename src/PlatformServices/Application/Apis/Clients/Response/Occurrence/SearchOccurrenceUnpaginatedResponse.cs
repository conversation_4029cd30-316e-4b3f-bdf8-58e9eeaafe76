using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Occurrence
{
    public sealed record SearchOccurrenceUnpaginatedResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("search_identifier")]
        public int SearchIdentifier { get; set; }

        [JsonPropertyName("inspection_sheet_id")]
        public Guid InspectionSheetId { get; set; }

        [JsonPropertyName("inspection_sheet_search_identifier")]
        public int InspectionSheetSearchIdentifier { get; set; }

        [JsonPropertyName("inspection_sheet_type")]
        public InspectionSheetType InspectionSheetType { get; set; }

        [JsonPropertyName("structure_id")]
        public Guid StructureId { get; set; }

        [JsonPropertyName("structure_name")]
        public string StructureName { get; set; }

        [JsonPropertyName("aspect")]
        public _Shared.AspectResponse Aspect { get; set; }

        [JsonPropertyName("response")]
        public OccurrenceResponse? Response { get; set; }

        [JsonPropertyName("occurrence_and_action_plan_status")]
        public OccurrenceActionPlanStatus OccurrenceAndActionPlanStatus { get; set; }

        [JsonPropertyName("action_plan_id")]
        public Guid? ActionPlanId { get; set; }

        [JsonPropertyName("latitude_sirgas2000")]
        public double? LatitudeSirgas2000 { get; set; }

        [JsonPropertyName("longitude_sirgas2000")]
        public double? LongitudeSirgas2000 { get; set; }

        [JsonPropertyName("easting")]
        public double? Easting { get; set; }

        [JsonPropertyName("northing")]
        public double? Northing { get; set; }

        [JsonPropertyName("created_date")]
        public DateTime CreatedDate { get; set; }

        [JsonPropertyName("action_plan_expiration_date")]
        public DateTime? ActionPlanExpirationDate { get; set; }

        [JsonPropertyName("action_plan_completion_date")]
        public DateTime? ActionPlanCompletionDate { get; set; }

        [JsonPropertyName("client_id")]
        public Guid ClientId { get; set; }
    }
}
