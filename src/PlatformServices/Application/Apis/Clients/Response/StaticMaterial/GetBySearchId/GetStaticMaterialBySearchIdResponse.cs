using Application.Apis.Clients.Response._Shared;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.StaticMaterial.GetBySearchId
{
    public sealed record GetStaticMaterialBySearchIdResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("search_identifier")]
        public int SearchIdentifier { get; set; }

        [JsonPropertyName("client")]
        public ClientResponse Client { get; set; }

        [JsonPropertyName("client_unit")]
        public ClientUnitResponse ClientUnit { get; set; }

        [JsonPropertyName("structure")]
        public StructureResponse Structure { get; set; }

        [JsonPropertyName("created_by")]
        public UserResponse CreatedBy { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("active")]
        public bool Active { get; set; }

        [JsonPropertyName("drained_static_material_value")]
        public GetStaticMaterialValueBySearchId DrainedStaticMaterialValue { get; set; }

        [JsonPropertyName("undrained_static_material_value")]
        public GetStaticMaterialValueBySearchId UndrainedStaticMaterialValue { get; set; }

        [JsonPropertyName("pseudo_static_static_material_value")]
        public GetStaticMaterialValueBySearchId PseudoStaticStaticMaterialValue { get; set; }
    }
}
