using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.StaticMaterial.GetBySearchId
{
    public sealed record StaticMaterialValueUseResistance
    {
        [JsonPropertyName("id")]
        public Guid? Id { get; set; }

        [JsonPropertyName("color")]
        public string Color { get; set; }

        [JsonPropertyName("natural_specific_weight")]
        public double NaturalSpecificWeight { get; set; }

        [JsonPropertyName("saturated_specific_weight")]
        public double? SaturatedSpecificWeight { get; set; }

        [JsonPropertyName("constitutive_model")]
        public ConstitutiveModel ConstitutiveModel { get; set; }

        [JsonPropertyName("cohesion")]
        public double? Cohesion { get; set; }

        [JsonPropertyName("friction_angle")]
        public double? FrictionAngle { get; set; }

        [JsonPropertyName("tensile_strength")]
        public double? TensileStrength { get; set; }

        [JsonPropertyName("cohesion_type")]
        public CohesionType? CohesionType { get; set; }

        [JsonPropertyName("cohesion_variation")]
        public double? CohesionVariation { get; set; }

        [JsonPropertyName("maximum")]
        public double? Maximum { get; set; }

        [JsonPropertyName("datum")]
        public double? Datum { get; set; }

        [JsonPropertyName("allow_sliding_along_boundary")]
        public bool? AllowSlidingAlongBoundary { get; set; }

        [JsonPropertyName("ucs_intact")]
        public double? UcsIntact { get; set; }

        [JsonPropertyName("m")]
        public double? M { get; set; }

        [JsonPropertyName("s")]
        public double? S { get; set; }

        [JsonPropertyName("strength_definition")]
        public StrengthDefinition? StrengthDefinition { get; set; }

        [JsonPropertyName("gsi")]
        public double? Gsi { get; set; }

        [JsonPropertyName("mi")]
        public double? Mi { get; set; }

        [JsonPropertyName("d")]
        public double? D { get; set; }

        [JsonPropertyName("mb")]
        public double? Mb { get; set; }

        [JsonPropertyName("a")]
        public double? A { get; set; }

        [JsonPropertyName("resistance_ratio")]
        public double? ResistanceRatio { get; set; }

        [JsonPropertyName("maximum_shear_strength")]
        public double? MaximumShearStrength { get; set; }

        [JsonPropertyName("minimum_shear_strength")]
        public double? MinimumShearStrength { get; set; }

        [JsonPropertyName("stress_history_type")]
        public StressHistoryType? StressHistoryType { get; set; }

        [JsonPropertyName("stress_history_method")]
        public StressHistoryMethod? StressHistoryMethod { get; set; }

        [JsonPropertyName("constant")]
        public double? Constant { get; set; }

        [JsonPropertyName("hu")]
        public Hu Hu { get; set; }

        [JsonPropertyName("custom_hu_value")]
        public double? CustomHuValue { get; set; }

        [JsonPropertyName("water_surface")]
        public WaterSurface WaterSurface { get; set; }

        [JsonPropertyName("point_values")]
        public List<PointValue> PointValues { get; set; }
    }
}
