using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.StaticMaterial.GetBySearchId
{
    public sealed record class PointValue
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        // Can be Depth(m), Elevation(m) or Effective Normal Stress 
        // what the value will represent depends on the Constitutive Model and Stress History Method
        [JsonPropertyName("value_1")]
        public double Value1 { get; set; }

        // Can be OCR, PC(KPA) or Shear Stress
        // what the value will represent depends on the Constitutive Model and Stress History Method
        [JsonPropertyName("value_2")]
        public double Value2 { get; set; }

        [JsonPropertyName("index")]
        public int Index { get; set; }
    }
}
