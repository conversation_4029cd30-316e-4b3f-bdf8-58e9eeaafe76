using Application.Apis.Clients.Response._Shared;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.StabilityAnalysis.GetByIdWithSafetyFactorsMetrics
{
    public sealed record GetStabilityAnalysisByIdWithSafetyFactorsMetricsResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; init; }

        [JsonPropertyName("reading_created_date")]
        public DateTime ReadingCreatedDate { get; init; }

        [JsonPropertyName("structure_id")]
        public Guid StructureId { get; init; }

        [JsonPropertyName("structure_name")]
        public string StructureName { get; init; }

        [JsonPropertyName("section_name")]
        public string SectionName { get; init; }

        [JsonPropertyName("calculated_by")]
        public UserResponse CalculatedBy { get; set; }

        [JsonIgnore]
        public string CalculatedByFullName => !string.IsNullOrWhiteSpace(CalculatedBy?.FirstName)
            ? $"{CalculatedBy.FirstName} {CalculatedBy.Surname}"
            : null;

        [JsonPropertyName("safety_factor_results")]
        public IEnumerable<SafetyFactorValueResponse> SafetyFactorValueResults { get; set; }

        [JsonPropertyName("safety_factor_metric_results")]
        public IEnumerable<SafetyFactorMetricResponse> SafetyFactorMetricResults { get; set; }

        [JsonPropertyName("warnings")]
        public IEnumerable<string> Warnings { get; set; }
    }
}
