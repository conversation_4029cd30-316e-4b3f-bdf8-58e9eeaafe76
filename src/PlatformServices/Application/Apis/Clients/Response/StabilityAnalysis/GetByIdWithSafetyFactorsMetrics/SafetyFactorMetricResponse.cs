using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.StabilityAnalysis.GetByIdWithSafetyFactorsMetrics
{
    public sealed record SafetyFactorMetricResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; init; }

        [JsonPropertyName("soil_condition_type")]
        public SoilConditionType SoilConditionType { get; init; }

        [JsonPropertyName("reference_value")]
        public double ReferenceValue { get; init; }

        [JsonPropertyName("alert_level")]
        public SafetyFactorAlertLevel AlertLevel { get; init; }

        [JsonPropertyName("standard_identifier")]
        public string StandardIdentifier { get; init; }
    }
}
