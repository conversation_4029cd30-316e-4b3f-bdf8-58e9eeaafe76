using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.StabilityAnalysis.GetByIdWithSafetyFactorsMetrics
{
    public sealed record SafetyFactorValueResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; init; }

        [JsonPropertyName("sli_file_type")]
        public SliFileType SliFileType { get; init; }

        [JsonPropertyName("value")]
        public double Value { get; init; }
    }
}
