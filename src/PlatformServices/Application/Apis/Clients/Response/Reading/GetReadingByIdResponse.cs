using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Reading
{
    public sealed record GetReadingByIdResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("instrument")]
        public InstrumentResponse Instrument { get; set; }

        [JsonPropertyName("instrument_top_quota")]
        public decimal? InstrumentTopQuota { get; set; }

        [JsonPropertyName("instrument_base_quota")]
        public decimal? InstrumentBaseQuota { get; set; }

        [JsonPropertyName("instrument_azimuth")]
        public double? InstrumentAzimuth { get; set; }

        [JsonPropertyName("instrument_measurement_frequency")]
        public string InstrumentMeasurementFrequency { get; set; }

        [JsonPropertyName("is_referential")]
        public bool? IsReferential { get; set; }

        [JsonPropertyName("values")]
        public List<ReadingValueResponse> Values { get; set; }
    }
}
