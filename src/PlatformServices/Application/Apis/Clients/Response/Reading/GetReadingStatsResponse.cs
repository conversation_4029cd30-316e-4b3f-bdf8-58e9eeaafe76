using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Reading
{
    public sealed record GetReadingStatsResponse
    {
        [JsonPropertyName("instrument_id")]
        public Guid InstrumentId { get; init; }

        [JsonPropertyName("measurement_id")]
        public Guid? MeasurementId { get; init; }

        [JsonPropertyName("quota")]
        public decimal Quota { get; init; }
    }
}
