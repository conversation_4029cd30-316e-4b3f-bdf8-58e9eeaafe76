using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Reading
{
    public sealed record InstrumentResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("identifier")]
        public string Identifier { get; set; }

        [JsonPropertyName("type")]
        public InstrumentType Type { get; set; }
    }
}
