using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Instrument.GetAutomatedReadingsData
{
    public class InstrumentSimpleResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [Json<PERSON>ropertyName("identifier")]
        public string Identifier { get; set; }

        [Json<PERSON>ropertyName("alternative_name")]
        public string AlternativeName { get; set; }

        [JsonPropertyName("type")]
        public InstrumentType Type { get; set; }

        [JsonPropertyName("top_quota")]
        public decimal? TopQuota { get; set; }

        [JsonPropertyName("base_quota")]
        public decimal? BaseQuota { get; set; }

        [JsonPropertyName("azimuth")]
        public double? Azimuth { get; set; }

        [JsonPropertyName("client_unit_name")]
        public string ClientUnitName { get; set; }

        [JsonPropertyName("structure_name")]
        public string StructureName { get; set; }
    }
}