using Application.Apis.Clients.Response._Shared;
using Application.Apis.Clients.Response.Reading;
using Coordinate.Core.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Instrument
{
    public sealed record GetInstrumentReadingValuesResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("measurement")]
        public MeasurementInfoResponse Measurement { get; set; }

        [JsonPropertyName("security_levels")]
        public List<SecurityLevels> SecurityLevels { get; set; }

        [JsonPropertyName("instrument_base_quota")]
        public decimal? InstrumentBaseQuota { get; set; }

        [JsonPropertyName("instrument_top_quota")]
        public decimal? InstrumentTopQuota { get; set; }

        [JsonPropertyName("measurement_quota")]
        public decimal? MeasurementQuota { get; set; }

        [JsonPropertyName("measurement_delta_ref")]
        public decimal? MeasurementDeltaRef { get; set; }

        [JsonPropertyName("measurement_length")]
        public decimal? MeasurementLength { get; set; }

        [JsonPropertyName("measurement_is_referential")]
        public bool? MeasurementIsReferential { get; set; }

        [JsonPropertyName("date")]
        public DateTime Date { get; set; }

        [JsonPropertyName("quota")]
        public decimal? Quota { get; set; }

        [JsonPropertyName("depth")]
        public decimal? Depth { get; set; }

        [JsonPropertyName("pressure")]
        public decimal? Pressure { get; set; }

        [JsonPropertyName("dry")]
        public bool? Dry { get; set; }

        [JsonPropertyName("positive_a")]
        public decimal? PositiveA { get; set; }

        [JsonPropertyName("negative_a")]
        public decimal? NegativeA { get; set; }

        [JsonPropertyName("positive_b")]
        public decimal? PositiveB { get; set; }

        [JsonPropertyName("negative_b")]
        public decimal? NegativeB { get; set; }

        [JsonPropertyName("average_displacement_a")]
        public decimal? AverageDisplacementA { get; set; }

        [JsonPropertyName("average_displacement_b")]
        public decimal? AverageDisplacementB { get; set; }

        [JsonPropertyName("accumulated_displacement_a")]
        public decimal? AccumulatedDisplacementA { get; set; }

        [JsonPropertyName("accumulated_displacement_b")]
        public decimal? AccumulatedDisplacementB { get; set; }

        [JsonPropertyName("deviation_a")]
        public decimal? DeviationA { get; set; }

        [JsonPropertyName("deviation_b")]
        public decimal? DeviationB { get; set; }

        [JsonPropertyName("a_axis_reading")]
        public decimal? AAxisReading { get; set; }

        [JsonPropertyName("b_axis_reading")]
        public decimal? BAxisReading { get; set; }

        [JsonPropertyName("datum")]
        public Datum? Datum { get; set; }

        [JsonPropertyName("east_coordinate")]
        public decimal? EastCoordinate { get; set; }

        [JsonPropertyName("north_coordinate")]
        public decimal? NorthCoordinate { get; set; }

        [JsonPropertyName("east_displacement")]
        public decimal? EastDisplacement { get; set; }

        [JsonPropertyName("north_displacement")]
        public decimal? NorthDisplacement { get; set; }

        [JsonPropertyName("z_displacement")]
        public decimal? ZDisplacement { get; set; }

        [JsonPropertyName("total_planimetric_displacement")]
        public decimal? TotalPlanimetricDisplacement { get; set; }

        [JsonPropertyName("a_displacement")]
        public decimal? ADisplacement { get; set; }

        [JsonPropertyName("b_displacement")]
        public decimal? BDisplacement { get; set; }

        [JsonPropertyName("relative_depth")]
        public decimal? RelativeDepth { get; set; }

        [JsonPropertyName("delta_ref")]
        public decimal? DeltaRef { get; set; }

        [JsonPropertyName("absolute_settlement")]
        public decimal? AbsoluteSettlement { get; set; }

        [JsonPropertyName("relative_settlement")]
        public decimal? RelativeSettlement { get; set; }

        [JsonPropertyName("nature")]
        public NatureResponse Nature { get; set; }

        [JsonPropertyName("a_axis_pga")]
        public decimal? AAxisPga { get; set; }

        [JsonPropertyName("b_axis_pga")]
        public decimal? BAxisPga { get; set; }

        [JsonPropertyName("z_axis_pga")]
        public decimal? ZAxisPga { get; set; }
    }
}
