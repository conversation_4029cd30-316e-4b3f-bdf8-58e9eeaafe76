using Application.Apis.Clients.Response._Shared;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Instrument
{
    public sealed record MeasurementResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("active")]
        public bool Active { get; set; }

        [JsonPropertyName("identifier")]
        public string Identifier { get; set; }

        [JsonPropertyName("alternative_name")]
        public string AlternativeName { get; set; }

        [JsonPropertyName("quota")]
        public decimal Quota { get; set; }

        [JsonPropertyName("lithotype")]
        public string Lithotype { get; set; }

        [JsonPropertyName("limit")]
        public decimal? Limit { get; set; }

        [JsonPropertyName("delta_ref")]
        public decimal? DeltaRef { get; set; }

        [JsonPropertyName("is_referencial")]
        public bool? IsReferential { get; set; }

        [JsonPropertyName("elevation")]
        public decimal? Elevation { get; set; }

        [JsonPropertyName("length")]
        public decimal? Length { get; set; }

        [JsonPropertyName("security_levels")]
        public SecurityLevels SecurityLevels { get; set; }
    }
}
