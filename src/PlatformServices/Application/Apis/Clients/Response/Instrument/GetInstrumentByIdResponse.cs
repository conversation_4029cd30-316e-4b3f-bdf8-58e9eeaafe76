using Application.Apis.Clients.Response._Shared;
using Coordinate.Core.Classes;
using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Instrument
{
    public sealed record GetInstrumentByIdResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("search_identifier")]
        public int SearchIdentifier { get; set; }

        [JsonPropertyName("type")]
        public InstrumentType Type { get; set; }

        [JsonPropertyName("client")]
        public ClientResponse Client { get; set; }

        [JsonPropertyName("client_unit")]
        public ClientUnitResponse ClientUnit { get; set; }

        [JsonPropertyName("structure")]
        public StructureResponse Structure { get; set; }

        [JsonPropertyName("identifier")]
        public string Identifier { get; set; }

        [JsonPropertyName("alternative_name")]
        public string AlternativeName { get; set; }

        [JsonPropertyName("coordinate_setting")]
        public CoordinateSetting CoordinateSetting { get; set; }

        [Json<PERSON>ropertyName("top_quota")]
        public decimal? TopQuota { get; set; }

        [JsonPropertyName("depth")]
        public decimal? Depth { get; set; }

        [JsonPropertyName("base_quota")]
        public decimal? BaseQuota { get; set; }

        [JsonPropertyName("azimuth")]
        public double? Azimuth { get; set; }

        [JsonPropertyName("automated")]
        public bool Automated { get; set; }

        [JsonPropertyName("online")]
        public bool Online { get; set; }

        [JsonPropertyName("geophone_type")]
        public GeophoneType? GeophoneType { get; set; }

        [JsonPropertyName("linimetric_ruler_position")]
        public LinimetricRulerPosition? LinimetricRulerPosition { get; set; }

        [JsonPropertyName("elevation")]
        public decimal? Elevation { get; set; }

        [JsonPropertyName("responsible_for_installation")]
        public string ResponsibleForInstallation { get; set; }

        [JsonPropertyName("installation_date")]
        public DateTime InstallationDate { get; set; }

        [JsonPropertyName("model")]
        public string Model { get; set; }

        [JsonPropertyName("dry_type")]
        public DryType? DryType { get; set; }

        [JsonPropertyName("security_levels")]
        public List<SecurityLevels> SecurityLevels { get; set; }

        [JsonPropertyName("measurements")]
        public List<MeasurementResponse> Measurements { get; set; }
    }
}
