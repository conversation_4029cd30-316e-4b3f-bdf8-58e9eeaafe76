using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Structure.GetById
{
    public sealed record Responsible
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("email_address")]
        public string EmailAddress { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [Json<PERSON>ropertyName("position")]
        public Position Position { get; set; }

        [Json<PERSON>ropertyName("role")]
        public ResponsibleRole Role { get; set; }

        [JsonPropertyName("professional_record")]
        public string ProfessionalRecord { get; set; }
    }
}
