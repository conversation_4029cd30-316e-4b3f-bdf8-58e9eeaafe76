using Application.Apis.Clients.Response._Shared;
using Coordinate.Core.Classes;
using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Structure.GetById
{
    public sealed record GetStructureByIdResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("client_unit")]
        public ClientUnitResponse ClientUnit { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("status")]
        public StructureStatus Status { get; set; }

        [JsonPropertyName("protocol")]
        public Protocol? Protocol { get; set; }

        [JsonPropertyName("coordinate_setting")]
        public CoordinateSetting CoordinateSetting { get; set; }

        [JsonPropertyName("structure_type")]
        public GetStructureTypeByIdResponse StructureType { get; set; }

        [JsonPropertyName("should_evaluate_drained_condition")]
        public bool ShouldEvaluateDrainedCondition { get; set; }

        [JsonPropertyName("should_evaluate_undrained_condition")]
        public bool ShouldEvaluateUndrainedCondition { get; set; }

        [JsonPropertyName("should_evaluate_pseudo_static_condition")]
        public bool ShouldEvaluatePseudoStaticCondition { get; set; }

        [JsonPropertyName("map_configuration")]
        public MapConfiguration MapConfiguration { get; set; }

        [JsonPropertyName("slide2_configuration")]
        public Slide2Configuration Slide2Configuration { get; set; }

        [JsonPropertyName("has_auto_update")]
        public bool HasAutoUpdate { get; set; }

        [JsonPropertyName("frequency_to_fetch_data")]
        public AutomationFrequency FrequencyToFetchData { get; set; }

        [JsonPropertyName("frequency_to_generate_packages")]
        public AutomationFrequency FrequencyToGeneratePackages { get; set; }

        [JsonPropertyName("seismic_coefficient")]
        public Orientation SeismicCoefficient { get; set; }

        [JsonPropertyName("gravity")]
        public double Gravity { get; set; }

        [JsonPropertyName("country")]
        public Region Country { get; set; }

        [JsonPropertyName("state")]
        public Region State { get; set; }

        [JsonPropertyName("city")]
        public Region City { get; set; }

        [JsonPropertyName("purpose")]
        public string Purpose { get; set; }

        [JsonPropertyName("year_of_construction_initial_dike")]
        public int? YearOfConstructionInitialDike { get; set; }

        [JsonPropertyName("construction_stages")]
        public int? ConstructionStages { get; set; }

        [JsonPropertyName("crest_dimension")]
        public Dimension CrestDimension { get; set; }

        [JsonPropertyName("total_height")]
        public double? TotalHeight { get; set; }

        [JsonPropertyName("downstream_slope")]
        public string DownstreamSlope { get; set; }

        [JsonPropertyName("upstream_slope")]
        public string UpstreamSlope { get; set; }

        [JsonPropertyName("classification")]
        public Classification? Classification { get; set; }

        [JsonPropertyName("section_type")]
        public string SectionType { get; set; }

        [JsonPropertyName("foundation_type")]
        public string FoundationType { get; set; }

        [JsonPropertyName("raising_method")]
        public string RaisingMethod { get; set; }

        [JsonPropertyName("expected_elevations")]
        public int? ExpectedElevations { get; set; }

        [JsonPropertyName("elevations_made")]
        public int? ElevationsMade { get; set; }

        [JsonPropertyName("reservoir_design_volume")]
        public double? ReservoirDesignVolume { get; set; }

        [JsonPropertyName("current_reservoir_volume")]
        public double? CurrentReservoirVolume { get; set; }

        [JsonPropertyName("internal_drainage")]
        public string InternalDrainage { get; set; }

        [JsonPropertyName("superficial_drainage")]
        public string SuperficialDrainage { get; set; }

        [JsonPropertyName("basin_area_in_square_kilometers")]
        public double? BasinAreaInSquareKilometers { get; set; }

        [JsonPropertyName("project_precipitation")]
        public double? ProjectPrecipitation { get; set; }

        [JsonPropertyName("full_of_project")]
        public int? FullOfProject { get; set; }

        [JsonPropertyName("maximum_influent_flow")]
        public double? MaximumInfluentFlow { get; set; }

        [JsonPropertyName("project_flow")]
        public double? ProjectFlow { get; set; }

        [JsonPropertyName("normal_maximum_water_level")]
        public double? NormalMaximumWaterLevel { get; set; }

        [JsonPropertyName("maximum_water_level_maximorum")]
        public double? MaximumWaterLevelMaximorum { get; set; }

        [JsonPropertyName("freeboard_normal_maximum_water_level")]
        public double? FreeboardNormalMaximumWaterLevel { get; set; }

        [JsonPropertyName("freeboard_maximum_water_level_maximorum")]
        public double? FreeboardMaximumWaterLevelMaximorum { get; set; }

        [JsonPropertyName("spillway")]
        public string Spillway { get; set; }

        [JsonPropertyName("aspects")]
        public List<AspectStructure> Aspects { get; set; }

        [JsonPropertyName("responsibles")]
        public List<Responsible> Responsibles { get; set; }
    }
}
