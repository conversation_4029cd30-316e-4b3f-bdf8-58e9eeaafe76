using Application.Apis.Clients.Response._Shared;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Structure.GetStabilityAnalysisInfo
{
    public sealed record GetStructureStabilityAnalysisInfoResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; init; }

        [JsonPropertyName("name")]
        public string Name { get; init; }

        [JsonPropertyName("should_evaluate_drained_condition")]
        public bool ShouldEvaluateDrainedCondition { get; init; }

        [JsonPropertyName("should_evaluate_undrained_condition")]
        public bool ShouldEvaluateUndrainedCondition { get; init; }

        [JsonPropertyName("should_evaluate_pseudo_static_condition")]
        public bool ShouldEvaluatePseudoStaticCondition { get; init; }

        [JsonPropertyName("slide2_configuration")]
        public Slide2Configuration Slide2Configuration { get; init; }

        [JsonPropertyName("seismic_coefficient")]
        public Orientation SeismicCoefficient { get; init; }
    }
}
