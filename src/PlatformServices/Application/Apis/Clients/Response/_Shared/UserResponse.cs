using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response._Shared
{
    public sealed record UserResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("username")]
        public string Username { get; set; }

        [<PERSON>son<PERSON>ropertyName("first_name")]
        public string FirstName { get; set; }

        [<PERSON>son<PERSON>roperty<PERSON><PERSON>("surname")]
        public string Surname { get; set; }
    }
}
