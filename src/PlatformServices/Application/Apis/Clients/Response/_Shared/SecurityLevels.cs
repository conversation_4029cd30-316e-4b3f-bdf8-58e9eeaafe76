using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response._Shared
{
    public sealed record SecurityLevels
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("attention")]
        public decimal? Attention { get; set; }

        [JsonPropertyName("alert")]
        public decimal? Alert { get; set; }

        [JsonPropertyName("emergency")]
        public decimal? Emergency { get; set; }

        [JsonPropertyName("abrupt_variation_between_readings")]
        public decimal? AbruptVariationBetweenReadings { get; set; }

        [JsonPropertyName("maximum_daily_rainfall")]
        public decimal? MaximumDailyRainfall { get; set; }

        [JsonPropertyName("rain_intensity")]
        public decimal? RainIntensity { get; set; }

        [JsonPropertyName("axis")]
        public Axis? Axis { get; set; }

    }
}
