using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response._Shared
{
    public sealed record ClientUnitResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("active")]
        public bool? Active { get; set; }

        [JsonPropertyName("client_id")]
        public Guid ClientId { get; set; }
    }
}
