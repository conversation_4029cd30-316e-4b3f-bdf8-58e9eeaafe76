using System.Text.Json.Serialization;
using Coordinate.Core.Classes;

namespace Application.Apis.Clients.Response._Shared
{
    public sealed record StructureResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("coordinate_setting")]
        public CoordinateSetting CoordinateSetting { get; set; }
    }
}
