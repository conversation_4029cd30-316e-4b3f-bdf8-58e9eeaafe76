using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response._Shared
{
    public sealed record CircularParameters
    {
        [JsonPropertyName("calculation_methods")]
        public List<CalculationMethod> CalculationMethods { get; set; }

        [JsonPropertyName("circular_search_method")]
        public CircularSearchMethod CircularSearchMethod { get; set; }

        [JsonPropertyName("divisions_along_slope")]
        public int? DivisionsAlongSlope { get; set; }

        [JsonPropertyName("circles_per_division")]
        public int? CirclesPerDivision { get; set; }

        [JsonPropertyName("number_of_iterations")]
        public int? NumberOfIterations { get; set; }

        [JsonPropertyName("divisions_next_iteration")]
        public int? DivisionsNextIteration { get; set; }

        [JsonPropertyName("radius_increment")]
        public int? RadiusIncrement { get; set; }

        [JsonPropertyName("number_of_surfaces")]
        public int? NumberOfSurfaces { get; set; }
    }
}
