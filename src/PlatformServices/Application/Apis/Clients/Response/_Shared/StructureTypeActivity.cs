using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response._Shared
{
    public sealed record StructureTypeActivity
    {
        [JsonPropertyName("id")]
        public Guid? Id { get; set; }

        [JsonPropertyName("activity")]
        public Activity Activity { get; set; }

        [JsonPropertyName("index")]
        public int Index { get; set; }
    }
}
