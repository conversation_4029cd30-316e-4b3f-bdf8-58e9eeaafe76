using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response._Shared
{
    public sealed record AspectResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("description")]
        public string Description { get; set; }

        [JsonPropertyName("allow_option_not_applicable")]
        public bool AllowOptionNotApplicable { get; set; }

        [JsonPropertyName("response_for_occurrence")]
        public OccurrenceDecision ResponseForOccurrence { get; set; }

        [JsonPropertyName("area")]
        public AreaResponse Area { get; set; }
    }
}
