using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response._Shared
{
    public sealed record GetStructureTypeByIdResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("description")]
        public string Description { get; set; }

        [JsonPropertyName("active")]
        public bool Active { get; set; }

        [JsonPropertyName("activities")]
        public List<StructureTypeActivity> Activities { get; set; }

        public bool IsActivityPresent(Domain.Enums.Activity activity)
        {
            return 
                Activities?
                    .Any(structureActivity => structureActivity.Activity == activity) 
                    ?? false;
        }
    }
}
