using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response._Shared
{
    public sealed record ReadingValueResponse
    {
        [JsonPropertyName("id")]
        public Guid? Id { get; set; }

        [JsonPropertyName("date")]
        public DateTime? Date { get; set; }

        [JsonPropertyName("quota")]
        public decimal? Quota { get; set; }

        [JsonPropertyName("dry")]
        public bool? Dry { get; set; }
    }
}
