using Domain.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response._Shared
{
    public sealed record NonCircularParameters
    {
        [JsonPropertyName("calculation_methods")]
        public List<CalculationMethod> CalculationMethods { get; set; }

        [JsonPropertyName("non_circular_search_method")]
        public NonCircularSearchMethod NonCircularSearchMethod { get; set; }

        [JsonPropertyName("divisions_along_slope")]
        public int? DivisionsAlongSlope { get; set; }

        //In the slide2 document this field is called circles per division
        //but the request was to call it surfaces per division
        [JsonPropertyName("surfaces_per_division")]
        public int? SurfacesPerDivision { get; set; }

        [JsonPropertyName("number_of_iterations")]
        public int? NumberOfIterations { get; set; }

        [JsonPropertyName("divisions_next_iteration")]
        public int? DivisionsNextIteration { get; set; }

        [JsonPropertyName("number_of_vertices_along_surface")]
        public int? NumberOfVerticesAlongSurface { get; set; }

        [JsonPropertyName("number_of_surfaces")]
        public int? NumberOfSurfaces { get; set; }

        [JsonPropertyName("number_of_nests")]
        public int? NumberOfNests { get; set; }

        [JsonPropertyName("maximum_iterations")]
        public int? MaximumIterations { get; set; }

        [JsonPropertyName("initial_number_of_surface_vertices")]
        public int? InitialNumberOfSurfaceVertices { get; set; }

        [JsonPropertyName("initial_number_of_iterations")]
        public int? InitialNumberOfIterations { get; set; }

        [JsonPropertyName("maximum_number_of_steps")]
        public int? MaximumNumberOfSteps { get; set; }

        [JsonPropertyName("number_of_factors_safety_compared_before_stopping")]
        public int? NumberOfFactorsSafetyComparedBeforeStopping { get; set; }

        [JsonPropertyName("tolerance_for_stopping_criterion")]
        public double? ToleranceForStoppingCriterion { get; set; }

        [JsonPropertyName("number_of_particles")]
        public int? NumberOfParticles { get; set; }
    }
}
