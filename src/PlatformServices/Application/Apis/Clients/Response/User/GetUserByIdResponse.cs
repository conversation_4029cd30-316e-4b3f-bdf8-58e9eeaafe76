using System.Text.Json.Serialization;

namespace Application.Apis.Users.Response
{
    public sealed record GetUserByIdResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("username")]
        public string Username { get; set; }

        [<PERSON>son<PERSON>ropertyN<PERSON>("first_name")]
        public string FirstName { get; set; }

        [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("surname")]
        public string Surname { get; set; }
        
        public string GetFullName()
        {
            return $"{FirstName} {Surname}";
        }
    }
}
