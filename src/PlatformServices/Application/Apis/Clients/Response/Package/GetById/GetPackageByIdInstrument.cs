using Application.Apis.Clients.Response._Shared;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Package.GetById
{
    public sealed record GetPackageByIdInstrument
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("identifier")]
        public string Identifier { get; set; }

        [JsonPropertyName("measurement")]
        public MeasurementResponse Measurement { get; set; }

        [JsonPropertyName("quota_variation")]
        public decimal QuotaVariation { get; set; }

        [JsonPropertyName("reading_value")]
        public ReadingValueResponse ReadingValue { get; set; }

        [JsonPropertyName("reading_value_linked_to_package")]
        public bool ReadingValueLinkedToPackage { get; set; }
    }
}
