using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Package.GetById
{
    public sealed record GetPackageByIdSectionReview
    {
        [JsonPropertyName("id")]
        public Guid Id { get; init; }

        [JsonPropertyName("start_date")]
        public DateTime StartDate { get; init; }

        [JsonPropertyName("index")]
        public int Index { get; init; }
    }
}
