using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Package.GetById
{
    public sealed record GetPackageByIdResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("date")]
        public DateTime Date { get; set; }

        [JsonPropertyName("calculated_by_id")]
        public Guid? CalculatedById { get; init; }

        [JsonPropertyName("structure_have_activity_update_water_level_upstream")]
        public bool StructureHaveActivityUpdateWaterLevelUpstream { get; set; }

        [JsonPropertyName("structure_have_activity_process_water_level_downstream")]
        public bool StructureHaveActivityProcessWaterLevelDownstream { get; set; }

        [JsonPropertyName("upstream_linimetric_ruler")]
        public PackageLinimetricRuler UpstreamLinimetricRuler { get; set; }

        [JsonPropertyName("downstream_linimetric")]
        public PackageLinimetricRuler DownstreamLinimetricRuler { get; set; }

        [JsonPropertyName("sections")]
        public List<GetPackageByIdSection> Sections { get; set; } = new();
    }
}
