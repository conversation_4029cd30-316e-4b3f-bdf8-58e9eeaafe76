using Application.Apis.Clients.Response._Shared;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Package.GetById
{
    public sealed record GetPackageByIdSection
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("section_review")]
        public GetPackageByIdSectionReview SectionReview { get; set; }

        [JsonPropertyName("construction_stage")]
        public GetPackageByIdConstructionStage ConstructionStage { get; set; }

        [JsonPropertyName("structure")]
        public StructureResponse Structure { get; set; }

        [JsonPropertyName("beach_length")]
        public PackageBeachLength BeachLength { get; set; }

        [JsonPropertyName("instruments")]
        public List<GetPackageByIdInstrument> Instruments { get; set; } = new();
    }
}
