using System.Text.Json.Serialization;
using Domain.Enums;

namespace Application.Apis.Clients.Response.InspectionSheet;

public sealed record PreviousSituation
{
    [JsonPropertyName("spillway_structures_reliability")]
    public ScoringTypeSpillwayAndPercolation? SpillwayStructuresReliability { get; set; }

    [JsonPropertyName("percolation")]
    public ScoringTypeSpillwayAndPercolation? Percolation { get; set; }

    [JsonPropertyName("deformations_and_settlements")]
    public ScoringTypeDeformationsAndSlope? DeformationsAndSettlements { get; set; }

    [JsonPropertyName("slope_deterioration")]
    public ScoringTypeDeformationsAndSlope? SlopeDeterioration { get; set; }

    [JsonPropertyName("surface_drainage")]
    public ScoringTypeSurfaceDrainage? SurfaceDrainage { get; set; }
}