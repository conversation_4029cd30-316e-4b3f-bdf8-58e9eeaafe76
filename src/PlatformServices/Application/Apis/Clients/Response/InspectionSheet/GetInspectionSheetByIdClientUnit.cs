using System.Text.Json.Serialization;
using Coordinate.Core.Classes;

namespace Application.Apis.Clients.Response.InspectionSheet;

public sealed record GetInspectionSheetByIdClientUnit
{
    [JsonPropertyName("id")]
    public Guid Id { get; set; }

    [JsonPropertyName("name")]
    public string Name { get; set; }

    [JsonPropertyName("active")]
    public bool? Active { get; set; }

    [JsonPropertyName("client_id")]
    public Guid ClientId { get; set; }
}