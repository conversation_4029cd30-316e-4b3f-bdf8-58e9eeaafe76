using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.InspectionSheet;

public sealed record OccurrenceAttachment
{
    [JsonPropertyName("id")]
    public Guid? Id { get; set; }

    [JsonPropertyName("file")]
    public Application.Apis._Shared.File File { get; set; }

    [JsonPropertyName("northing")]
    public double? Northing { get; set; }

    [JsonPropertyName("easting")]
    public double? Easting { get; set; }

    [JsonPropertyName("latitude_sirgas2000")]
    public double? LatitudeSirgas2000 { get; set; }

    [JsonPropertyName("longitude_sirgas2000")]
    public double? LongitudeSirgas2000 { get; set; }
}