using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.InspectionSheet;

public sealed record InspectionSheetResponsible
{
    [JsonPropertyName("id")]
    public Guid? Id { get; set; }

    [JsonPropertyName("responsible")]
    public string Responsible { get; set; }
    
    [JsonPropertyName("is_internal_representative")]
    public bool IsInternalRepresentative { get; set; }
}