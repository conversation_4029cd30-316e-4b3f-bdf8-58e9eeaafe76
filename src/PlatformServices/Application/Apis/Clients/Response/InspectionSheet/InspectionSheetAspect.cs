using System.Text.Json.Serialization;
using Domain.Enums;

namespace Application.Apis.Clients.Response.InspectionSheet;

public sealed record InspectionSheetAspect
{
    [JsonPropertyName("id")]
    public Guid Id { get; set; }

    [JsonPropertyName("description")]
    public string Description { get; set; }

    [JsonPropertyName("allow_option_not_applicable")]
    public bool AllowOptionNotApplicable { get; set; }

    [JsonPropertyName("response_for_occurrence")]
    public OccurrenceDecision ResponseForOccurrence { get; set; }
    
    [JsonPropertyName("voice_note")]
    public Application.Apis._Shared.File VoiceNote { get; set; }

    [JsonPropertyName("occurrences")]
    public List<Occurrence> Occurrences { get; set; }
}