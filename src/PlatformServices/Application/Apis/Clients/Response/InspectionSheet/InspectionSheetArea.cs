using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.InspectionSheet;

public sealed record InspectionSheetArea
{
    [JsonPropertyName("id")]
    public Guid Id { get; set; }

    [JsonPropertyName("name")]
    public string Name { get; set; }
    
    [JsonPropertyName("voice_note")]
    public Application.Apis._Shared.File VoiceNote { get; set; }

    [JsonPropertyName("aspects")]
    public List<InspectionSheetAspect> Aspects { get; set; }
}