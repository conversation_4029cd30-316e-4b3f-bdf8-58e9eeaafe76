using System.Text.Json.Serialization;
using Domain.Enums;

namespace Application.Apis.Clients.Response.InspectionSheet;

public sealed record IdentifiedAnomaly
{
    [JsonPropertyName("id")]
    public Guid Id { get; set; }

    [JsonPropertyName("anomaly")]
    public Anomaly Anomaly { get; set; }

    [JsonPropertyName("executed_actions")]
    public string ExecutedActions { get; set; }

    [JsonPropertyName("action_result_classification")]
    public ActionResultClassification? ActionResultClassification { get; set; }

    [JsonPropertyName("anomaly_score")]
    public AnomalyScore? AnomalyScore { get; set; }

    [JsonPropertyName("note")]
    public string Note { get; set; }
}