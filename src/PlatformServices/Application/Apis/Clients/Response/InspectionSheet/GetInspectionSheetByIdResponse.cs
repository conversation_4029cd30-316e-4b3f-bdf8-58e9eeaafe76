using System.Collections.Concurrent;
using System.Text.Json.Serialization;
using Application.Apis.Clients.Response._Shared;
using Domain.Enums;

namespace Application.Apis.Clients.Response.InspectionSheet;

public sealed record GetInspectionSheetByIdResponse
{
    [JsonPropertyName("id")]
    public Guid Id { get; set; }

    [JsonPropertyName("search_identifier")]
    public int SearchIdentifier { get; set; }

    [JsonPropertyName("client")]
    public ClientResponse Client { get; set; }

    [JsonPropertyName("client_unit")]
    public GetInspectionSheetByIdClientUnit ClientUnit { get; set; }

    [JsonPropertyName("structure")]
    public StructureResponse Structure { get; set; }

    [JsonPropertyName("created_by")]
    public UserResponse CreatedBy { get; set; }

    [JsonPropertyName("created_date")]
    public DateTime CreatedDate { get; set; }

    [JsonPropertyName("locked")]
    public bool Locked { get; set; }

    [JsonPropertyName("locked_by")]
    public UserResponse LockedBy { get; set; }

    [JsonPropertyName("locked_at")]
    public DateTime? LockedAt { get; set; }

    [JsonPropertyName("status")]
    public InspectionSheetStatus Status { get; set; }

    [JsonPropertyName("type")]
    public InspectionSheetType Type { get; set; }

    [JsonPropertyName("start_date")]
    public DateTime? StartDate { get; set; }

    [JsonPropertyName("end_date")]
    public DateTime? EndDate { get; set; }

    [JsonPropertyName("executed_by")]
    public string ExecutedBy { get; set; }

    [JsonPropertyName("responsibles")]
    public List<InspectionSheetResponsible> Responsibles { get; set; }

    [JsonPropertyName("evaluator_name")]
    public string EvaluatorName { get; set; }

    [JsonPropertyName("evaluator_position")]
    public string EvaluatorPosition { get; set; }

    [JsonPropertyName("evaluator_crea")]
    public string EvaluatorCrea { get; set; }

    [JsonPropertyName("evaluator_art")]
    public string EvaluatorArt { get; set; }

    [JsonPropertyName("file")]
    public Application.Apis._Shared.File File { get; set; }

    [JsonPropertyName("spillway_structures_reliability")]
    public ScoringTypeSpillwayAndPercolation? SpillwayStructuresReliability
    {
        get;
        set;
    }

    [JsonPropertyName("percolation")]
    public ScoringTypeSpillwayAndPercolation? Percolation { get; set; }

    [JsonPropertyName("deformations_and_settlements")]
    public ScoringTypeDeformationsAndSlope? DeformationsAndSettlements
    {
        get;
        set;
    }

    [JsonPropertyName("slope_deterioration")]
    public ScoringTypeDeformationsAndSlope? SlopeDeterioration { get; set; }

    [JsonPropertyName("surface_drainage")]
    public ScoringTypeSurfaceDrainage? SurfaceDrainage { get; set; }

    [JsonPropertyName("notes")]
    public List<InspectionSheetNote> Notes { get; set; }

    [JsonPropertyName("identified_anomalies")]
    public List<IdentifiedAnomaly> IdentifiedAnomalies { get; set; }

    [JsonPropertyName("areas")]
    public List<InspectionSheetArea> Areas { get; set; }

    [JsonPropertyName("previous_situation")]
    public PreviousSituation PreviousSituation { get; set; }

    [JsonPropertyName("transcription_is_in_progress")]
    public bool TranscriptionIsInProgress { get; set; }

    public bool HasVoiceNotesToTranscribe =>
        Notes.Any(note => note.VoiceNote != null) || 
        Areas.Any(area =>
            area.Aspects.Any(aspect => 
                aspect.Occurrences.Any(occurrence => occurrence.VoiceNote != null)));

}