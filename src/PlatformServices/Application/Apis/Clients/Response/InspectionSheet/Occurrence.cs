using System.Text.Json.Serialization;
using Domain.Enums;

namespace Application.Apis.Clients.Response.InspectionSheet;

public sealed record Occurrence
{
    [JsonPropertyName("id")]
    public Guid? Id { get; set; }

    [JsonPropertyName("search_identifier")]
    public int SearchIdentifier { get; set; }

    [JsonPropertyName("northing")]
    public double? Northing { get; set; }

    [JsonPropertyName("easting")]
    public double? Easting { get; set; }

    [JsonPropertyName("latitude_sirgas2000")]
    public double? LatitudeSirgas2000 { get; set; }

    [JsonPropertyName("longitude_sirgas2000")]
    public double? LongitudeSirgas2000 { get; set; }

    [JsonPropertyName("note")]
    public string Note { get; set; }

    [JsonPropertyName("linked_to")]
    public OccurrenceInfo LinkedTo { get; set; }

    [JsonPropertyName("response")]
    public OccurrenceResponse? Response { get; set; }
    
    [JsonPropertyName("voice_note")]
    public Application.Apis._Shared.File VoiceNote { get; set; }

    [JsonPropertyName("occurrence_attachments")]
    public List<OccurrenceAttachment> OccurrenceAttachments { get; set; }
}