using Application.Apis.Clients.Request;
using Application.Apis.Clients.Request.InspectionSheet;
using Application.Apis.Clients.Request.Instrument.GetAutomatedReadingsData;
using Application.Apis.Clients.Request.Notification;
using Application.Apis.Clients.Request.Occurrence;
using Application.Apis.Clients.Request.Simulation.List;
using Application.Apis.Clients.Request.Simulation.Patch;
using Application.Apis.Clients.Response;
using Application.Apis.Clients.Response.Clients;
using Application.Apis.Clients.Response.InspectionSheet;
using Application.Apis.Clients.Response.Instrument;
using Application.Apis.Clients.Response.Instrument.GetAutomatedReadingsData;
using Application.Apis.Clients.Response.Occurrence;
using Application.Apis.Clients.Response.OccurrenceListReport;
using Application.Apis.Clients.Response.Package.GetById;
using Application.Apis.Clients.Response.Reading;
using Application.Apis.Clients.Response.Report;
using Application.Apis.Clients.Response.Section.GetById;
using Application.Apis.Clients.Response.Simulation.GetById;
using Application.Apis.Clients.Response.Simulation.List;
using Application.Apis.Clients.Response.StabilityAnalysis.GetByIdWithSafetyFactorsMetrics;
using Application.Apis.Clients.Response.StaticMaterial.GetBySearchId;
using Application.Apis.Clients.Response.Structure.GetById;
using Application.Apis.Clients.Response.Structure.GetStabilityAnalysisInfo;
using Application.Apis.Clients.Response.User;
using Application.Apis.Users.Request;
using Application.Apis.Users.Response;
using Refit;

namespace Application.Apis.Clients
{
    public interface IClientsApiService
    {
        Task<List<GetExpiringClientsResponse>> GetExpiringClients();
        Task<IEnumerable<GetUserEmailResponse>> GetUsersEmails(GetUserEmailRequest request);
        Task<List<ListUserResponse>> GetUsersAsync(ListUserRequest request);
        Task<HttpResponseMessage> DeleteNotifications();
        Task<GetReadingByIdResponse> GetReadingById(Guid id);
        Task<GetInstrumentByIdResponse> GetInstrumentById(Guid id);
        Task<IEnumerable<GetInstrumentByIdResponse>> GetInstrumentsByIds(IEnumerable<Guid> ids);
        Task<GetAdjacentReadingValuesResponse> GetInstrumentAdjacentReadingValues(Guid id, Guid? measurementId, DateTime date);
        Task<bool> AddSecurityLevelAlert(Guid id, AddInstrumentSecurityLevelAlertsRequest request);
        Task<GetPackageByIdResponse> GetPackageById(Guid id);
        Task<GetSectionByIdResponse> GetSectionById(Guid id);
        Task<GetStructureByIdResponse> GetStructureById(Guid id);
        Task<decimal?> GetTotalRainfall(Guid instrumentId, DateTime readingValueDate);
        Task<List<GetStaticMaterialBySearchIdResponse>> GetStaticMaterialsBySearchIds(List<int> searchIds, DateTime? reviewDate);
        Task<GetStaticMaterialBySearchIdResponse> GetStaticMaterialById(Guid id);
        Task<HttpResponseMessage> AddSliToSectionReview(AddSliFileRequest request);

        Task<IEnumerable<GetPendingReportResponse>> GetPendingReports(
            DateTime startDate, DateTime endDate);

        Task<bool> UpdateReportEmissionDates(
            Guid id,
            DateTime nextEmissionDate,
            DateTime lastEmissionDate);

        Task<T> GetReportData<T>(Guid reportId);
        Task<HttpResponseMessage> AddNotification(AddNotificationRequest request);
        Task<GetUserByIdResponse> GetUserById(Guid id);
        Task<Guid> CreateStabilityAnalysis(AddStabilityAnalysisRequest request);
        Task<Guid> DeletePackage(Guid packageId, Guid sectionId);
        Task<GetStabilityAnalysisByIdWithSafetyFactorsMetricsResponse> GetStabilityAnalysisWithSafetyFactorsMetricsAsync(Guid id);
        Task<IEnumerable<Guid>> GetRelatedReadingValues(Guid id);
        Task<IEnumerable<Guid>> AddPackage(AddPackageRequest request);
        Task<Guid> CalculatePackageStability(CalculatePackageStabilityRequest request);
        Task<GetSimulationByIdResponse> GetSimulationById(Guid id);
        Task<List<GetReadingStatsResponse>> GetReadingStats(GetReadingStatsRequest request);
        Task<List<GetBeachLengthStatsResponse>> GetBeachLengthStats(GetBeachLengthStatsRequest request);
        Task<HttpResponseMessage> PatchSimulation(PatchSimulationRequest request);
        Task<GetStructureStabilityAnalysisInfoResponse> GetStructureStabilityAnalysisInfo(Guid id);

        Task<IEnumerable<GetClientAutomatedReadingsConfigurationsResponse>>
            GetEnabledClientAutomatedReadingsConfigurationsAsync();

        Task<ApiResponse<IEnumerable<Guid>>> AddReadingsAsync(
            IEnumerable<AddReadingRequest> readings);

        Task<List<ListSimulationResponse>> ListSimulations(ListSimulationRequest request);
        Task<HttpResponseMessage> DeleteSimulation(Guid id);
        Task<List<GetAutomatedReadingsDataResponse>> GetAutomatedReadingsDataAsync(
            GetAutomatedReadingsDataRequest request);

        Task<bool> UpdatePackageStatus(UpdatePackageStatusRequest request);
        Task<bool> UpdateSimulationStatus(UpdateSimulationStatusRequest request);
        Task<GetInspectionSheetByIdResponse> GetInspectionSheetAsync(Guid id);
        Task UpdateInspectionSheetAsync(UpdateInspectionSheetRequest request);
        Task<GetOccurrenceListReportByIdResponse> GetOccurrenceListReportAsync(Guid id);
        Task<List<SearchOccurrenceUnpaginatedResponse>> SearchOccurrenceUnpaginatedAsync(
            List<Guid> structures,
            SearchOccurrenceUnpaginatedRequest request);
        Task<GetClientLogoResponse> GetClientLogoAsync(Guid id);
        Task<bool> CheckIfNotificationExists(ExistsNotificationRequest request);
    }
}
