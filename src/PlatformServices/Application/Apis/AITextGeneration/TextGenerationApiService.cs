using Application.Apis.AITextGeneration.Request;
using Microsoft.Extensions.Logging;

namespace Application.Apis.AITextGeneration;

public sealed class TextGenerationApiService : ITextGenerationApiService
{
    private readonly ITextGenerationApi _textGenerationApi;
    private readonly ILogger<TextGenerationApiService> _logger;

    public TextGenerationApiService(
        ITextGenerationApi textGenerationApi,
        ILogger<TextGenerationApiService> logger)
    {
        _textGenerationApi = textGenerationApi;
        _logger = logger;
    }

    public async Task<string> GenerateText(
        string contextMessage,
        string userMessage)
    {
        var improveTextRequest = new TextGenerationRequest(
            new[]
            {
                new MessageRequest("system", contextMessage),
                new MessageRequest("user", userMessage)
            },
            "gpt-35-turbo-16k");

        var response = await _textGenerationApi
            .GenerateText(improveTextRequest);

        if (!response.IsSuccessStatusCode)
        {
            _logger.LogWarning(
                response.Error,
                "Text Generation API call failed with status code {0}.",
                response.StatusCode);

            return string.Empty;
        }

        return response.Content!.Value.Choices[0].Message.Content;
    }
}