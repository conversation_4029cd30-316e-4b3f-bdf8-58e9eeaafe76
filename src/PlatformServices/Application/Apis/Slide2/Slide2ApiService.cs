using Application.Apis.Keycloak;
using Application.Apis.Slide2.Request;
using Application.Apis.Slide2.Response;

namespace Application.Apis.Slide2
{
    public class Slide2ApiService : ISlide2ApiService
    {
        private readonly ISlide2Api _slide2Api;

        public Slide2ApiService(ISlide2Api slide2Api)
        {
            _slide2Api = slide2Api;
        }

        public async Task<string> CreateS01File(CreateS01FileRequest request)
        {
            var response = await _slide2Api.CreateS01File(request);

            return response.IsSuccessStatusCode
                ? response.Content
                : string.Empty;
        }

        public async Task<GetS01FileResponse> GetS01File(string fileId)
        {
            var response = await _slide2Api.GetS01File(fileId);

            return response.IsSuccessStatusCode
                ? response.Content
                : null;
        }
    }
}
