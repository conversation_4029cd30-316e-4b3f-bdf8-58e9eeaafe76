using System.Diagnostics.Metrics;
using static Application.Observability.ApplicationConstants;
using static Application.Observability.Metrics.StabilityAnalysisMetricsConstants;
using static Application.Observability.Metrics.MeasurementUnits;

namespace Application.Observability.Metrics;

public class StabilityAnalysisMetricHandler
{
    private readonly Counter<int> _slide2AnalysisFailed;

    public StabilityAnalysisMetricHandler(IMeterFactory meterFactory)
    {
        var meter = meterFactory.Create(ServiceName, ServiceVersion);
        
        _slide2AnalysisFailed = meter.CreateCounter<int>(
            Slide2AnalysisFailed,
            Occurrences);
    }

    public void IncrementSlide2AnalysisFailed(int quantity = 1)
    {
        _slide2AnalysisFailed.Add(quantity);
    }
}