using Microsoft.AspNetCore.Hosting;
using Serilog;
using System.Net.Mail;
using System.Net.Mime;

namespace Application.Email
{
    public class EmailService : IEmailService
    {
        private readonly SmtpClient _smtpClient;
        private readonly IWebHostEnvironment _app;
        private readonly string _folderPath = "email";
        private readonly string _defaultSender;

        public EmailService(
            SmtpClient smtpClient,
            string defaultSender,
            IWebHostEnvironment app)
        {
            _smtpClient = smtpClient;
            _app = app;
            _defaultSender = defaultSender;
        }

        public async Task Send(MailMessage message, string template = "default-template.html")
        {
            try
            {
                AddDefaultSender(message);

                var path = Path.Combine(_app.WebRootPath, _folderPath, template);

                using (var reader = new StreamReader(path))
                {
                    var body = reader.ReadToEnd();

                    message.AlternateViews.Add(AlternateView
                        .CreateAlternateViewFromString(body.Replace("[content]", message.Body), default, MediaTypeNames.Text.Html));
                }

                await _smtpClient.SendMailAsync(message);
            }
            catch (Exception e)
            {
                Log.Warning(e, "Email failed to send.");
            }
        }

        private void AddDefaultSender(MailMessage message)
        {
            if (string.IsNullOrEmpty(message.Sender?.Address))
            {
                message.Sender = new(_defaultSender);
            }

            if (string.IsNullOrEmpty(message.From?.Address))
            {
                message.From = new(_defaultSender);
            }
        }
    }
}
