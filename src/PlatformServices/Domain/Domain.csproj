<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MassTransit" Version="7.3.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Domain.Core\Domain.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Resources\Texts.pt-BR.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Texts.pt-BR.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Resources\Texts.en.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Texts.es.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\Texts.pt-BR.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Texts.pt-BR.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

</Project>
