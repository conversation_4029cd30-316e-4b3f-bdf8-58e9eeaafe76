using Domain.Enums;

namespace Domain.Messages.Commands.Notification;

public class InstrumentNotification : CreateNotification
{
    public override string GenerateMessage()
    {
        var template = Theme switch
        {
            NotificationTheme.InstrumentCreated =>
                "O instrumento {0} foi cadastrado na estrutura {1} em {2}, pelo usuário {3}.",
            NotificationTheme.InstrumentUpdated =>
                "O instrumento {0} da estrutura {1} sofreu alteração em {2}, pelo usuário {3}. @{4}",
            NotificationTheme.InstrumentDeleted =>
                "O instrumento {0} da estrutura {1} foi inativado em {2}, pelo usuário {3}.",
            NotificationTheme.InstrumentRepaired =>
                "O instrumento {0} na estrutura {1} foi classificado como restaurado pelo usuário {3} em {2}.",
            NotificationTheme.InstrumentDamaged =>
                "O instrumento {0} da estrutura {1} foi classificado como avariado pelo usuário {3} em {2}.",
            _ =>
                "Ação desconhecida para o instrumento {0} da estrutura {1} em {2}, pelo usuário {3}.",
        };

        return string.Format(
            template,
            AuxiliaryData["InstrumentName"],
            AuxiliaryData["StructureName"],
            CreatedDate.ToString("dd/MM/yyyy HH:mm"),
            AuxiliaryData["ModifiedBy"],
            AuxiliaryData.ContainsKey("InstrumentId")
                ? AuxiliaryData["InstrumentId"]
                : string.Empty);
    }
}