using Domain.Enums;

namespace Domain.Messages.Commands.Notification;

public class CreateNotification
{
    public Guid Id { get; set; }
    public NotificationTheme Theme { get; set; }
    public Guid? CreatedById { get; set; }
    public DateTime CreatedDate { get; set; }
    public IDictionary<string, string> AuxiliaryData { get; set; } =
        new Dictionary<string, string>();

    public virtual string GenerateMessage() => string.Empty;

    public StructureNotification ToStructureNotification() =>
        new()
        {
            Id = Id,
            Theme = Theme,
            CreatedById = CreatedById,
            CreatedDate = CreatedDate,
            AuxiliaryData = AuxiliaryData ?? new Dictionary<string, string>()
        };

    public InstrumentNotification ToInstrumentNotification() =>
        new()
        {
            Id = Id,
            Theme = Theme,
            CreatedById = CreatedById,
            CreatedDate = CreatedDate,
            AuxiliaryData = AuxiliaryData ?? new Dictionary<string, string>()
        };
}