using Domain.Enums;

namespace Domain.Messages.Commands.Notification;

public sealed class StructureNotification : CreateNotification
{
    public override string GenerateMessage()
    {
        var template = Theme switch
        {
            NotificationTheme.StructureUpdated =>
                "A estrutura {0} da unidade {1} sofreu alteração em {2} pelo usuário {3}.",
            
            NotificationTheme.StructureDeleted =>
                "A estrutura {0} da unidade {1} foi inativada em {2} pelo usuário {3}.",
            
            _ =>
                "Ação desconhecida para a estrutura {0} da unidade {1} em {2} pelo usuário {3}.",
        };

        return string.Format(
            template, 
            AuxiliaryData["StructureName"],
            AuxiliaryData["ClientUnitName"],
            CreatedDate.ToString("dd/MM/yyyy HH:mm"), 
            AuxiliaryData["ModifiedBy"]);
    }
}