namespace Domain.Extensions
{
    public static class IntExtensions
    {
        public static string GetMonthName(this int monthNumber)
        {
            if (monthNumber < 1 || monthNumber > 12)
            {
                throw new ArgumentOutOfRangeException();
            }

            string[] monthNames = {
                "Janeiro", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>",
                "<PERSON><PERSON>", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"
            };

            return monthNames[monthNumber - 1];
        }
    }
}
