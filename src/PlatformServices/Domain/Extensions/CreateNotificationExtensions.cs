using Domain.Enums;
using Domain.Messages.Commands.Notification;

namespace Domain.Extensions
{
    public static class CreateNotificationExtensions
    {
        public static string GetInstrumentationMessageByAction(
            this CreateNotification notification)
        {
            return notification.Theme switch
            {
                NotificationTheme.InstrumentCreated => "O instrumento {0} foi cadastrado na estrutura {1} em {2}, pelo usuário {3}.",
                NotificationTheme.InstrumentUpdated => "O instrumento {0} da estrutura {1} sofreu alteração em {2}, pelo usuário {3}. @{4}",
                NotificationTheme.InstrumentDeleted => "O instrumento {0} da estrutura {1} foi inativado em {2}, pelo usuário {3}.",
                NotificationTheme.InstrumentRepaired => "O instrumento {0} na estrutura {1} foi classificado como restaurado pelo usuário {3} em {2}.",
                NotificationTheme.InstrumentDamaged => "O instrumento {0} da estrutura {1} foi classificado como avariado pelo usuário {3} em {2}.",
                _ => "Ação desconhecida para o instrumento {0} da estrutura {1} em {2}, pelo usuário {3}.",
            };
        }

        public static string GetControlLetterMessageByAction(
            this CreateNotification notification)
        {
            return notification.Theme switch
            {
                NotificationTheme.ControlLetterNewRegistry => "Novo registro de carta de controle adicionado para o instrumento {0} da estrutura {1} em {2}.",
                _ => "Ação desconhecida para carta de controle do instrumento {0} da estrutura {1} em {2}.",
            };
        }

        public static string GetReadingsMessageByAction(
            this CreateNotification notification)
        {
            return notification.Theme switch
            {
                NotificationTheme.ReadingCreated => "Nova leitura adicionada para o instrumento {0} da estrutura {1} com data/hora {2}, pelo usuário {3}.",
                NotificationTheme.ReadingUpdated => "O usuário {3} alterou a leitura do dia {2}, referente ao instrumento {0} da estrutura {1}. @{4}",
                NotificationTheme.ReadingDeleted => "A Leitura do instrumento {0} da estrutura {1}, referente data {2}, foi excluída pelo usuário {3}.",
                _ => "Ação desconhecida para a leitura do instrumento {0} da estrutura {1} em {2}, pelo usuário {3}."
            };
        }

        public static string GetStabilityAnalysisMessageByAction(
            this NotificationTheme notificationTheme)
        {
            return notificationTheme switch
            {
                NotificationTheme.StabilityAnalysisCreated => "Nova análise de estabilidade adicionada para a estrutura {0} referente à data/hora {1}, conforme solicitação do usuário {2}.",
                NotificationTheme.StabilityAnalysisUpdated => "Análise de estabilidade atualizada para a estrutura {0} referente à data/hora {1}, conforme solicitação do usuário {2}.",
                NotificationTheme.StabilityAnalysisSafetyFactorBelowToleratedLevels => "{0} O fator de segurança para a condição {1} da seção {2}, da estrutura {3} está abaixo de {4}.",
                NotificationTheme.StabilityAnalysisWithWarnings => "A análise de estabilidade da estrutura {0} referente à data/hora {1}, criada pelo usuário {2}, contém alertas: {3}",
                _ => "Ação desconhecida para a análise de estabilidade da estrutura {0} em {1}, pelo usuário {2}."
            };
        }
    }
}
