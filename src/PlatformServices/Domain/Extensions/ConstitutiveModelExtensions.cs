using Domain.Enums;

namespace Domain.Extensions
{
    public static class ConstitutiveModelExtensions
    {
        public static int GetTypeNumber(this ConstitutiveModel model)
        {
            return model switch
            {
                ConstitutiveModel.MohrCoulomb => 0,
                ConstitutiveModel.Undrained => 1,
                ConstitutiveModel.NoStrength => 2,
                ConstitutiveModel.InfiniteStrength => 3,
                ConstitutiveModel.ShearOrNormalFunction => 5,
                ConstitutiveModel.HoekBrown => 7,
                ConstitutiveModel.GeneralizedHoekBrown => 8,
                ConstitutiveModel.VerticalStressRatio => 9,
                ConstitutiveModel.Shansep => 18,
                _ => throw new NotImplementedException()
            };
        }
    }
}
