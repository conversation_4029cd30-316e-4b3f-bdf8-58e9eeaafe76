using Domain.Enums;

namespace Domain.Extensions;

public static class SliFileTypeExtensions
{
    /// <summary>
    /// Maps a given <see cref="SliFileType"/> to its corresponding <see cref="SoilConditionType"/>.
    /// </summary>
    /// <param name="sliFileType">The <see cref="SliFileType"/> to map.</param>
    /// <returns>
    /// A <see cref="SoilConditionType"/> that corresponds to the provided <see cref="SliFileType"/>.
    /// </returns>
    /// <exception cref="ArgumentOutOfRangeException">
    /// Thrown when the provided <see cref="SliFileType"/> is not recognized or supported.
    /// </exception>
    /// <remarks>
    /// The mapping is based on the type of analysis (e.g., Drained, Undrained, PseudoStatic) 
    /// associated with the <see cref="SliFileType"/>.
    /// </remarks>
    public static SoilConditionType MapSoilConditionType(
        this SliFileType sliFileType) =>
        sliFileType switch
        {
            SliFileType.CircularDrained
                or SliFileType.NonCircularDrained => SoilConditionType.Drained,
            SliFileType.CircularUndrained
                or SliFileType.NonCircularUndrained => SoilConditionType
                    .Undrained,
            SliFileType.CircularPseudoStatic
                or SliFileType.NonCircularPseudoStatic => SoilConditionType
                    .PseudoStatic,
            _ => throw new ArgumentOutOfRangeException(
                paramName: nameof(sliFileType),
                actualValue: sliFileType,
                message: "Unexpected value in switch statement.")
        };

    /// <summary>
    /// Maps a given <see cref="SliFileType"/> to its corresponding <see cref="SurfaceType"/>.
    /// </summary>
    /// <param name="sliFileType">The <see cref="SliFileType"/> to map.</param>
    /// <returns>
    /// A <see cref="SurfaceType"/> that corresponds to the provided <see cref="SliFileType"/>.
    /// </returns>
    /// <exception cref="ArgumentOutOfRangeException">
    /// Thrown when the provided <see cref="SliFileType"/> is not recognized or supported.
    /// </exception>
    /// <remarks>
    /// The mapping is based on the surface type (e.g., Circular, NonCircular)
    /// associated with the <see cref="SliFileType"/>.
    /// </remarks>
    public static SurfaceType MapSurfaceType(this SliFileType sliFileType) =>
        sliFileType switch
        {
            SliFileType.CircularDrained
                or SliFileType.CircularUndrained
                or SliFileType.CircularPseudoStatic => SurfaceType.Circular,
            SliFileType.NonCircularDrained
                or SliFileType.NonCircularUndrained
                or SliFileType.NonCircularPseudoStatic => SurfaceType
                    .NonCircular,
            _ => throw new ArgumentOutOfRangeException(
                paramName: nameof(sliFileType),
                actualValue: sliFileType,
                message: "Unexpected value in switch statement.")
        };
}