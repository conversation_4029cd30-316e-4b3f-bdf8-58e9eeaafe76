using Domain.Enums;

namespace Domain.Extensions
{
    public static class NonCircularSearchMethodExtensions
    {
        public static int GetNumber(this NonCircularSearchMethod nonCircularSearchMethod)
        {
            switch (nonCircularSearchMethod)
            {
                case NonCircularSearchMethod.AutoRefineSearch:
                    return 4;
                case NonCircularSearchMethod.CuckooSearch:
                    return 5;
                case NonCircularSearchMethod.SimulatedAnnealing:
                    return 3;
                case NonCircularSearchMethod.ParticleSwarmSearch:
                    return 6;
                case NonCircularSearchMethod.BlockSearch:
                    return 0;
                case NonCircularSearchMethod.PathSearch:
                    return 1;
                default:
                    throw new ArgumentOutOfRangeException(nameof(nonCircularSearchMethod), nonCircularSearchMethod, null);
            }
        }

        public static string GetRegexToReplace(this NonCircularSearchMethod nonCircularSearchMethod)
        {
            switch (nonCircularSearchMethod)
            {
                case NonCircularSearchMethod.AutoRefineSearch:
                    return "autoslope search:((?:.*?\r?\n)+?)(?=\r\n|$)";
                case NonCircularSearchMethod.CuckooSearch:
                    return "cuckoo search:((?:.*?\r?\n)+?)(?=\r\n|$)";
                case NonCircularSearchMethod.SimulatedAnnealing:
                    return "simulatedannealing search:((?:.*?\r?\n)+?)(?=\r\n|$)";
                case NonCircularSearchMethod.ParticleSwarmSearch:
                    return "pso search:((?:.*?\r?\n)+?)(?=\r\n|$)";
                case NonCircularSearchMethod.BlockSearch:
                    return "block search:((?:.*?\r?\n)+?)(?=\r\n|$)";
                case NonCircularSearchMethod.PathSearch:
                    return "path search:((?:.*?\r?\n)+?)(?=\r\n|$)";
                default:
                    throw new ArgumentOutOfRangeException(nameof(nonCircularSearchMethod), nonCircularSearchMethod, null);
            }
        }

        public static string GetMethodString(this NonCircularSearchMethod nonCircularSearchMethod, int? divisionsAlongSlope, int? surfacesPerDivision, int? numberOfIterations, int? divisionsNextIteration, int? numberOfVerticesAlongSurface, int? numberOfSurfaces, int? numberOfNests, int? maximumIterations, int? initialNumberOfSurfaceVertices, int? initialNumberOfIterations, int? maximumNumberOfSteps, int? numberOfFactorsSafetyComparedBeforeStopping, double? toleranceForStoppingCriterion, int? numberOfParticles, double? depth)
        {
            switch (nonCircularSearchMethod)
            {
                case NonCircularSearchMethod.AutoRefineSearch:
                    return $"autoslope search:\r\n" +
                        $"  divisionsalong: {divisionsAlongSlope}\r\n" +
                        $"  circlesperdiv: {surfacesPerDivision}\r\n" +
                        $"  iterations: {numberOfIterations}\r\n" +
                        $"  percentkept: {divisionsNextIteration}\r\n" +
                        $"  numvertices: {numberOfVerticesAlongSurface}\r\n" +
                        $"  optimize: 1\r\n" +
                        $"{(depth.HasValue ? $"  depth: {depth}\r\n" : "")}";
                case NonCircularSearchMethod.CuckooSearch:
                    return $"cuckoo search:\r\n" +
                        $"  nvertices: {numberOfVerticesAlongSurface}\r\n" +
                        $"  nsurf: 1000\r\n" +
                        $"  maxiterations: {maximumIterations}\r\n" +
                        $"  numnests: {numberOfNests}\r\n" +
                        $"  optimize: 1\r\n" +
                        $"  convex: 1\r\n" +
                        $"{(depth.HasValue ? $"  depth: {depth}\r\n" : "")}";
                case NonCircularSearchMethod.SimulatedAnnealing:
                    return $"simulatedannealing search:\r\n" +
                        $"  nvertices: {initialNumberOfSurfaceVertices}\r\n" +
                        $"  ngen: {initialNumberOfIterations}\r\n" +
                        $"  max_steps: {maximumNumberOfSteps}\r\n" +
                        $"  nepsilon: {numberOfFactorsSafetyComparedBeforeStopping}\r\n" +
                        $"  ftol: {toleranceForStoppingCriterion}\r\n" +
                        $"  c: 8\r\n  convex: 1\r\n" +
                        $"  optimize: 1\r\n" +
                        $"{(depth.HasValue ? $"  depth: {depth}\r\n" : "")}";
                case NonCircularSearchMethod.ParticleSwarmSearch:
                    return $"pso search:\r\n" +
                        $"  nvertices: {initialNumberOfSurfaceVertices}\r\n" +
                        $"  nsurf: 1000\r\n" +
                        $"  maxiterations: {maximumIterations}\r\n" +
                        $"  numparticles: {numberOfParticles}\r\n" +
                        $"  optimize: 1\r\n" +
                        $"  convex: 1\r\n" +
                        $"  b_unimodal: 1\r\n" +
                        $"{(depth.HasValue ? $"  depth: {depth}\r\n" : "")}";
                case NonCircularSearchMethod.BlockSearch:
                    return $"block search:\r\n" +
                        $"  samples: {numberOfSurfaces}\r\n" +
                        $"  leftstart: 135\r\n" +
                        $"  leftend: 135\r\n" +
                        $"  rightstart: 45\r\n" +
                        $"  rightend: 45\r\n" +
                        $"  random: 1\r\n" +
                        $"  convex: 1\r\n" +
                        $"  optimize: 1\r\n" +
                        $"  multiple_groups: 0\r\n" +
                        $"{(depth.HasValue ? $"  depth: {depth}\r\n" : "")}";
                case NonCircularSearchMethod.PathSearch:
                    return $"path search:\r\n  " +
                        $"samples: {numberOfSurfaces}\r\n" +
                        $"  random: 1\r\n" +
                        $"  convex: 1\r\n" +
                        $"  optimize: 1\r\n" +
                        $"{(depth.HasValue ? $"  depth: {depth}\r\n" : "")}";
                default:
                    throw new ArgumentOutOfRangeException(nameof(nonCircularSearchMethod), nonCircularSearchMethod, null);
            }
        }
    }
}
