using Domain.Enums;

namespace Domain.Extensions
{
    public static class CircularSearchMethodExtensions
    {
        public static string GetRegexToReplace(this CircularSearchMethod circularSearchMethod)
        {
            switch (circularSearchMethod)
            {
                case CircularSearchMethod.AutoRefineSearch:
                    return "maxcoverage search:((?:.*?\r?\n)+?)(?=\r\n|$)";
                case CircularSearchMethod.GridSearch:
                    return "grid search:((?:.*?\r?\n)+?)(?=\r\n|$)";
                case CircularSearchMethod.SlopeSearch:
                    return "threepoint search:((?:.*?\r?\n)+?)(?=\r\n|$)";
                default:
                    throw new ArgumentOutOfRangeException(nameof(circularSearchMethod), circularSearchMethod, null);
            }
        }

        public static string GetMethodString(this CircularSearchMethod circularSearchMethod, int? divisionsAlongSlope, int? circlesPerDivision, int? numberOfIterations, int? divisionsNextIteration, int? radiusIncrement, int? numberOfSurfaces, double? depth)
        {
            switch (circularSearchMethod)
            {
                case CircularSearchMethod.AutoRefineSearch:
                    return $"maxcoverage search:\r\n" +
                        $"  divisionsalong: {divisionsAlongSlope}\r\n" +
                        $"  circlesperdiv: {circlesPerDivision}\r\n" +
                        $"  iterations: {numberOfIterations}\r\n" +
                        $"  percentkept: {divisionsNextIteration}\r\n" +
                        $"  composite: 1\r\n" +
                        $"{(depth.HasValue ? $"  depth: {depth}\r\n" : "")}";
                case CircularSearchMethod.GridSearch:
                    return $"grid search:\r\n" +
                        $"  rinc: {radiusIncrement}\r\n" +
                        $"  composite: 1\r\n" +
                        $"  reverse_curvature: 1\r\n" +
                        $"{(depth.HasValue ? $"  depth: {depth}\r\n" : "")}";
                case CircularSearchMethod.SlopeSearch:
                    return $"threepoint search:\r\n" +
                        $"  composite: 1\r\n" +
                        $"  reverse_curvature: 1\r\n" +
                        $"  samples: {numberOfSurfaces}\r\n" +
                        $"  random: 1\r\n" +
                        $"  weightlower: 1\r\n" +
                        $"{(depth.HasValue ? $"  depth: {depth}\r\n" : "")}";
                default:
                    throw new ArgumentOutOfRangeException(nameof(circularSearchMethod), circularSearchMethod, null);
            }
        }
    }
}
