using Domain.Enums;

namespace Domain.Extensions
{
    public static class CalculationMethodExtensions
    {
        public static int GetNumber(this CalculationMethod calculationMethod)
        {
            switch (calculationMethod)
            {
                case CalculationMethod.BishopSimplified:
                    return 2;
                case CalculationMethod.CorpsOfEngineers1:
                    return 16;
                case CalculationMethod.CorpsOfEngineers2:
                    return 32;
                case CalculationMethod.GLEOrMorgensternPrice:
                    return 128;
                case CalculationMethod.JanbuSimplified:
                    return 4;
                case CalculationMethod.JanbuCorrected:
                    return 256;
                case CalculationMethod.LoweKarafiath:
                    return 64;
                case CalculationMethod.OrdinaryOrFellenius:
                    return 1;
                case CalculationMethod.Spencer:
                    return 8;
                case CalculationMethod.Sarma:
                    return 512;
                default:
                    throw new ArgumentOutOfRangeException(nameof(calculationMethod), calculationMethod, null);
            }
        }
    }
}
