using Domain.Extensions;

namespace Domain.Report.Vale
{
    public sealed record ValeStabilityFortnight
    {
        public int Month { get; set; }
        public string Fortnight { get; set; }
        public string Name => $"{Fortnight}º quinzena {Month.GetMonthName()}";
        public string DrainedValue { get; set; }
        public string UndrainedValue { get; set; }
        public string PseudoStaticValue { get; set; }
    }
}
