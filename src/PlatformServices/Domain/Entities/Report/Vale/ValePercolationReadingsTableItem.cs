namespace Domain.Report.Vale
{
    public sealed record ValePercolationReadingsTableItem
    {
        public string Instrument { get; set; }
        public bool IsOffline { get; set; }
        public string TopQuotaValue { get; set; }
        public string BaseQuotaValue { get; set; }
        public string MaxReadingValue { get; set; }
        public string LastReadingValue { get; set; }
        public string SafetyFactorNormalValue { get; set; }
        public string SafetyFactorAttentionValue { get; set; }
        public string SafetyFactorAlertValue { get; set; }
        public string SafetyFactorEmergencyValue { get; set; }
    }
}
