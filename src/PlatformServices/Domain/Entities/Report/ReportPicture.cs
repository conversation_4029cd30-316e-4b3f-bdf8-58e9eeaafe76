using Domain.Enums;
using Domain.Extensions;

namespace Domain.Entities.Report
{
    public abstract class ReportPicture
    {
        public string Path { get; set; }
        public int Width { get; set; }
        public int Height { get; set; }

        public abstract string GetCaption();
    };

    public sealed class SimplePicture : ReportPicture
    {
        public override string GetCaption() => string.Empty;
    }

    public sealed class EorPercolationReadingChartPicture : ReportPicture
    {
        public int Index { get; set; }
        public string InstrumentName { get; set; }

        public override string GetCaption()
        {
            return $"Figura 4.{Index} - Dados de leitura de instrumentos de percolação - {InstrumentName}";
        }
    }

    public sealed class EorDisplacementReadingChartPicture : ReportPicture
    {
        public int Index { get; set; }
        public string InstrumentName { get; set; }

        public override string GetCaption()
        {
            return $"Figura 4.{Index} - Dados de leitura de instrumentos de deslocamento - {InstrumentName}";
        }
    }

    public sealed class EorPluviometryAndWaterLevelReadingChartPicture : ReportPicture
    {
        public int Index { get; set; }
        public string SectionName { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }

        public override string GetCaption()
        {
            return $"Figura 4.{Index} - Seção {SectionName} (período {StartDate:d} a {EndDate:d})";
        }
    }

    public sealed class EorStabilityChartPicture : ReportPicture
    {
        public int Index { get; set; }
        public DateTime StartDate { get; init; }
        public DateTime EndDate { get; init; }
        public SoilConditionType SoilConditionType { get; init; }
        public ReportTemplateType Template { get; init; }

        public override string GetCaption() =>
            $"Figura 12.{Index} - Evolução por seção do FS para a condição {SoilConditionType.GetDescription()} entre {StartDate:d} e {EndDate:d}";
    }

    public sealed class EorStabilityHistoryChartPicture : ReportPicture
    {
        public int Index { get; set; }
        public string SectionName { get; init; }
        public DateTime StartDate { get; init; }
        public SoilConditionType SoilConditionType { get; init; }
        public ReportTemplateType Template { get; init; }

        public override string GetCaption()
        {
            var fortnight = StartDate.Day < 15 ? 1 : 2;
            var month = StartDate.ToString("MMMM");

            return Template switch
            {
                ReportTemplateType.Vale => $"Figura 6.{Index} - Condição {SoilConditionType.GetDescription()} - {fortnight}° quinzena de {month}",
                _ => $"Figura 12.{Index} - Condição {SoilConditionType.GetDescription()}"
            };
        }
    }
}
