using Domain.Enums;

namespace Domain.Entities.Report.Mosaic
{
    public sealed record MosaicStabilityTableValue
    {
        public SoilConditionType SoilConditionType { get; set; }
        public decimal MaxValue { get; set; }
        public decimal MinValue { get; set; }
        public decimal AverageValue { get; set; }
        public decimal LastValue { get; set; }
        public bool LastValueHasAlert { get; set; }
        public DateTime LastValueDate { get; set; }
        public string ImagePlaceholder { get; set; }
    }
}
