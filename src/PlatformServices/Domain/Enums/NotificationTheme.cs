namespace Domain.Enums
{
    public enum NotificationTheme
    {
        InstrumentCreated = 1,
        InstrumentUpdated = 2,
        InstrumentDeleted = 3,
        InstrumentDamaged = 4,
        InstrumentRepaired = 5,
        ReadingCreated = 6,
        ReadingUpdated = 7,
        ReadingDeleted = 8,
        ControlLetterNewRegistry = 9,
        SecurityLevelCreated = 10,
        SecurityLevelAboveTolerance = 11,
        OverdueInspection = 12,
        License = 13,
        DrainageAlert = 14,
        StabilityAnalysis = 15,
        StabilityAnalysisCreated = 16,
        StabilityAnalysisUpdated = 17,
        StabilityAnalysisSafetyFactorBelowToleratedLevels = 18,
        StabilityAnalysisWithWarnings = 19,
        AutomatedReading = 20,
        StructureUpdated = 21,
        StructureDeleted = 22,
    }
}