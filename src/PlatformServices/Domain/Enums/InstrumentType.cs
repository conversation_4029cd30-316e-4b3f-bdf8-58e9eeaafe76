using System.ComponentModel;

namespace Domain.Enums
{
    public enum InstrumentType
    {
        [Description("Indicador De Nível De Água")]
        WaterLevelIndicator = 1,
        [Description("Piezômetro De Tubo Aberto")]
        OpenStandpipePiezometer = 2,
        [Description("Piezômetro Elétrico")]
        ElectricPiezometer = 3,
        [Description("Inclinômetro Convencional")]
        ConventionalInclinometer = 4,
        [Description("Inclinômetro IPI")]
        IPIInclinometer = 5,
        [Description("Marco Superficial")]
        SurfaceLandmark = 6,
        [Description("Prisma")]
        Prism = 7,
        [Description("Medidor De Recalque")]
        SettlementGauge = 8,
        [Description("Geofone")]
        Geophone = 9,
        [Description("Régua Linimétrica")]
        LinimetricRuler = 10,
        [Description("Pluviômetro")]
        Pluviometer = 12,
        [Description("Pluviógrafo")]
        Pluviograph = 13
    }
}
