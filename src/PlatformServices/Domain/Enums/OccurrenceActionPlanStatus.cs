using System.ComponentModel;

namespace Domain.Enums
{
    public enum OccurrenceActionPlanStatus
    {
        [Description("Sem plano de ação")]
        HasNoActionPlan = 1,          // Black
        [Description("Concluído")]
        Completed = 2,                // Gray
        [Description("Pendente há mais de 3 dias")]
        PendingMoreThanThreeDays = 3, // Green
        [Description("Pendente há 3 dias ou menos")]
        PendingThreeDaysOrLess = 4,   // Yellow
        [Description("Vencido")]
        Overdue = 5,                  // Red
        [Description("Vencido e recorrente")]
        OverdueAndRecurring = 6       // Red and Blinking
    }
}
