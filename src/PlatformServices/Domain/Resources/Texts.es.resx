<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AcceptedTermsEmailBody" xml:space="preserve">
    <value>Estimado {0},
             Usted aceptó los términos de uso de Logisoil el {1} ​​y serán válidos hasta
             nueva revisión de las condiciones de uso. Estos términos pueden ser modificados por el
             equipo Logisoil sin previo aviso, en este caso,
             recibirás un nuevo correo electrónico para aceptar los cambios.</value>
  </data>
  <data name="AcceptedTermsEmailTitle" xml:space="preserve">
    <value>Términos y condiciones de Logisoil</value>
  </data>
  <data name="EoRReportEmailBody" xml:space="preserve">
    <value>¡Hola!

Estás recibiendo el informe de seguimiento de {0}, para el periodo {1} al {2} de la estructura {3}/{4}.

Tuyo sinceramente,

Equipo Logisoil</value>
  </data>
  <data name="ExpiringContractualPeriodBody" xml:space="preserve">
    <value>El período de contrato del cliente {0} vencerá en {1} día(s). Póngase en contacto con un administrador para renovar el período.</value>
  </data>
  <data name="ExpiringContractualPeriodTitle" xml:space="preserve">
    <value>El período de contrato de su cliente está por vencer</value>
  </data>
  <data name="ExpiringTrialPeriodBody" xml:space="preserve">
    <value>El período de prueba del cliente {0} expirará en {1} día(s). Póngase en contacto con un administrador para renovar el período.</value>
  </data>
  <data name="ExpiringTrialPeriodTitle" xml:space="preserve">
    <value>El período de prueba de su cliente está por vencer</value>
  </data>
  <data name="OccurrenceListReportEmailTitle" xml:space="preserve">
    <value>Informe de listado de ocurrencias</value>
  </data>
  <data name="EoRReportEmailTitle" xml:space="preserve">
    <value>Informe de seguimiento de Logisoil</value>
  </data>
  <data name="EoRReportNoDataEmailTitle" xml:space="preserve">
    <value>Ausencia de Datos para el Informe de Seguimiento de Logisoil</value>
  </data>
  <data name="EoRReportNoDataEmailBody" xml:space="preserve">
    <value>No fue posible generar el informe de seguimiento de {0}, correspondiente al período de {1} a {2}, debido a la falta de datos disponibles en este período.

Si necesitas más información, por favor contacta con el soporte.</value>
  </data>
  <data name="OccurrenceListReportEmailBody" xml:space="preserve">
    <value>¡Hola! &lt;br/&gt;

Está recibiendo el informe de incidentes que se solicitó en {0}, de las estructuras {1}.

Saludos cordiales,&lt;br/&gt;

Equipo de Logisoil</value>
  </data>
</root>