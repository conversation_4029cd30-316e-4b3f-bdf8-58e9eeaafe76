<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Dapper" Version="2.1.21" />
		<PackageReference Include="Dapper.SqlBuilder" Version="2.0.78" />
		<PackageReference Include="FluentMigrator" Version="3.3.1" />
		<PackageReference Include="FluentMigrator.Runner" Version="3.3.1" />
		<PackageReference Include="FluentMigrator.Runner.SqlServer" Version="3.3.1" />
		<PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\Domain\Domain.csproj" />
	</ItemGroup>

</Project>
