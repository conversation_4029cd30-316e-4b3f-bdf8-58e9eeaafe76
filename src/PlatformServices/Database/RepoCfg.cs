using Database.Repositories.AutomatedReadingVariable;
using Microsoft.Extensions.DependencyInjection;

namespace Database
{
    public static class RepoCfg
    {
        public static IServiceCollection AddRepos(
            this IServiceCollection services,
            string connectionString)
        {
            services.AddSingleton<IAutomatedReadingVariableRepository>(x =>
                new AutomatedReadingVariableRepository(connectionString));

            return services;
        }
    }
}
