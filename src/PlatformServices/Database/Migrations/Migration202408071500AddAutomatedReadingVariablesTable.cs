using FluentMigrator;

namespace Database.Migrations
{
    [Migration(************)]
    public class Migration************AddAutomatedReadingVariablesTable : Migration
    {
        public override void Up()
        {
            Create.Table("automated-reading-variables")
                .WithColumn("id")
                    .AsGuid()
                    .PrimaryKey("pk-automated-reading-variables")
                .WithColumn("created-date")
                    .AsDateTime2()
                    .NotNullable()
                .WithColumn("client-id")
                    .AsGuid()
                    .NotNullable()
                .WithColumn("variable-name")
                    .AsAnsiString(255)
                    .NotNullable()
                .WithColumn("variable-value")
                    .AsCustom("VARCHAR(MAX)");

            Create
                .Index("idx-automated-reading-variables-client-id")
                .OnTable("automated-reading-variables")
                .OnColumn("client-id");

            Create
                .Index("idx-automated-reading-variables-variable-name")
                .OnTable("automated-reading-variables")
                .OnColumn("variable-name");
        }

        public override void Down()
        {
            Delete
                .Index("idx-automated-reading-variables-client-id")
                .OnTable("automated-reading-variables");

            Delete
                .Index("idx-automated-reading-variables-variable-name")
                .OnTable("automated-reading-variables");

            Delete.Table("clients");
        }
    }
}
