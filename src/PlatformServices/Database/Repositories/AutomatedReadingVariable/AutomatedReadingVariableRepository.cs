using Dapper;
using System.Data;

namespace Database.Repositories.AutomatedReadingVariable
{
    public sealed class AutomatedReadingVariableRepository : IAutomatedReadingVariableRepository
    {
        private readonly string _connectionString;

        public AutomatedReadingVariableRepository(
            string connectionString)
        {
            _connectionString = connectionString;
        }

        public async Task<T> GetVariableValueAsync<T>(
            string variableName,
            Guid clientId)
        {
            var parameters = new DynamicParameters();
            parameters.Add("@variableName", variableName, DbType.AnsiString, size: 255);
            parameters.Add("@clientId", clientId, DbType.Guid);

            await using var connection = ApplicationDatabase
                .GetConnection(_connectionString);
            await connection.OpenAsync();

            return await connection.QueryFirstOrDefaultAsync<T>(
                sql: Queries.GetVariableValue,
                param: parameters);
        }

        public async Task InsertAsync(
            Domain.Entities.AutomatedReadingVariable entity)
        {
            await using var connection = ApplicationDatabase
                .GetConnection(_connectionString);
            await connection.OpenAsync();

            await connection
                .ExecuteAsync(
                sql: Queries.Insert,
                param: entity);
        }

        public async Task UpdateAsync(
    Domain.Entities.AutomatedReadingVariable entity)
        {
            await using var connection = ApplicationDatabase
                .GetConnection(_connectionString);
            await connection.OpenAsync();

            await connection
                .ExecuteAsync(
                sql: Queries.Update,
                param: entity);
        }
    }
}