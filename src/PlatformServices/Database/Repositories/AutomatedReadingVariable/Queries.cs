namespace Database.Repositories.AutomatedReadingVariable
{
    internal static class Queries
    {
        internal const string GetVariableValue = @"
            SELECT
                [variable-value]
            FROM [automated-reading-variables]
            WHERE
                [variable-name] = @variableName
                AND [client-id] = @clientId
            ORDER BY [created-date] DESC";

        internal const string Insert = @"
            INSERT INTO [dbo].[automated-reading-variables]
                ([id]
                ,[created-date]
                ,[client-id]
                ,[variable-name]
                ,[variable-value])
            VALUES
                (@Id
                ,@CreatedDate
                ,@ClientId
                ,@VariableName
                ,@VariableValue)";

        internal const string Update = @"
            UPDATE [dbo].[automated-reading-variables]
            SET
                [variable-value] = @VariableValue
            WHERE
                [variable-name] = @VariableName AND [client-id] = @ClientId";
    }
}