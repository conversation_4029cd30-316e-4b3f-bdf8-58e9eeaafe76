using Domain.Enums;
using Domain.Extensions;

namespace Domain.Tests.Extensions.SliFileTypeExtensions;

[Trait("SliFileTypeExtensions", "MapSoilConditionType")]
public class MapSoilConditionTypeTests
{
    [Theory(DisplayName =
        "When SliFileType contains Drained, then should return Drained")]
    [InlineData(SliFileType.CircularDrained)]
    [InlineData(SliFileType.NonCircularDrained)]
    public void WhenSliFileTypeContainsDrained_ShouldReturnDrained(
        SliFileType fileType)
    {
        const SoilConditionType expected = SoilConditionType.Drained;

        var result = fileType.MapSoilConditionType();

        result.Should().Be(expected);
    }
    
    [Theory(DisplayName =
        "When SliFileType contains Undrained, then should return Undrained")]
    [InlineData(SliFileType.CircularUndrained)]
    [InlineData(SliFileType.NonCircularUndrained)]
    public void WhenSliFileTypeContainsUndrained_ShouldReturnUndrained(
        SliFileType fileType)
    {
        const SoilConditionType expected = SoilConditionType.Undrained;

        var result = fileType.MapSoilConditionType();

        result.Should().Be(expected);
    }
}