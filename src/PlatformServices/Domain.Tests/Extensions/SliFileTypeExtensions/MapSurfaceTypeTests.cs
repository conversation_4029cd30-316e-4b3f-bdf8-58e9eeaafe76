using Domain.Enums;
using Domain.Extensions;

namespace Domain.Tests.Extensions.SliFileTypeExtensions;

[Trait("SliFileTypeExtensions", "MapSurfaceType")]
public class MapSurfaceTypeTests
{
    [Theory(DisplayName =
        "When SliFileType contains Circular, then should return Circular")]
    [InlineData(SliFileType.CircularDrained)]
    [InlineData(SliFileType.CircularUndrained)]
    [InlineData(SliFileType.CircularPseudoStatic)]
    public void WhenSliFileTypeContainsCircular_ShouldReturnCircular(
        SliFileType fileType)
    {
        const SurfaceType expected = SurfaceType.Circular;

        var result = fileType.MapSurfaceType();

        result.Should().Be(expected);
    }
    
    [Theory(DisplayName =
        "When SliFileType contains NonCircular, then should return NonCircular")]
    [InlineData(SliFileType.NonCircularDrained)]
    [InlineData(SliFileType.NonCircularUndrained)]
    [InlineData(SliFileType.NonCircularPseudoStatic)]
    public void WhenSliFileTypeContainsNonCircular_ShouldReturnNonCircular(
        SliFileType fileType)
    {
        const SurfaceType expected = SurfaceType.NonCircular;

        var result = fileType.MapSurfaceType();

        result.Should().Be(expected);
    }
}