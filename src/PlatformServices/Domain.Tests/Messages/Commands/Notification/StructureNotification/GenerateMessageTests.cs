using Domain.Enums;

namespace Domain.Tests.Messages.Commands.Notification.StructureNotification;

[Trait("StructureNotification", "GenerateMessage")]
public class GenerateMessageTests
{
    [Fact(DisplayName = "When theme is StructureUpdated, should return correct message")]
    public void WhenThemeIsStructureUpdated_ShouldReturnCorrectMessage()
    {
        const string expectedMessage = "A estrutura Estrutura A da unidade Unidade 1 sofreu alteração em 01/10/2023 12:00 pelo usuário Usuário X.";
        
        var notification = new Domain.Messages.Commands.Notification.StructureNotification
        {
            Theme = NotificationTheme.StructureUpdated,
            AuxiliaryData = new Dictionary<string, string>
            {
                { "StructureName", "Estrutura A" },
                { "ClientUnitName", "Unidade 1" },
                { "ModifiedBy", "Usuário X" }
            },
            CreatedDate = new DateTime(2023, 10, 1, 12, 0, 0),
        };

        var message = notification.GenerateMessage();

        message.Should().Be(expectedMessage);
    }
    
    [Fact(DisplayName = "When theme is StructureDeleted, should return correct message")]
    public void WhenThemeIsStructureDeleted_ShouldReturnCorrectMessage()
    {
        const string expectedMessage = "A estrutura Estrutura B da unidade Unidade 2 foi inativada em 01/10/2023 12:00 pelo usuário Usuário Y.";
        
        var notification = new Domain.Messages.Commands.Notification.StructureNotification
        {
            Theme = NotificationTheme.StructureDeleted,
            AuxiliaryData = new Dictionary<string, string>
            {
                { "StructureName", "Estrutura B" },
                { "ClientUnitName", "Unidade 2" },
                { "ModifiedBy", "Usuário Y" }
            },
            CreatedDate = new DateTime(2023, 10, 1, 12, 0, 0),
        };

        var message = notification.GenerateMessage();

        message.Should().Be(expectedMessage);
    }
    
    [Fact(DisplayName = "When theme is unknown, should return default message")]
    public void WhenThemeIsUnknown_ShouldReturnDefaultMessage()
    {
        const string expectedMessage = "Ação desconhecida para a estrutura Estrutura C da unidade Unidade 3 em 01/10/2023 12:00 pelo usuário Usuário Z.";
        
        var notification = new Domain.Messages.Commands.Notification.StructureNotification
        {
            Theme = (NotificationTheme)999, // Unknown theme
            AuxiliaryData = new Dictionary<string, string>
            {
                { "StructureName", "Estrutura C" },
                { "ClientUnitName", "Unidade 3" },
                { "ModifiedBy", "Usuário Z" }
            },
            CreatedDate = new DateTime(2023, 10, 1, 12, 0, 0),
        };

        var message = notification.GenerateMessage();

        message.Should().Be(expectedMessage);
    }
}