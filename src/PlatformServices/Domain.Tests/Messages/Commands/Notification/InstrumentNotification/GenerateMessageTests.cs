using Domain.Enums;

namespace Domain.Tests.Messages.Commands.Notification.InstrumentNotification;

[Trait("InstrumentNotification", "GenerateMessage")]
public class GenerateMessageTests
{
    [Fact(DisplayName = "When theme is InstrumentCreated, should return correct message")]
    public void WhenThemeIsInstrumentCreated_ShouldReturnCorrectMessage()
    {
        const string expectedMessage = "O instrumento Instrumento A foi cadastrado na estrutura Estrutura 1 em 01/10/2023 12:00, pelo usuário Usuário X.";
        
        var notification = new Domain.Messages.Commands.Notification.InstrumentNotification
        {
            Theme = NotificationTheme.InstrumentCreated,
            AuxiliaryData = new Dictionary<string, string>
            {
                { "InstrumentName", "Instrumento A" },
                { "StructureName", "Estrutura 1" },
                { "ModifiedBy", "Usuário X" }
            },
            CreatedDate = new DateTime(2023, 10, 1, 12, 0, 0),
        };

        var message = notification.GenerateMessage();

        message.Should().Be(expectedMessage);
    }

    [Fact(DisplayName = "When theme is InstrumentUpdated, should return correct message")]
    public void WhenThemeIsInstrumentUpdated_ShouldReturnCorrectMessage()
    {
        const string expectedMessage = "O instrumento Instrumento B da estrutura Estrutura 2 sofreu alteração em 01/10/2023 12:00, pelo usuário Usuário Y. @12345";
        
        var notification = new Domain.Messages.Commands.Notification.InstrumentNotification
        {
            Theme = NotificationTheme.InstrumentUpdated,
            AuxiliaryData = new Dictionary<string, string>
            {
                { "InstrumentName", "Instrumento B" },
                { "StructureName", "Estrutura 2" },
                { "ModifiedBy", "Usuário Y" },
                { "InstrumentId", "12345" }
            },
            CreatedDate = new DateTime(2023, 10, 1, 12, 0, 0),
        };

        var message = notification.GenerateMessage();

        message.Should().Be(expectedMessage);
    }

    [Fact(DisplayName = "When theme is InstrumentDeleted, should return correct message")]
    public void WhenThemeIsInstrumentDeleted_ShouldReturnCorrectMessage()
    {
        const string expectedMessage = "O instrumento Instrumento C da estrutura Estrutura 3 foi inativado em 01/10/2023 12:00, pelo usuário Usuário Z.";
        
        var notification = new Domain.Messages.Commands.Notification.InstrumentNotification
        {
            Theme = NotificationTheme.InstrumentDeleted,
            AuxiliaryData = new Dictionary<string, string>
            {
                { "InstrumentName", "Instrumento C" },
                { "StructureName", "Estrutura 3" },
                { "ModifiedBy", "Usuário Z" }
            },
            CreatedDate = new DateTime(2023, 10, 1, 12, 0, 0),
        };

        var message = notification.GenerateMessage();

        message.Should().Be(expectedMessage);
    }

    [Fact(DisplayName = "When theme is unknown, should return default message")]
    public void WhenThemeIsUnknown_ShouldReturnDefaultMessage()
    {
        const string expectedMessage = "Ação desconhecida para o instrumento Instrumento D da estrutura Estrutura 4 em 01/10/2023 12:00, pelo usuário Usuário W.";
        
        var notification = new Domain.Messages.Commands.Notification.InstrumentNotification
        {
            Theme = (NotificationTheme)999, // Unknown theme
            AuxiliaryData = new Dictionary<string, string>
            {
                { "InstrumentName", "Instrumento D" },
                { "StructureName", "Estrutura 4" },
                { "ModifiedBy", "Usuário W" }
            },
            CreatedDate = new DateTime(2023, 10, 1, 12, 0, 0),
        };

        var message = notification.GenerateMessage();

        message.Should().Be(expectedMessage);
    }
}