using Domain.Messages.Commands.InspectionSheet;
using Domain.Messages.Commands.Notification;
using Domain.Messages.Commands.Package;
using Domain.Messages.Commands.Section;
using Domain.Messages.Commands.Simulation;
using Domain.Messages.Events.Client;
using Domain.Messages.Events.OccurrenceListReport;
using Domain.Messages.Events.Reading;
using Domain.Messages.Events.User;
using Elsa.Activities.MassTransit.Consumers;
using MassTransit;

namespace Api.Configuration;

public static class MassTransitCfg
{
    public static IServiceCollection AddMassTransit(
        this IServiceCollection services,
        string connectionString)
    {
        services
            .AddMassTransit(registrationConfigurator =>
            {
                registrationConfigurator
                    .AddWorkflowConsumers()
                    .SetKebabCaseEndpointNameFormatter();

                registrationConfigurator.UsingRabbitMq((registrationContext, factoryConfigurator) =>
                {
                    factoryConfigurator.Host(connectionString);
                    factoryConfigurator.ConfigureEndpoints(registrationContext);
                });
            });

        return services;
    }

    private static IBusRegistrationConfigurator AddWorkflowConsumers(
        this IBusRegistrationConfigurator configurator)
    {
        configurator.AddConsumer<WorkflowConsumer<DisabledClient>>(
            consumerConfigurator => consumerConfigurator.UseConcurrencyLimit(1));
            
        configurator.AddConsumer<WorkflowConsumer<AcceptedTerm>>(
            consumerConfigurator => consumerConfigurator.UseConcurrencyLimit(1));
            
        configurator.AddConsumer<WorkflowConsumer<ReadingCreated>>(
            consumerConfigurator => consumerConfigurator.UseConcurrencyLimit(10));
            
        configurator.AddConsumer<WorkflowConsumer<ReadingUpdated>>(
            consumerConfigurator => consumerConfigurator.UseConcurrencyLimit(5));
            
        configurator.AddConsumer<WorkflowConsumer<CalculateStability>>(
            consumerConfigurator => consumerConfigurator.UseConcurrencyLimit(5));
            
        configurator.AddConsumer<WorkflowConsumer<CreateSliFile>>(
            consumerConfigurator => consumerConfigurator.UseConcurrencyLimit(5));
            
        configurator.AddConsumer<WorkflowConsumer<StartSimulation>>(
            consumerConfigurator => consumerConfigurator.UseConcurrencyLimit(5));

        configurator.AddConsumer<WorkflowConsumer<CreateNotification>>(
            consumerConfigurator => consumerConfigurator.UseConcurrencyLimit(5));

        configurator.AddConsumer<WorkflowConsumer<OccurrenceListReportCreated>>(
            consumerConfigurator => consumerConfigurator.UseConcurrencyLimit(1));

        configurator.AddConsumer<WorkflowConsumer<TranscribeInspectionSheetVoiceNotes>>(
            consumerConfigurator => consumerConfigurator.UseConcurrencyLimit(1));

        return configurator;
    }
}