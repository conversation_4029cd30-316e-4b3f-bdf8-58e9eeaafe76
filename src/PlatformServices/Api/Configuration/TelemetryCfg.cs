using System.Diagnostics;
using Application.Observability;
using Application.Observability.Metrics;
using Azure.Monitor.OpenTelemetry.AspNetCore;
using OpenTelemetry.Logs;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;

namespace Api.Configuration;

public static class TelemetryCfg
{
    public static IServiceCollection AddTelemetry(
        this IServiceCollection services,
        AzureMonitorOptions azureMonitorOptions)
    {
        services
            .AddMetrics()
            .AddSingleton<StabilityAnalysisMetricHandler>()
            .AddOpenTelemetry()
            .UseAzureMonitor(options =>
            {
                options.ConnectionString = azureMonitorOptions.ConnectionString;
                options.SamplingRatio = azureMonitorOptions.SamplingRatio;
                options.EnableLiveMetrics = azureMonitorOptions
                    .EnableLiveMetrics;
                options.DisableOfflineStorage = azureMonitorOptions
                    .DisableOfflineStorage;
            })
            .ConfigureResource(resourceBuilder => resourceBuilder.AddService(
                ApplicationConstants.ServiceName,
                ApplicationConstants.ServiceVersion)
            )
            .WithTracing(traceBuilder => traceBuilder
                .SetSampler<AlwaysOffSampler>()
                .AddOtlpExporter()
            )
            .WithMetrics(metricsBuilder => metricsBuilder
                .AddMeter(ApplicationConstants.ServiceName)
                .AddHttpClientInstrumentation()
                .AddOtlpExporter()
            )
            .WithLogging(loggingBuilder => loggingBuilder
                .AddConsoleExporter()
                .AddOtlpExporter()
            );

        return services;
    }
}