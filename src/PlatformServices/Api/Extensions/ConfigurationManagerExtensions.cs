using Azure.Extensions.AspNetCore.Configuration.Secrets;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;

namespace Api.Extensions
{
    public static class ConfigurationManagerExtensions
    {
        public static ConfigurationManager ConfigureKeyVault(this ConfigurationManager configurationManager)
        {
            var kvURL = configurationManager["Vault:Url"];
            var tenantId = configurationManager["Vault:TenantId"];
            var clientId = configurationManager["Vault:ClientId"];
            var clientSecret = configurationManager["Vault:ClientSecret"];

            var credential = new ClientSecretCredential(tenantId, clientId, clientSecret);

            var client = new SecretClient(new(kvURL), credential);

            configurationManager.AddAzureKeyVault(client, new AzureKeyVaultConfigurationOptions());

            return configurationManager;
        }
    }
}
