<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>2ae9ba6f-44f8-4b8c-82e1-2f354375ad94</UserSecretsId>
    <GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.2.2" />
    <PackageReference Include="Azure.Identity" Version="1.10.4" />
    <PackageReference Include="Azure.Monitor.OpenTelemetry.AspNetCore" Version="1.2.0" />
    <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.4.0" />
    <PackageReference Include="Elsa.Activities.MassTransit" Version="2.14.1" />
    <PackageReference Include="Elsa.Designer.Components.Web" Version="2.14.1" />
    <PackageReference Include="MassTransit" Version="8.0.9" />
    <PackageReference Include="NewRelic.LogEnrichers.Serilog" Version="1.0.1" />
    <PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.9.0" />
    <PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.9.0" />
    <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.9.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.9.0" />
    <PackageReference Include="Serilog" Version="2.10.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="5.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.3.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Application\Application.csproj" />
    <ProjectReference Include="..\Domain\Domain.csproj" />
    <ProjectReference Include="..\Workflow\Workflow.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\images\" />
    <Folder Include="wwwroot\report\results\" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="wwwroot\report\templates\eor_mosaic.docx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="Pages\_Host.cshtml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Pages\_Host.cshtml.cs">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
  </ItemGroup>

</Project>
