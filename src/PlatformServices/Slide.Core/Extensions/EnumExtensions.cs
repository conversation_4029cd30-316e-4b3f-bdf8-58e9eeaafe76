namespace Slide.Core.Extensions
{
    public static class EnumExtensions
    {
        public static string GetDescription(this Enum value)
        {
            var field = value.GetType().GetField(value.ToString());

            var attribute = field.GetCustomAttributes(false).OfType<System.ComponentModel.DescriptionAttribute>().FirstOrDefault();

            return attribute != null ? attribute.Description : value.ToString();
        }
    }
}
