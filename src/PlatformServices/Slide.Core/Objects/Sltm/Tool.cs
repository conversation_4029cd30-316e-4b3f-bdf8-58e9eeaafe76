using Geometry.Core;
using Slide.Core.Enums;
using Slide.Core.Extensions;
using Slide.Core.Objects._Shared;
using System.Drawing;

namespace Slide.Core.Objects.Sltm
{
    public class Tool
    {
        public ToolType ToolType { get; set; }
        public Guid Guid { get; set; } = Guid.NewGuid();
        public int PenLineStyle { get; set; } = 0;
        public bool TextOutline { get; set; } = true;
        public Color TextColor { get; set; } = Color.FromArgb(0, 0, 0);
        public int RotationDegreesCCW { get; set; } = 0;
        public int TextDimTextFlag { get; set; } = 12231;
        public bool IsDimensionUnits { get; set; } = false;
        public bool IsExteriorLine { get; set; } = true;
        public int ConvertToInches { get; set; } = 0;
        public bool SwapArrows { get; set; } = false;
        public int NumDecimalPlaces { get; set; } = 1;
        public bool IsBehindTextLine { get; set; } = false;
        public bool IsExtensionLine { get; set; } = false;
        public bool IsDimensionPrimaryLine { get; set; } = false;
        public int DimensionStyle { get; set; } = 12101;
        public bool ImageBehindGeometry { get; set; } = false;
        public string FileName { get; set; } = "NO_FILE_LOADED";
        public string PathName { get; set; } = "NO_PATH_LOADED";
        public Color TransparentColor { get; set; } = Color.FromArgb(255, 255, 255);
        public bool MoveLocked { get; set; } = false;
        public bool PseudoTransparent { get; set; } = false;
        public bool Transparent { get; set; } = false;
        public bool Alpha { get; set; } = false;
        public int AlphaPercent { get; set; } = 20;
        public int XAxisSpacing { get; set; } = 1;
        public int YAxisSpacing { get; set; } = 1;
        public bool IsAxisSpacing { get; set; } = false;
        public int ArcBegin { get; set; } = 0;
        public int ArcEnd { get; set; } = 360;
        public bool IsArc { get; set; } = false;
        public bool IsCircle { get; set; } = false;
        public bool ArrowAtHead { get; set; } = false;
        public bool ArrowAtTail { get; set; } = false;
        public bool ShowVertices { get; set; } = false;
        public bool AxisLeft { get; set; } = false;
        public bool AxisRight { get; set; } = false;
        public bool AxisTop { get; set; } = false;
        public bool AxisBottom { get; set; } = false;
        public bool AxisGlobalCoords { get; set; } = true;
        public bool IsHatchedInterior { get; set; } = false;
        public int HatchStyleIndex { get; set; } = 2;
        public bool IsFilled { get; set; } = false;
        public int LineWeight { get; set; } = 3;
        public Color FillColor { get; set; } = Color.FromArgb(0, 0, 255);
        public Color EntityColor { get; set; } = Color.FromArgb(0, 0, 94);
        public PointD Start { get; set; }
        public PointD End { get; set; }
        public int FontSize { get; set; } = 10;
        public string FontLogFont { get; set; } = "0, 0, 0, 0, 400, 0, 0, 0, 0, 0, 0, 0, 0 lfFaceName: Arial";
        public bool ScaleWithZoom { get; set; } = true;
        public int ToolOrderWithinLayer { get; set; }
        public bool LockAspectRatio { get; set; } = true;
        public int TextAlign { get; set; } = 288;
        public string AssignedViewGuid { get; set; } = "";
        public int AssignedStage { get; set; } = -1;
        public bool Visibility { get; set; } = true;
        public DPointArrayStart VerticesStart { get; set; } = new();
        public int NumTextLines { get; set; } = 1;
        public string Text1 { get; set; } = "";

        public override string ToString()
        {
            var verticesStartString = VerticesStart.ToString();
            verticesStartString = verticesStartString.Replace("\r\n", "\r\n  ");

            return $"    tool type: \"{ToolType.GetDescription()}\"\r\n" +
                   $"    guid: \"{{{Guid}}}\"\r\n" +
                   $"    pen_line_style: {PenLineStyle}\r\n" +
                   $"    text_outline: {(TextOutline ? "yes" : "no")}\r\n" +
                   $"    text_color: {TextColor.R}, {TextColor.G}, {TextColor.B}\r\n" +
                   $"    rotation_degrees_ccw: {RotationDegreesCCW}\r\n" +
                   $"    text dim_text_flag: {TextDimTextFlag}\r\n" +
                   $"    is_dimension_units: {(IsDimensionUnits ? "yes" : "no")}\r\n" +
                   $"    is_exterior_line: {(IsExteriorLine ? "yes" : "no")}\r\n" +
                   $"    convert_to_inches: {ConvertToInches}\r\n" +
                   $"    swap_arrows: {(SwapArrows ? "yes" : "no")}\r\n" +
                   $"    num_decimal_places: {NumDecimalPlaces}\r\n" +
                   $"    is_behind_text_line: {(IsBehindTextLine ? "yes" : "no")}\r\n" +
                   $"    is_extension_line: {(IsExtensionLine ? "yes" : "no")}\r\n" +
                   $"    is_dimension_primary_line: {(IsDimensionPrimaryLine ? "yes" : "no")}\r\n" +
                   $"    dimension_style: {DimensionStyle}\r\n" +
                   $"    image_behind_geometry: {(ImageBehindGeometry ? "yes" : "no")}\r\n" +
                   $"    file_name: \"{FileName}\"\r\n" +
                   $"    path_name: \"{PathName}\"\r\n" +
                   $"    transparent_color: {TransparentColor.R}, {TransparentColor.G}, {TransparentColor.B}\r\n" +
                   $"    move_locked: {(MoveLocked ? "yes" : "no")}\r\n" +
                   $"    pseudo_transparent: {(PseudoTransparent ? "yes" : "no")}\r\n" +
                   $"    transparent: {(Transparent ? "yes" : "no")}\r\n" +
                   $"    alpha: {(Alpha ? "yes" : "no")}\r\n" +
                   $"    alpha_percent: {AlphaPercent}\r\n" +
                   $"    x_axis_spacing: {XAxisSpacing}\r\n" +
                   $"    y_axis_spacing: {YAxisSpacing}\r\n" +
                   $"    is_axis_spacing: {(IsAxisSpacing ? "yes" : "no")}\r\n" +
                   $"    arc_begin: {ArcBegin}\r\n" +
                   $"    arc_end: {ArcEnd}\r\n" +
                   $"    is_arc: {(IsArc ? "yes" : "no")}\r\n" +
                   $"    is_circle: {(IsCircle ? "yes" : "no")}\r\n" +
                   $"    arrow_at_head: {(ArrowAtHead ? "yes" : "no")}\r\n" +
                   $"    arrow_at_tail: {(ArrowAtTail ? "yes" : "no")}\r\n" +
                   $"    show_vertices: {(ShowVertices ? "yes" : "no")}\r\n" +
                   $"    axis_left: {(AxisLeft ? "yes" : "no")}\r\n" +
                   $"    axis_right: {(AxisRight ? "yes" : "no")}\r\n" +
                   $"    axis_top: {(AxisTop ? "yes" : "no")}\r\n" +
                   $"    axis_bottom: {(AxisBottom ? "yes" : "no")}\r\n" +
                   $"    axis_global_coords: {(AxisGlobalCoords ? "yes" : "no")}\r\n" +
                   $"    is_hatched_interior: {(IsHatchedInterior ? "yes" : "no")}\r\n" +
                   $"    hatch_style_index: {HatchStyleIndex}\r\n" +
                   $"    is_filled: {(IsFilled ? "yes" : "no")}\r\n" +
                   $"    line_weight: {LineWeight}\r\n" +
                   $"    fill_color: {FillColor.R}, {FillColor.G}, {FillColor.B}\r\n" +
                   $"    entity_color: {EntityColor.R}, {EntityColor.G}, {EntityColor.B}\r\n" +
                   $"    start: {Start.X}, {Start.Y}\r\n" +
                   $"    end: {End.X}, {End.Y}\r\n" +
                   $"    font_size: {FontSize}\r\n" +
                   $"    font_logfont: {FontLogFont}\r\n" +
                   $"    scale_with_zoom: {(ScaleWithZoom ? "yes" : "no")}\r\n" +
                   $"    tool order within layer: {ToolOrderWithinLayer}\r\n" +
                   $"    lock aspect ratio: {(LockAspectRatio ? "yes" : "no")}\r\n" +
                   $"    text_align: {TextAlign}\r\n" +
                   $"    assigned_view_guid: \"{AssignedViewGuid}\"\r\n" +
                   $"    assigned_stage: {AssignedStage}\r\n" +
                   $"    visibility: {(Visibility ? "yes" : "no")}\r\n" +
                   $"    vertices start:{verticesStartString}\r\n" +
                   $"    vertices end:\r\n" +
                   $"    num_text_lines: {NumTextLines}\r\n" +
                   $"    text 1: \"{Text1}\"";
        }
    }
}
