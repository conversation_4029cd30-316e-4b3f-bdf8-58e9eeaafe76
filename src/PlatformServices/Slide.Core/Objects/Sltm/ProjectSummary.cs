namespace Slide.Core.Objects.Sltm
{
    public class ProjectSummary
    {
        public string ProjectTitle { get; set; } = "Stability analysis";
        public string Analysis { get; set; } = "1";
        public string Author { get; set; } = "Logisoil";
        public string DateCreated { get; set; } = $"{DateTime.UtcNow}";
        public string Comments1 { get; set; } = "This file was automatically created by Logisoil.";
        public string Comments2 { get; set; } = "";
        public string Comments3 { get; set; } = "";
        public string Comments4 { get; set; } = "";
        public string Comments5 { get; set; } = "";
        public string Company { get; set; } = "Walm Engenharia";

        public override string ToString()
        {
            return $"  project_title: \"{ProjectTitle}\"\r\n" +
                   $"  analysis: \"{Analysis}\"\r\n" +
                   $"  author: \"{Author}\"\r\n" +
                   $"  date_created: \"{DateCreated}\"\r\n" +
                   $"  comments1: \"{Comments1}\"\r\n" +
                   $"  comments2: \"{Comments2}\"\r\n" +
                   $"  comments3: \"{Comments3}\"\r\n" +
                   $"  comments4: \"{Comments4}\"\r\n" +
                   $"  comments5: \"{Comments5}\"\r\n" +
                   $"  company: \"{Company}\"";
        }
    }
}
