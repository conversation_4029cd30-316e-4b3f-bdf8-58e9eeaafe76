using System.Text;

namespace Slide.Core.Objects.Sltm
{
    public class ToolsData
    {
        public List<Tool> Tools { get; set; } = new();

        public override string ToString()
        {
            var sb = new StringBuilder();
            sb.AppendLine($"\r\ntools data start:\r\n  num tools: {Tools.Count}");

            for (int i = 0; i < Tools.Count; i++)
            {
                sb.AppendLine($"  tool {i + 1} start:\r\n{Tools[i].ToString()}\r\n  tool {i + 1} end:");
            }

            sb.AppendLine("tools data end:");

            return sb.ToString();
        }
    }
}
