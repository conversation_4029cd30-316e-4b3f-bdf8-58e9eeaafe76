using Slide.Core.Objects.Sli;

namespace Slide.Core.Objects._Shared
{
    public class DPointArrayStart
    {
        public List<DPoint> DPoints { get; set; } = new();

        public DPointArrayStart()
        {

        }

        public DPointArrayStart(DPointArrayStart dPointArrayStart)
        {
            DPoints = dPointArrayStart.DPoints.Select(dPoint => new DPoint(dPoint)).ToList();
        }

        public override string ToString()
        {
            var text = $"\r\n    dpoint array start:\r\n      num points: {DPoints.Count}";

            foreach (var dPoint in DPoints)
            {
                text += $"\r\n      {dPoint.Index}: {dPoint.X}, {dPoint.Y}";
            }

            text += "\r\n    dpoint array end:";

            return text;
        }
    }
}
