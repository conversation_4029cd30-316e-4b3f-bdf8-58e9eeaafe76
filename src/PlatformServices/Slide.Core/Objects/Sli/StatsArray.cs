namespace Slide.Core.Objects.Sli
{
    public class StatsArray
    {
        public double Value1 { get; set; }
        public double Value2 { get; set; }
        public double? Value3 { get; set; }
        public double? Value4 { get; set; }
        public bool? IsOn { get; set; }

        public StatsArray()
        {

        }

        public StatsArray(StatsArray statsArray)
        {
            Value1 = statsArray.Value1;
            Value2 = statsArray.Value2;
            Value3 = statsArray.Value3;
            Value4 = statsArray.Value4;
            IsOn = statsArray.IsOn;
        }

        public override string ToString()
        {
            return $"{Value1}, {Value2}{(Value3.HasValue ? $", {Value3}" : "")}{(Value4.HasValue ? $", {Value4}" : "")}{(IsOn.HasValue ? $", is_on: {Convert.ToInt16(IsOn)}" : "")}";
        }
    }
}
