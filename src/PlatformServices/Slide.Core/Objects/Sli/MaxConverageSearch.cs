namespace Slide.Core.Objects.Sli
{
    public class MaxConverageSearch
    {
        public int DivisionsAlong { get; set; } = 30;
        public int CirclesPerDiv { get; set; } = 12;
        public int Iterations { get; set; } = 12;
        public int PercentKept { get; set; } = 50;
        public bool Composite { get; set; } = true;
        public double? Depth { get; set; }

        public MaxConverageSearch()
        {

        }

        public MaxConverageSearch(MaxConverageSearch maxConverageSearch)
        {
            DivisionsAlong = maxConverageSearch.DivisionsAlong;
            CirclesPerDiv = maxConverageSearch.CirclesPerDiv;
            Iterations = maxConverageSearch.Iterations;
            PercentKept = maxConverageSearch.PercentKept;
            Composite = maxConverageSearch.Composite;
            Depth = maxConverageSearch.Depth;
        }

        public override string ToString()
        {
            return $"\r\nmaxcoverage search:\r\n  divisionsalong: {DivisionsAlong}\r\n  circlesperdiv: {CirclesPerDiv}\r\n  iterations: {Iterations}\r\n  percentkept: {PercentKept}\r\n  composite: {Convert.ToInt32(Composite)}{(Depth.HasValue && Depth != 0 ? $"\r\n  depth: {Depth}" : "")}";
        }
    }
}
