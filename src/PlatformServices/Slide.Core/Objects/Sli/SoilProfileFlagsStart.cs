namespace Slide.Core.Objects.Sli
{
    public class SoilProfileFlagsStart
    {
        public FromBoreholesStart FromBoreholesStart { get; set; } = new();
        public EmptyClass FromBoreholesEnd { get; set; }

        public SoilProfileFlagsStart()
        {

        }

        public SoilProfileFlagsStart(SoilProfileFlagsStart soilProfileFlagsStart)
        {
            FromBoreholesStart = new(soilProfileFlagsStart.FromBoreholesStart);
            FromBoreholesEnd = new();
        }

        public override string ToString()
        {
            return $"\r\nsoil profile flags start:\r\n{FromBoreholesStart}\r\nsoil profile flags end:";
        }
    }
}
