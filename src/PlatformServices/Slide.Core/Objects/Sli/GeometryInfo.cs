namespace Slide.Core.Objects.Sli
{
    public class GeometryInfo
    {
        public int MaterialType { get; set; }
        public bool EndFlag { get; set; }
        public double X { get; set; }
        public double Y { get; set; }
        public bool Disc { get; set; }
        public bool ABI { get; set; }
        public bool Visible { get; set; } = true;
        public bool Temporary { get; set; }

        public GeometryInfo()
        {

        }

        public GeometryInfo(GeometryInfo geometryInfo)
        {
            MaterialType = geometryInfo.MaterialType;
            EndFlag = geometryInfo.EndFlag;
            X = geometryInfo.X;
            Y = geometryInfo.Y;
            Disc = geometryInfo.Disc;
            ABI = geometryInfo.ABI;
            Visible = geometryInfo.Visible;
            Temporary = geometryInfo.Temporary;
        }

        public override string ToString()
        {
            return $"         {MaterialType}                {Convert.ToInt16(EndFlag)}        {X:0.000000000000},{Y:0.000000000000}           {Convert.ToInt16(Disc)}              {Convert.ToInt16(ABI)}          {Convert.ToInt16(Visible)}          {Convert.ToInt16(Temporary)}";
        }

    }
}
