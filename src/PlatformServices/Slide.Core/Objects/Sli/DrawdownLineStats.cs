namespace Slide.Core.Objects.Sli
{
    public class DrawdownLineStats
    {
        public StatsArray Stats { get; set; } = new()
        {
            Value1 = 2,
            Value2 = 0.5,
            Value3 = 0
        };

        public string DetermRdUsage { get; set; } = "Mean";

        public DrawdownLineStats()
        {

        }

        public DrawdownLineStats(DrawdownLineStats drawdownLineStats)
        {
            Stats = new StatsArray(drawdownLineStats.Stats);
            DetermRdUsage = drawdownLineStats.DetermRdUsage;
        }

        public override string ToString()
        {
            return $"\r\ndrawdown line stats:\r\n    {Stats}\r\n    determ rd usage: {DetermRdUsage}";
        }
    }
}
