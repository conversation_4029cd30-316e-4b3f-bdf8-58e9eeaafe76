namespace Slide.Core.Objects.Sli
{
    public class PathSearch
    {
        public int Samples { get; set; } = 5000;
        public bool Random { get; set; } = true;
        public bool Convex { get; set; } = true;
        public bool Optimize { get; set; } = true;
        public double? Depth { get; set; }

        public PathSearch()
        {

        }

        public PathSearch(PathSearch pathSearch)
        {
            Samples = pathSearch.Samples;
            Random = pathSearch.Random;
            Convex = pathSearch.Convex;
            Optimize = pathSearch.Optimize;
            Depth = pathSearch.Depth;
        }

        public override string ToString()
        {
            return $"\r\npath search:\r\n  samples: {Samples}\r\n  random: {Convert.ToInt16(Random)}\r\n  convex: {Convert.ToInt16(Convex)}\r\n  optimize: {Convert.ToInt16(Optimize)}{(Depth.HasValue && Depth != 0 ? $"\r\n  depth: {Depth}" : "")}";
        }
    }
}
