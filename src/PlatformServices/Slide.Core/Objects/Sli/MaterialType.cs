using Slide.Core.Enums;

namespace Slide.Core.Objects.Sli
{
    public class MaterialType
    {
        public Guid MaterialValueId { get; set; }
        public int MaterialSearchId { get; set; }
        public int Index { get; set; }
        public Enums.MaterialType Type { get; set; }
        public double Water { get; set; }
        public bool WTable { get; set; }
        public bool Piezo { get; set; }
        public double? TsRatio { get; set; }
        public double Uw { get; set; }
        public double? Uwbwt { get; set; }
        public bool? HuType { get; set; }
        public double? TsMin { get; set; }
        public double? MatTensile { get; set; }
        public double? MatMaxShear { get; set; }
        public int GwMode { get; set; } = 0;
        public int GwUserIndex { get; set; } = -1;
        public int GwKRatio { get; set; } = 1;
        public int GwKAngle { get; set; }
        public double GwKs { get; set; } = 1e-07;
        public double SK { get; set; } = 1e-07;
        public string SType { get; set; } = "General";
        public double GwWc { get; set; } = 0.4;
        public int GwWcr { get; set; }
        public int GwUsemv { get; set; }
        public double GwMv { get; set; } = 0.0002;
        public int GwUseWcForPhiB { get; set; }
        public int GwPhiB { get; set; }
        public int GwAev { get; set; }
        public int RdUndrained { get; set; }
        public int RdType { get; set; }
        public int RdCr { get; set; }
        public int RdPhir { get; set; }
        public double WithRu { get; set; }
        public int Stg2 { get; set; }
        public int Bbar { get; set; }
        public bool UseAlternateStrength { get; set; }
        public int AlternateMaterialIndex { get; set; }
        public int SpatiallyVariableMaterial { get; set; }
        public int CorrelationLengthX { get; set; } = 5;
        public int CorrelationLengthY { get; set; } = 5;
        public int MeshSize { get; set; } = 1;
        public double? C { get; set; }
        public double? Phi { get; set; }
        public double? Mb { get; set; }
        public double? S { get; set; }
        public double? A { get; set; }
        public double? SigC { get; set; }
        public bool? AllowSliding { get; set; }
        public double? ShansepA { get; set; }
        public double? ShansepS { get; set; }
        public double? ShansepM { get; set; }
        public bool? HisType { get; set; }
        public int? ShansepMd { get; set; }
        public int? ShansepHt { get; set; }
        public double? ShansepCon { get; set; }
        public string FunctionName { get; set; }
        public double? Dc { get; set; }
        public double? Ccut { get; set; }
        public double? CDatum { get; set; }
        public CohesionType? CType { get; set; }

        public MaterialType()
        {

        }

        public MaterialType(MaterialType materialType)
        {
            Index = materialType.Index;
            Type = materialType.Type;
            Water = materialType.Water;
            WTable = materialType.WTable;
            Piezo = materialType.Piezo;
            TsRatio = materialType.TsRatio;
            Uw = materialType.Uw;
            Uwbwt = materialType.Uwbwt;
            HuType = materialType.HuType;
            TsMin = materialType.TsMin;
            MatTensile = materialType.MatTensile;
            MatMaxShear = materialType.MatMaxShear;
            GwMode = materialType.GwMode;
            GwUserIndex = materialType.GwUserIndex;
            GwKRatio = materialType.GwKRatio;
            GwKAngle = materialType.GwKAngle;
            GwKs = materialType.GwKs;
            SK = materialType.SK;
            SType = materialType.SType;
            GwWc = materialType.GwWc;
            GwWcr = materialType.GwWcr;
            GwUsemv = materialType.GwUsemv;
            GwMv = materialType.GwMv;
            GwUseWcForPhiB = materialType.GwUseWcForPhiB;
            GwPhiB = materialType.GwPhiB;
            GwAev = materialType.GwAev;
            RdUndrained = materialType.RdUndrained;
            RdType = materialType.RdType;
            RdCr = materialType.RdCr;
            RdPhir = materialType.RdPhir;
            WithRu = materialType.WithRu;
            Stg2 = materialType.Stg2;
            Bbar = materialType.Bbar;
            UseAlternateStrength = materialType.UseAlternateStrength;
            AlternateMaterialIndex = materialType.AlternateMaterialIndex;
            SpatiallyVariableMaterial = materialType.SpatiallyVariableMaterial;
            CorrelationLengthX = materialType.CorrelationLengthX;
            CorrelationLengthY = materialType.CorrelationLengthY;
            MeshSize = materialType.MeshSize;
            C = materialType.C;
            Phi = materialType.Phi;
            Mb = materialType.Mb;
            S = materialType.S;
            A = materialType.A;
            SigC = materialType.SigC;
            AllowSliding = materialType.AllowSliding;
            ShansepA = materialType.ShansepA;
            ShansepS = materialType.ShansepS;
            ShansepM = materialType.ShansepM;
            HisType = materialType.HisType;
            ShansepMd = materialType.ShansepMd;
            ShansepHt = materialType.ShansepHt;
            ShansepCon = materialType.ShansepCon;
            FunctionName = materialType.FunctionName;
            Dc = materialType.Dc;
            Ccut = materialType.Ccut;
            CDatum = materialType.CDatum;
            CType = materialType.CType;
        }

        public override string ToString()
        {
            return $"  soil{Index} = type: {(int)Type}" +
                $" water: {Water}" +
                $"{(Piezo ? " piezo: 1" : WTable ? " wtable: 1" : " wtable: 0")}" +
                $"{(HuType != null ? $" hutype: {Convert.ToInt16(HuType)}" : "")}" +
                $"{(C != null ? $" c: {C}" : "")}" +
                $"{(TsRatio != null ? $" tsratio: {TsRatio}" : "")}" +
                $" uw: {Uw}" +
                $"{(Uwbwt != null ? $" uwbwt: {Uwbwt}" : "")}" +
                $"{(TsMin != null ? $" tsmin: {TsMin}" : "")}" +
                $"{(MatTensile != null ? $" mat_tensile: {MatTensile}" : "")}" +
                $"{(MatMaxShear != null ? $" mat_max_shear: {MatMaxShear}" : "")}" +
                $" gw_mode: {GwMode}" +
                $" gw_user: {GwMode}" +
                $" gw_user_index: {GwUserIndex}" +
                $" gw_k_ratio: {GwKRatio}" +
                $" gw_k_angle: {GwKAngle}" +
                $" gw_ks: {GwKs}" +
                $" SK: {SK}" +
                $" STYPE: {SType}" +
                $" gw_wc: {GwWc}" +
                $" gw_wcr: {GwWcr}" +
                $" gw_usemv: {GwUsemv}" +
                $" gw_mv: {GwMv}" +
                $" gw_use_wc_for_phib: {GwUseWcForPhiB}" +
                $" gw_phi_b: {GwPhiB}" +
                $" gw_aev: {GwAev}" +
                $" rd_undrained: {RdUndrained}" +
                $" rd_type: {RdType}" +
                $" rd_cr: {RdCr}" +
                $" rd_phir: {RdPhir}" +
                $" withru: {WithRu}" +
                $" stg2: {Stg2}" +
                $" bbar: {Bbar}" +
                $" use_alternate_strength: {Convert.ToInt32(UseAlternateStrength)}" +
                $" alternate_mat_index: {AlternateMaterialIndex}" +
                $" spatially_variable_material: {SpatiallyVariableMaterial}" +
                $" correlation_length_x: {CorrelationLengthX}" +
                $" correlation_length_y: {CorrelationLengthY}" +
                $" mesh_size: {MeshSize}" +
                $"{(Phi != null ? $" phi: {Phi}" : "")}" +
                $"{(Mb != null ? $" mb: {Mb}" : "")}" +
                $"{(S != null ? $" s: {S}" : "")}" +
                $"{(A != null ? $" a: {A}" : "")}" +
                $"{(SigC != null ? $" sigc: {SigC}" : "")}" +
                $"{(AllowSliding != null ? $" allowsliding: {AllowSliding}" : "")}" +
                $"{(ShansepA != null ? $" shansepA: {ShansepA}" : "")}" +
                $"{(ShansepS != null ? $" shansepS: {ShansepS}" : "")}" +
                $"{(CType != null ? $" ctype: {Convert.ToInt16(CType)}" : "")}" +
                $"{(ShansepM != null ? $" shansepm: {ShansepM}" : "")}" +
                $"{(HisType != null ? $" histype: {Convert.ToInt16(HisType)}" : "")}" +
                $"{(ShansepMd != null ? $" shansepmd: {ShansepMd}" : "")}" +
                $"{(ShansepHt != null ? $" shansepht: {ShansepHt}" : "")}" +
                $"{(ShansepCon != null ? $" shansepcon: {ShansepCon}" : "")}" +
                $"{(FunctionName != null ? $" name: {FunctionName}" : "")}" +
                $"{(Dc != null ? $" dc: {Dc}" : "")}" +
                $"{(Ccut != null ? $" ccut: {Ccut}" : "")}" +
                $"{(CDatum != null ? $" cdatum: {CDatum}" : "")}";
        }
    }
}
