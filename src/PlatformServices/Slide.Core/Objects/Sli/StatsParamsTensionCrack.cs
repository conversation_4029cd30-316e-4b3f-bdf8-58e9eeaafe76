namespace Slide.Core.Objects.Sli
{
    public class StatsParamsTensionCrack
    {
        public List<StatsArray> Array { get; set; } = new()
        {
            new()
            {
                Value1 = 2,
                Value2 = 0.5,
                Value3 = 0
            },
            new()
            {
                Value1 = 2,
                Value2 = 0,
                Value3 = 0,
                Value4 = 0,
                IsOn = false
            }
        };
        public string DetermTcUsage { get; set; } = "Mean";

        public StatsParamsTensionCrack() { }

        public StatsParamsTensionCrack(StatsParamsTensionCrack statsParamsTensionCrack)
        {
            Array = new List<StatsArray>(statsParamsTensionCrack.Array);
            DetermTcUsage = statsParamsTensionCrack.DetermTcUsage;
        }

        public override string ToString()
        {
            var text = $"\r\nstats params tension crack:\r\n";

            foreach (var statsArray in Array)
            {
                text += $"  {statsArray}\r\n";
            }

            text += $"    determ tc usage: {DetermTcUsage}";

            return text;
        }
    }
}
