namespace Slide.Core.Objects.Sli
{
    public class GridSearch
    {
        public int RInc { get; set; } = 10;
        public bool Composite { get; set; } = true;
        public bool ReverseCurvature { get; set; } = true;
        public double? Depth { get; set; }

        public GridSearch()
        {

        }

        public GridSearch(GridSearch gridSearch)
        {
            RInc = gridSearch.RInc;
            Composite = gridSearch.Composite;
            ReverseCurvature = gridSearch.ReverseCurvature;
            Depth = gridSearch.Depth;
        }

        public override string ToString()
        {
            return $"\r\ngrid search:\r\n  rinc: {RInc}\r\n  composite: {Convert.ToInt32(Composite)}\r\n  reverse_curvature: {Convert.ToInt32(ReverseCurvature)}{(Depth.HasValue && Depth != 0 ? $"\r\n  depth: {Depth}" : "")}";
        }
    }
}
