using Slide.Core.Enums;
using Slide.Core.Extensions;

namespace Slide.Core.Objects.Sli
{
    public class StatsParamPiezo
    {
        public int Id { get; set; }
        public Option IsOn { get; set; }
        public string Dist { get; set; } = "Normal";
        public int Stddv { get; set; }
        public int Rmin { get; set; }
        public int Rmax { get; set; }

        public StatsParamPiezo() { }

        public StatsParamPiezo(StatsParamPiezo statsParamPiezo)
        {
            Id = statsParamPiezo.Id;
            IsOn = statsParamPiezo.IsOn;
            Dist = statsParamPiezo.Dist;
            Stddv = statsParamPiezo.Stddv;
            Rmin = statsParamPiezo.Rmin;
            Rmax = statsParamPiezo.Rmax;
        }

        public override string ToString()
        {
            return $"   id: {Id}  is_on: {IsOn.GetDescription()}  dist: {Dist}  stddv: {Stddv}  rmin: {Rmin}  rmax: {Rmax}";
        }
    }
}
