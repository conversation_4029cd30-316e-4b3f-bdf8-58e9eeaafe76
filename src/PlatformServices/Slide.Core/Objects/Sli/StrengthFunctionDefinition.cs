namespace Slide.Core.Objects.Sli
{
    public class StrengthFunctionDefinition
    {
        public int Index { get; set; }
        public int Type { get; set; } = 5;
        public string Name { get; set; }
        public List<StrengthFunction> Functions { get; set; }

        public StrengthFunctionDefinition()
        {

        }

        public StrengthFunctionDefinition(StrengthFunctionDefinition strengthFunctionDefinition)
        {
            Index = strengthFunctionDefinition.Index;
            Type = strengthFunctionDefinition.Type;
            Name = strengthFunctionDefinition.Name;
            Functions = new List<StrengthFunction>(strengthFunctionDefinition.Functions);
        }

        public override string ToString()
        {
            var text = $"{Index} type: {Type} num: {Functions.Count} name: {Name}\r\n";

            foreach (var function in Functions)
            {
                text += $"    n: {function.N} s: {function.S}\r\n";
            }

            return text;
        }
    }
}
