namespace Slide.Core.Objects.Sli
{
    public class DesignStandardArrayDefinition
    {
        public List<DesignStandardArray> DesignStandardArray { get; set; } = new()
        {
            new()
            {
                Name = "None",
                Standard = 0,
                Array = new()
                {
                    new() { 1, 1, 1, 1, 1, 1 },
                    new() { 1, 1, 1, 1, 1 },
                    new() { 1, 1, 1, 1, 1, 1 },
                }
            },
            new()
            {
                Name = "Eurocode 7 - Design Approach 1, Combination 1",
                Standard = 1,
                Array = new()
                {
                    new() { 1, 0, 1.35, 1.5, 1, 1 },
                    new() { 1, 1, 1, 1, 1 },
                    new() { 1, 1.1, 1.1, 1.1, 1.1, 1 },
                }
            },
            new()
            {
                Name = "Eurocode 7 - Design Approach 1, Combination 2",
                Standard = 2,
                Array = new()
                {
                    new() { 1, 0, 1, 1.3, 1, 1 },
                    new() { 1.25, 1.25, 1.4, 1, 1.25 },
                    new() { 1, 1.1, 1.1, 1.1, 1.1, 1 },
                }
            },
            new()
            {
                Name = "Eurocode 7 - Design Approach 2",
                Standard = 3,
                Array = new()
                {
                    new() { 1, 0, 1.35, 1.5, 1, 1 },
                    new() { 1, 1, 1, 1, 1 },
                    new() { 1.1, 1.1, 1.1, 1.1, 1.1, 1 },
                }
            },
            new()
            {
                Name = "Eurocode 7 - Design Approach 3",
                Standard = 4,
                Array = new()
                {
                    new() { 1, 0, 1, 1.3, 1, 1 },
                    new() { 1.25, 1.25, 1.4, 1, 1.25 },
                    new() { 1, 1, 1, 1, 1, 1 },
                }
            },
            new()
            {
                Name = "BS 8006:1995 Section 7 - Reinforced Slopes",
                Standard = 5,
                Array = new()
                {
                    new() { 1, 1, 1.2, 1.3, 1.5, 1 },
                    new() { 1.6, 1, 1, 1, 1 },
                    new() { 1.25, 1.5, 1.5, 1.5, 1.3, 1 },
                }
            },
            new()
            {
                Name = "BS 8006:2010 Section 7 - Reinforced Slopes",
                Standard = 8,
                Array = new()
                {
                    new() { 1, 1, 1.2, 1.3, 1.5, 1 },
                    new() { 1.6, 1, 1, 1, 1 },
                    new() { 1, 1.5, 1.5, 1.5, 1.3, 1 },
                }
            }
        };

        public DesignStandardArrayDefinition()
        {

        }

        public DesignStandardArrayDefinition(DesignStandardArrayDefinition designStandardArrayDefinition)
        {
            DesignStandardArray = designStandardArrayDefinition.DesignStandardArray.Select(x => new DesignStandardArray
            {
                Name = x.Name,
                Standard = x.Standard,
                Array = x.Array.Select(y => y.ToList()).ToList()
            }).ToList();
        }

        public override string ToString()
        {
            var text = $"\r\ndesign standard array:\r\n{DesignStandardArray.Count}\r\n";

            foreach (var item in DesignStandardArray)
            {
                var designStandardArray = $"  name: {item.Name}\r\n  standard: {item.Standard}\r\n";

                foreach (var array in item.Array)
                {
                    var arrayText = "";

                    arrayText += string.Join(" ", array);

                    designStandardArray += $"  {arrayText}\r\n";
                }

                text += designStandardArray;
            }

            return text;
        }
    }
}
