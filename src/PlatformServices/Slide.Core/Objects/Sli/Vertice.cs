namespace Slide.Core.Objects.Sli
{
    public class Vertice
    {
        public int? Index { get; set; }
        public double X { get; set; }
        public double Y { get; set; }

        public Vertice()
        {

        }

        public Vertice(double x, double y)
        {
            X = x;
            Y = y;
            Index = null;
        }

        public Vertice(Vertice vertice)
        {
            Index = vertice.Index;
            X = vertice.X;
            Y = vertice.Y;
        }

        public override string ToString()
        {
            return $"  {Index} x: {Math.Round(X, 12)}  y: {Math.Round(Y, 12)}";
        }

        public bool IsCloseTo(Vertice other)
        {
            const double tolerance = 0.001;
            
            
        }
    }
}
