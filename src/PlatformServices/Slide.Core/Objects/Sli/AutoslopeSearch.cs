namespace Slide.Core.Objects.Sli
{
    public class AutoslopeSearch
    {
        public bool Optimize { get; set; } = true;
        public double? Depth { get; set; }
        public int DivisionsAlong { get; set; } = 30;
        public int CirclesPerDiv { get; set; } = 12;
        public int Iterations { get; set; } = 12;
        public int PercentKept { get; set; } = 50;
        public int NumVertices { get; set; } = 12;

        public AutoslopeSearch()
        {

        }

        public AutoslopeSearch(AutoslopeSearch autoslopeSearch)
        {
            Optimize = autoslopeSearch.Optimize;
            Depth = autoslopeSearch.Depth;
            DivisionsAlong = autoslopeSearch.DivisionsAlong;
            CirclesPerDiv = autoslopeSearch.CirclesPerDiv;
            Iterations = autoslopeSearch.Iterations;
            PercentKept = autoslopeSearch.PercentKept;
            NumVertices = autoslopeSearch.NumVertices;
        }

        public override string ToString()
        {
            return $"\r\nautoslope search:\r\n  divisionsalong: {DivisionsAlong}\r\n  circlesperdiv: {CirclesPerDiv}\r\n  iterations: {Iterations}\r\n  percentkept: {PercentKept}\r\n  numvertices: {NumVertices}\r\n{(Depth.HasValue && Depth != 0 ? $"  depth: {Depth}\r\n" : "")}  optimize: {Convert.ToInt32(Optimize)}\r\n";
        }
    }
}
