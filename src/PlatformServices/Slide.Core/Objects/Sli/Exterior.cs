namespace Slide.Core.Objects.Sli
{
    public class Exterior
    {
        public int Index { get; set; }
        public List<int> Vertices { get; set; }

        public Exterior()
        {

        }

        public Exterior(Exterior exterior)
        {
            Index = exterior.Index;
            Vertices = new(exterior.Vertices);
        }

        public override string ToString()
        {
            return $"\r\nexterior:\r\n  {Index}  vertices: [{string.Join(",", Vertices)}]";
        }
    }
}
