namespace Slide.Core.Objects.Sli
{
    public class ProbabilitySettings
    {
        public int SamplingMethod { get; set; } = 1;
        public int NumberOfSamples { get; set; } = 1000;
        public int AnalysisType { get; set; } = 0;
        public int SpatiallyVariableAnalysis { get; set; } = 0;
        public int SpatialCovarianceFunction { get; set; } = 0;
        public int SpatialAutoMesh { get; set; } = 1;
        public int SpatialInfiniteCorrelationCheck { get; set; } = 1;
        public int HermiteOrder { get; set; } = 3;

        public ProbabilitySettings()
        {

        }

        public ProbabilitySettings(ProbabilitySettings probabilitySettings)
        {
            SamplingMethod = probabilitySettings.SamplingMethod;
            NumberOfSamples = probabilitySettings.NumberOfSamples;
            AnalysisType = probabilitySettings.AnalysisType;
            SpatiallyVariableAnalysis = probabilitySettings.SpatiallyVariableAnalysis;
            SpatialCovarianceFunction = probabilitySettings.SpatialCovarianceFunction;
            SpatialAutoMesh = probabilitySettings.SpatialAutoMesh;
            SpatialInfiniteCorrelationCheck = probabilitySettings.SpatialInfiniteCorrelationCheck;
            HermiteOrder = probabilitySettings.HermiteOrder;
        }

        public override string ToString()
        {
            return $"\r\nprobability settings:\r\n  sampling_method: {SamplingMethod}\r\n  number_of_samples: {NumberOfSamples}\r\n  analysis_type: {AnalysisType}\r\n  spatially_variable_analysis: {SpatiallyVariableAnalysis}\r\n  spatial_covariance_function: {SpatialCovarianceFunction}\r\n  spatial_auto_mesh: {SpatialAutoMesh}\r\n  spatial_infinite_correlation_check: {SpatialInfiniteCorrelationCheck}\r\n  hermite_order: {HermiteOrder}";
        }
    }
}
