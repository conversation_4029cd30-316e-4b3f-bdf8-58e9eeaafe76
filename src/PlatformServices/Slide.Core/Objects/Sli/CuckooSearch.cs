namespace Slide.Core.Objects.Sli
{
    public class CuckooSearch
    {
        public int NVertices { get; set; } = 8;
        public int NSurf { get; set; } = 1000;
        public int MaxIterations { get; set; } = 500;
        public int NumNests { get; set; } = 50;
        public bool Optimize { get; set; } = true;
        public bool Convex { get; set; } = true;
        public double? Depth { get; set; }

        public CuckooSearch()
        {

        }

        public CuckooSearch(CuckooSearch cuckooSearch)
        {
            NVertices = cuckooSearch.NVertices;
            NSurf = cuckooSearch.NSurf;
            MaxIterations = cuckooSearch.MaxIterations;
            NumNests = cuckooSearch.NumNests;
            Optimize = cuckooSearch.Optimize;
            Convex = cuckooSearch.Convex;
            Depth = cuckooSearch.Depth;
        }

        public override string ToString()
        {
            return $"\r\ncuckoo search:\r\n  nvertices: {NVertices}\r\n  nsurf: {NSurf}\r\n  maxiterations: {MaxIterations}\r\n  numnests: {NumNests}\r\n  optimize: {Convert.ToInt32(Optimize)}\r\n  convex: {Convert.ToInt32(Convex)}{(Depth.HasValue && Depth != 0 ? $"\r\n  depth: {Depth}" : "")}";
        }
    }
}
