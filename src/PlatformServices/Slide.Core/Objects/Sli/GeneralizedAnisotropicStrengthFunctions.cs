namespace Slide.Core.Objects.Sli
{
    public class GeneralizedAnisotropicStrengthFunctions
    {
        public int NumFunctions { get; set; }

        public GeneralizedAnisotropicStrengthFunctions()
        {

        }

        public GeneralizedAnisotropicStrengthFunctions(GeneralizedAnisotropicStrengthFunctions generalizedAnisotropicStrengthFunctions)
        {
            NumFunctions = generalizedAnisotropicStrengthFunctions.NumFunctions;
        }

        public override string ToString()
        {
            return $"\r\ngeneralized anisotropic strength functions:\r\n  numfunctions: {NumFunctions}";
        }
    }
}
