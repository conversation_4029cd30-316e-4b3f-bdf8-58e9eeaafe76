namespace Slide.Core.Objects.Sli
{
    public class ShansepFunctionSection
    {
        public List<ShansepFunctionDefinition> Definitions { get; set; } = new();

        public ShansepFunctionSection()
        {

        }

        public ShansepFunctionSection(ShansepFunctionSection shansepFunctionSection)
        {
            Definitions = shansepFunctionSection.Definitions.Select(x => new ShansepFunctionDefinition(x)).ToList();
        }

        public override string ToString()
        {
            var text = $"\r\nshansep variable functions:\r\n  numfunctions: {Definitions.Count}";

            foreach (var definition in Definitions)
            {
                text += definition.ToString();
            }

            return text;
        }
    }
}
