namespace Slide.Core.Objects.Sli
{
    public class AnchorType
    {
        public string Name { get; set; }
        public int Type { get; set; } = 1;
        public int Fa { get; set; }
        public int Sp { get; set; } = 1;
        public int Cap { get; set; } = 100;
        public int FactorFlag { get; set; }
        public int FactorTensionPlate { get; set; } = 1;
        public int FactorBond { get; set; } = 1;
        public int FactorShear { get; set; } = 1;
        public int FactorCompression { get; set; } = 1;
        public int DsFlag { get; set; }
        public int CtFlag { get; set; }
        public int AppliedFactor { get; set; }
        public int FoFlag { get; set; }
        public int FoAng { get; set; } = 30;
        public string EaCommonType { get; set; } = "empty";
        public string EaShortDescription { get; set; } = "empty";

        public AnchorType()
        {

        }

        public AnchorType(AnchorType anchorType)
        {
            Name = anchorType.Name;
            Type = anchorType.Type;
            Fa = anchorType.Fa;
            Sp = anchorType.Sp;
            Cap = anchorType.Cap;
            FactorFlag = anchorType.FactorFlag;
            FactorTensionPlate = anchorType.FactorTensionPlate;
            FactorBond = anchorType.FactorBond;
            FactorShear = anchorType.FactorShear;
            FactorCompression = anchorType.FactorCompression;
            DsFlag = anchorType.DsFlag;
            CtFlag = anchorType.CtFlag;
            AppliedFactor = anchorType.AppliedFactor;
            FoFlag = anchorType.FoFlag;
            FoAng = anchorType.FoAng;
            EaCommonType = anchorType.EaCommonType;
            EaShortDescription = anchorType.EaShortDescription;
        }

        public override string ToString()
        {
            return $"  {Name} = type: {Type} fa: {Fa} sp: {Sp} cap: {Cap} factor_flag: {FactorFlag} factor_tension_plate: {FactorTensionPlate} factor_bond: {FactorBond} factor_shear: {FactorShear} factor_compression: {FactorCompression} ds_flag: {DsFlag} ct_flag: {CtFlag} applied_factor: {AppliedFactor} fo_flag: {FoFlag} fo_ang: {FoAng} ea_common_type: {EaCommonType} ea_short_description: {EaShortDescription}";
        }
    }
}
