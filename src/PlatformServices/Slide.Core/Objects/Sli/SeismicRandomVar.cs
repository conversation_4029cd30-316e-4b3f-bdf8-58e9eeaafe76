namespace Slide.Core.Objects.Sli
{
    public class SeismicRandomVar
    {
        public List<StatsArray> Array { get; set; } = new()
        {
            new()
            {
                Value1 = 2,
                Value2 = 0,
                Value3 = 0,
                Value4 = 0,
                IsOn = true
            },
            new()
            {
                Value1 = 2,
                Value2 = 0,
                Value3 = 0,
                Value4 = 0,
                IsOn = true
            },
            new()
            {
                Value1 = 0,
                Value2 = 0.5
            }
        };

        public SeismicRandomVar()
        {

        }

        public SeismicRandomVar(SeismicRandomVar seismicRandomVar)
        {
            Array = seismicRandomVar.Array.Select(statsArray => new StatsArray(statsArray)).ToList();
        }

        public override string ToString()
        {
            var text = $"\r\nseismic random var:";

            foreach (var statsArray in Array)
            {
                text += $"\r\n    {statsArray}";
            }

            return text;
        }
    }
}
