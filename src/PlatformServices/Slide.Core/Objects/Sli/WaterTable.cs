namespace Slide.Core.Objects.Sli
{
    public class WaterTable
    {
        public bool FromBoreholes { get; set; }
        public Guid UniqueId { get; set; } = Guid.NewGuid();
        public List<int> Vertices { get; set; } = new();

        public WaterTable()
        {

        }

        public WaterTable(WaterTable waterTable)
        {
            FromBoreholes = waterTable.FromBoreholes;
            UniqueId = waterTable.UniqueId;
            Vertices = new(waterTable.Vertices);
        }

        public override string ToString()
        {
            return $"\r\nwater table:\r\n  from_boreholes: {Convert.ToInt32(FromBoreholes)}  unique_id: {{{UniqueId}}} vertices: [{string.Join(",", Vertices)}]";
        }
    }
}
