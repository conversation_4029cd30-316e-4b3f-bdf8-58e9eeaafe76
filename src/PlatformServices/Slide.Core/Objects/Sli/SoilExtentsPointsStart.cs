using Slide.Core.Objects._Shared;

namespace Slide.Core.Objects.Sli
{
    public class SoilExtentsPointsStart
    {
        public DPointArrayStart DPointArrayStart { get; set; } = new();
        public EmptyClass DPointArrayEnd { get; set; }

        public SoilExtentsPointsStart()
        {

        }

        public SoilExtentsPointsStart(SoilExtentsPointsStart soilExtentsPointsStart)
        {
            DPointArrayStart = new(soilExtentsPointsStart.DPointArrayStart);
            DPointArrayEnd = new();
        }

        public override string ToString()
        {
            return $"  soil_extents_points start: {DPointArrayStart}\r\n  soil_extents_points end:";
        }
    }
}
