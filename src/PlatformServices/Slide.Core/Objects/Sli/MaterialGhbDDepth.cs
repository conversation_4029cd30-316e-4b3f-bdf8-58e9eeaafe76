namespace Slide.Core.Objects.Sli
{
    public class MaterialGhbDDepth
    {
        public int Num { get; set; }
        public int D { get; set; }
        public int Dv { get; set; }
        public int DD { get; set; }
        public int CD { get; set; }
        public int CutD { get; set; }
        public int DatD { get; set; }
        public int D0poly { get; set; } = -1;
        public Guid? D0polyGuid { get; set; }
        public int DpolyInterpMethod { get; set; }
        public int DTop { get; set; } = 1;
        public int DTable { get; set; } = -1;

        public MaterialGhbDDepth()
        {

        }

        public MaterialGhbDDepth(MaterialGhbDDepth materialGhbDDepth)
        {
            Num = materialGhbDDepth.Num;
            D = materialGhbDDepth.D;
            Dv = materialGhbDDepth.Dv;
            DD = materialGhbDDepth.DD;
            CD = materialGhbDDepth.CD;
            CutD = materialGhbDDepth.CutD;
            DatD = materialGhbDDepth.DatD;
            D0poly = materialGhbDDepth.D0poly;
            D0polyGuid = materialGhbDDepth.D0polyGuid;
            DpolyInterpMethod = materialGhbDDepth.DpolyInterpMethod;
            DTop = materialGhbDDepth.DTop;
            DTable = materialGhbDDepth.DTable;
        }

        public override string ToString()
        {
            return $"  num: {Num}  D: {D}  Dv: {Dv}  dD: {DD}  cD: {CD}  cutD: {CutD}  datD: {DatD}  d0poly: {D0poly}  d0poly_guid: {(D0polyGuid.HasValue ? D0polyGuid : " ")} dpoly_interp_method: {DpolyInterpMethod}  d_top: {DTop}  d_table: {DTable}";
        }
    }
}
