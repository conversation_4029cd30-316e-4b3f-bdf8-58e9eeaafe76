namespace Slide.Core.Objects.Sli
{
    public class NumberMatAssign
    {
        public int NumMatAssign { get; set; }

        public NumberMatAssign()
        {

        }

        public NumberMatAssign(NumberMatAssign numberMatAssign)
        {
            NumMatAssign = numberMatAssign.NumMatAssign;
        }

        public override string ToString()
        {
            return $"\r\n  num_mat_assign: {NumMatAssign}";
        }
    }
}
