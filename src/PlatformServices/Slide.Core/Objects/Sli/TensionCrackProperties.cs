namespace Slide.Core.Objects.Sli
{
    public class TensionCrackProperties
    {
        public string Type { get; set; } = "dry";
        public int AngleFlag { get; set; }
        public int AngleValue { get; set; } = 10;

        public TensionCrackProperties()
        {

        }

        public TensionCrackProperties(TensionCrackProperties tensionCrackProperties)
        {
            Type = tensionCrackProperties.Type;
            AngleFlag = tensionCrackProperties.AngleFlag;
            AngleValue = tensionCrackProperties.AngleValue;
        }

        public override string ToString()
        {
            return $"\r\ntension crack properties:\r\n  type: {Type}  angle_flag: {AngleFlag}  angle_value: {AngleValue}";
        }
    }
}
