using Slide.Core.Enums;
using Slide.Core.Extensions;

namespace Slide.Core.Objects.Sli
{
    public class ModelDescription
    {
        public string Version { get; set; } = "9.028";
        public int Methods { get; set; } = 2;
        public Units Units { get; set; } = Units.Metric;
        public double Seismic { get; set; }
        public double SeismicV { get; set; }
        public bool SeismicAdvanced { get; set; } = false;
        public int SeismicAdvancedMethod { get; set; }
        public int SeismicPseudoStatic { get; set; }
        public int SeismicPseudoStaticMethod { get; set; }
        public Water Water { get; set; } = Water.Hu;
        public Direction Direction { get; set; } = Direction.LeftToRight;
        public double Tolerance { get; set; } = 0.005;
        public int Maxiter { get; set; } = 75;
        public int Malpha { get; set; } = 1;
        public int ZeroShearCheck { get; set; } = 1;
        public int TensileCheck { get; set; }
        public int TensilePercentage { get; set; } = 95;
        public int CorpengCheck { get; set; }
        public int CorpengVal { get; set; }
        public int CheckVerticalSegments { get; set; } = 0;
        public int ApplySupportToInterslice { get; set; }
        public int CheckPiezoSurfaceIntersection { get; set; } = 1;
        public int CorrectRadius { get; set; }
        public int CorrectNormal { get; set; }
        public int CheckMinLambda { get; set; }
        public int CheckMaxLambda { get; set; }
        public double MinLambda { get; set; } = -0.1;
        public double MaxLambda { get; set; } = 6;
        public int CheckDiscardBelowFs { get; set; }
        public int CheckDiscardAboveFs { get; set; }
        public double DiscardBelowFsVal { get; set; } = 0.1;
        public double DiscardAboveFsVal { get; set; } = 3;
        public int InitTrialFs { get; set; } = 1;
        public double MaxChangeFs { get; set; } = 0.5;
        public double MinFsIter { get; set; } = 0.1;
        public int CheckMinFsIter { get; set; }
        public int CheckMaxChangeFs { get; set; }
        public int CheckMinSliceWeight { get; set; }
        public int CheckMinSliceWidth { get; set; }
        public int CheckMaxBaseAngle { get; set; }
        public double MinSliceWeight { get; set; } = 0.001;
        public double MinSliceWidth { get; set; } = 0.001;
        public int MaxBaseAngle { get; set; } = 80;
        public int BMinSlices { get; set; }
        public int Steffensen { get; set; } = 1;
        public int MultiDocMode { get; set; }
        public string TempMultiDocId { get; set; } = string.Empty;
        public int UseProfile { get; set; }
        public int SarmaCohesion { get; set; }
        public int SarmaPhi { get; set; } = 35;
        public int SlicesType { get; set; }
        public int SarmaInterslice { get; set; }
        public int SarmaSliceAngleOption { get; set; }
        public int SarmaSliceAngleMethod { get; set; }
        public int SarmaOptimizeNonVertical { get; set; }
        public double SeismicTargetFs { get; set; } = 1;
        public int SarmaIntersectMaterials { get; set; } = 1;
        public double Gammaw { get; set; } = 9.81;
        public int MaxNegPorePressure { get; set; }
        public int UseMaxNegPorePressure { get; set; } = 1;
        public int WatersurfMaxNegPorePressure { get; set; }
        public int WatersurfUseMaxNegPorePressure { get; set; } = 1;
        public int RegularMaxNegPorePressure { get; set; }
        public int RegularUseMaxNegPorePressure { get; set; }
        public Mode Mode { get; set; } = Mode.Circular;
        public NonCircularSearch Search { get; set; } = NonCircularSearch.CuckooSearch;
        public CircularSearch CircularSearch { get; set; } = CircularSearch.SlopeSearch;
        public int DataMode { get; set; } = 1;
        public int NumMaterials { get; set; }
        public int NumAnchors { get; set; } = 5;
        public int Discretized { get; set; }
        public int FeaMeshExists { get; set; }
        public int ProbabilityAnalysis { get; set; }
        public Option SensitivityAnalysis { get; set; } = Option.No;
        public int RandomType { get; set; }
        public int RandomMethod { get; set; }
        public int WithRu { get; set; } = 1;
        public int SeismicStage2 { get; set; }
        public Option Transient { get; set; } = Option.No;
        public int DesignSelection { get; set; }
        public int SingleSource { get; set; } = 1;
        public int UseYgForAnchors { get; set; }
        public int UseEnhancedSearchFormulation { get; set; }
        public int WeakLayerHandling { get; set; }
        public Option AdvancedOptions { get; set; } = Option.No;
        public int AdvancedOptionsType { get; set; }
        public int AdvancedOptionsRddMethod { get; set; }
        public int RddInterpolationMethod { get; set; } = 1;
        public int SeismicInterpolationMethod { get; set; } = 1;
        public TimeUnits TimeUnits { get; set; } = TimeUnits.Days;
        public MetricUnits PermeabilityUnitsImperial { get; set; } = MetricUnits.FeetPerSecond;
        public MetricUnits PermeabilityUnitsMetric { get; set; } = MetricUnits.MetersPerSecond;
        public int BoreholeNumDiv { get; set; } = 20;
        public int BoreholeDefineByThickness { get; set; } = 1;
        public int SurfaceInterpMethod { get; set; } = 5;
        public int InterpolateTopBoreholeSurface { get; set; }
        public int ExcavateAboveBoreholeTopSurface { get; set; } = 1;
        public int UseDateForStages { get; set; }
        public int StageNames { get; set; }

        public ModelDescription()
        {

        }

        public ModelDescription(ModelDescription modelDescription)
        {
            Version = modelDescription.Version;
            Methods = modelDescription.Methods;
            Units = modelDescription.Units;
            Seismic = modelDescription.Seismic;
            SeismicV = modelDescription.SeismicV;
            SeismicAdvanced = modelDescription.SeismicAdvanced;
            SeismicAdvancedMethod = modelDescription.SeismicAdvancedMethod;
            SeismicPseudoStatic = modelDescription.SeismicPseudoStatic;
            SeismicPseudoStaticMethod = modelDescription.SeismicPseudoStaticMethod;
            Water = modelDescription.Water;
            Direction = modelDescription.Direction;
            Tolerance = modelDescription.Tolerance;
            Maxiter = modelDescription.Maxiter;
            Malpha = modelDescription.Malpha;
            ZeroShearCheck = modelDescription.ZeroShearCheck;
            TensileCheck = modelDescription.TensileCheck;
            TensilePercentage = modelDescription.TensilePercentage;
            CorpengCheck = modelDescription.CorpengCheck;
            CorpengVal = modelDescription.CorpengVal;
            CheckVerticalSegments = modelDescription.CheckVerticalSegments;
            ApplySupportToInterslice = modelDescription.ApplySupportToInterslice;
            CheckPiezoSurfaceIntersection = modelDescription.CheckPiezoSurfaceIntersection;
            CorrectRadius = modelDescription.CorrectRadius;
            CorrectNormal = modelDescription.CorrectNormal;
            CheckMinLambda = modelDescription.CheckMinLambda;
            CheckMaxLambda = modelDescription.CheckMaxLambda;
            MinLambda = modelDescription.MinLambda;
            MaxLambda = modelDescription.MaxLambda;
            CheckDiscardBelowFs = modelDescription.CheckDiscardBelowFs;
            CheckDiscardAboveFs = modelDescription.CheckDiscardAboveFs;
            DiscardBelowFsVal = modelDescription.DiscardBelowFsVal;
            DiscardAboveFsVal = modelDescription.DiscardAboveFsVal;
            InitTrialFs = modelDescription.InitTrialFs;
            MaxChangeFs = modelDescription.MaxChangeFs;
            MinFsIter = modelDescription.MinFsIter;
            CheckMinFsIter = modelDescription.CheckMinFsIter;
            CheckMaxChangeFs = modelDescription.CheckMaxChangeFs;
            CheckMinSliceWeight = modelDescription.CheckMinSliceWeight;
            CheckMinSliceWidth = modelDescription.CheckMinSliceWidth;
            CheckMaxBaseAngle = modelDescription.CheckMaxBaseAngle;
            MinSliceWeight = modelDescription.MinSliceWeight;
            MinSliceWidth = modelDescription.MinSliceWidth;
            MaxBaseAngle = modelDescription.MaxBaseAngle;
            BMinSlices = modelDescription.BMinSlices;
            Steffensen = modelDescription.Steffensen;
            MultiDocMode = modelDescription.MultiDocMode;
            TempMultiDocId = modelDescription.TempMultiDocId;
            UseProfile = modelDescription.UseProfile;
            SarmaCohesion = modelDescription.SarmaCohesion;
            SarmaPhi = modelDescription.SarmaPhi;
            SlicesType = modelDescription.SlicesType;
            SarmaInterslice = modelDescription.SarmaInterslice;
            SarmaSliceAngleOption = modelDescription.SarmaSliceAngleOption;
            SarmaSliceAngleMethod = modelDescription.SarmaSliceAngleMethod;
            SarmaOptimizeNonVertical = modelDescription.SarmaOptimizeNonVertical;
            SeismicTargetFs = modelDescription.SeismicTargetFs;
            SarmaIntersectMaterials = modelDescription.SarmaIntersectMaterials;
            Gammaw = modelDescription.Gammaw;
            MaxNegPorePressure = modelDescription.MaxNegPorePressure;
            UseMaxNegPorePressure = modelDescription.UseMaxNegPorePressure;
            WatersurfMaxNegPorePressure = modelDescription.WatersurfMaxNegPorePressure;
            WatersurfUseMaxNegPorePressure = modelDescription.WatersurfUseMaxNegPorePressure;
            RegularMaxNegPorePressure = modelDescription.RegularMaxNegPorePressure;
            RegularUseMaxNegPorePressure = modelDescription.RegularUseMaxNegPorePressure;
            Mode = modelDescription.Mode;
            Search = modelDescription.Search;
            CircularSearch = modelDescription.CircularSearch;
            DataMode = modelDescription.DataMode;
            NumMaterials = modelDescription.NumMaterials;
            NumAnchors = modelDescription.NumAnchors;
            Discretized = modelDescription.Discretized;
            FeaMeshExists = modelDescription.FeaMeshExists;
            ProbabilityAnalysis = modelDescription.ProbabilityAnalysis;
            SensitivityAnalysis = modelDescription.SensitivityAnalysis;
            RandomType = modelDescription.RandomType;
            RandomMethod = modelDescription.RandomMethod;
            WithRu = modelDescription.WithRu;
            SeismicStage2 = modelDescription.SeismicStage2;
            Transient = modelDescription.Transient;
            DesignSelection = modelDescription.DesignSelection;
            SingleSource = modelDescription.SingleSource;
            UseYgForAnchors = modelDescription.UseYgForAnchors;
            UseEnhancedSearchFormulation = modelDescription.UseEnhancedSearchFormulation;
            WeakLayerHandling = modelDescription.WeakLayerHandling;
            AdvancedOptions = modelDescription.AdvancedOptions;
            AdvancedOptionsType = modelDescription.AdvancedOptionsType;
            AdvancedOptionsRddMethod = modelDescription.AdvancedOptionsRddMethod;
            RddInterpolationMethod = modelDescription.RddInterpolationMethod;
            SeismicInterpolationMethod = modelDescription.SeismicInterpolationMethod;
            TimeUnits = modelDescription.TimeUnits;
            PermeabilityUnitsImperial = modelDescription.PermeabilityUnitsImperial;
            PermeabilityUnitsMetric = modelDescription.PermeabilityUnitsMetric;
            BoreholeNumDiv = modelDescription.BoreholeNumDiv;
            BoreholeDefineByThickness = modelDescription.BoreholeDefineByThickness;
            SurfaceInterpMethod = modelDescription.SurfaceInterpMethod;
            InterpolateTopBoreholeSurface = modelDescription.InterpolateTopBoreholeSurface;
            ExcavateAboveBoreholeTopSurface = modelDescription.ExcavateAboveBoreholeTopSurface;
            UseDateForStages = modelDescription.UseDateForStages;
            StageNames = modelDescription.StageNames;
        }

        public override string ToString()
        {
            return $@"
model description:
  version: {Version}
  methods: {Methods}
  units: {Units.GetDescription()}
  seismic: {Seismic}
  seismicv: {SeismicV}
  seismic_advanced: {(SeismicAdvanced ? 1 : 0)}
  seismic_advanced_method: {SeismicAdvancedMethod}
  seismic_pseudostatic: {SeismicPseudoStatic}
  seismic_pseudostatic_method: {SeismicPseudoStaticMethod}
  water: {Water.GetDescription()}
  direction: {Direction.GetDescription()}
  tolerance: {Tolerance}
  maxiter: {Maxiter}
  malpha: {Malpha}
  zero_shear_check: {ZeroShearCheck}
  tensile check: {TensileCheck}
  tensile percentage: {TensilePercentage}
  corpeng_check: {CorpengCheck}
  corpeng_val: {CorpengVal}
  check_vertical_segments: {CheckVerticalSegments}
  apply_support_to_interslice: {ApplySupportToInterslice}
  check_piezo_surface_intersection: {CheckPiezoSurfaceIntersection}
  correctradius: {CorrectRadius}
  correctnormal: {CorrectNormal}
  check_min_lambda: {CheckMinLambda}
  check_max_lambda: {CheckMaxLambda}
  min_lambda: {MinLambda}
  max_lambda: {MaxLambda}
  check_discard_below_fs: {CheckDiscardBelowFs}
  check_discard_above_fs: {CheckDiscardAboveFs}
  discard_below_fs_val: {DiscardBelowFsVal}
  discard_above_fs_val: {DiscardAboveFsVal}
  init_trial_fs: {InitTrialFs}
  max_change_fs: {MaxChangeFs}
  min_fs_iter: {MinFsIter}
  check_min_fs_iter: {CheckMinFsIter}
  check_max_change_fs: {CheckMaxChangeFs}
  check_min_slice_weight: {CheckMinSliceWeight}
  check_min_slice_width: {CheckMinSliceWidth}
  check_max_base_angle: {CheckMaxBaseAngle}
  min_slice_weight: {MinSliceWeight}
  min_slice_width: {MinSliceWidth}
  max_base_angle: {MaxBaseAngle}
  bMinSlices: {BMinSlices}
  steffensen: {Steffensen}
  multidocmode: {MultiDocMode}
  temp_multi_doc_id: {TempMultiDocId}
  use_profile: {UseProfile}
  sarma_cohesion: {SarmaCohesion}
  sarma_phi: {SarmaPhi}
  slices_type: {SlicesType}
  sarma_interslice: {SarmaInterslice}
  sarma_slice_angle_option: {SarmaSliceAngleOption}
  sarma_slice_angle_method: {SarmaSliceAngleMethod}
  sarma_optimize_non_vertical: {SarmaOptimizeNonVertical}
  seismic_target_fs: {SeismicTargetFs}
  sarma_intersect_materials: {SarmaIntersectMaterials}
  gammaw: {Gammaw}
  maxnegporepressure: {MaxNegPorePressure}
  usemaxnegporepressure: {UseMaxNegPorePressure}
  watersurf_maxnegporepressure: {WatersurfMaxNegPorePressure}
  watersurf_usemaxnegporepressure: {WatersurfUseMaxNegPorePressure}
  regular_maxnegporepressure: {RegularMaxNegPorePressure}
  regular_usemaxnegporepressure: {RegularUseMaxNegPorePressure}
  mode: {(int)Mode}
  search: {(int)Search}
  circularsearch: {(int)CircularSearch}
  datamode: {DataMode}
  nummaterials: {NumMaterials}
  numanchors: {NumAnchors}
  discretized: {Discretized}
  fea mesh exists: {FeaMeshExists}
  probability_analysis: {ProbabilityAnalysis}
  sensitivity analysis: {SensitivityAnalysis.GetDescription()}
  random_type: {RandomType}
  random_method: {RandomMethod}
  with_ru: {WithRu}
  seismic_stage2: {SeismicStage2}
  transient: {Transient.GetDescription()}
  design_selection: {DesignSelection}
  singlesource: {SingleSource}
  use_yg_for_anchors: {UseYgForAnchors}
  use_enhanced_search_formulation: {UseEnhancedSearchFormulation}
  weak_layer_handling: {WeakLayerHandling}
  advanced_options: {AdvancedOptions.GetDescription()}
  advanced_options_type: {AdvancedOptionsType}
  advanced_options_rdd_method: {AdvancedOptionsRddMethod}
  rdd_interpolation_method: {RddInterpolationMethod}
  seismic_interpolation_method: {SeismicInterpolationMethod}
  time_units: {TimeUnits.GetDescription()}
  permeability_units_imperial: {PermeabilityUnitsImperial.GetDescription()}
  permeability_units_metric: {PermeabilityUnitsMetric.GetDescription()}
  borehole_num_div: {BoreholeNumDiv}
  borehole_define_by_thickness: {BoreholeDefineByThickness}
  surface_interp_method: {SurfaceInterpMethod}
  interpolate_top_borehole_surface: {InterpolateTopBoreholeSurface}
  excavate_above_borehole_top_surface: {ExcavateAboveBoreholeTopSurface}
  use_date_for_stages: {UseDateForStages}
  stage_names: {StageNames}";
        }
    }
}
