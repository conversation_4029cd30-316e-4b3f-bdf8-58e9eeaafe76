namespace Slide.Core.Objects.Sli
{
    public class StartDiscreteStrengthFns
    {
        public int Num { get; set; }

        public StartDiscreteStrengthFns()
        {

        }

        public StartDiscreteStrengthFns(StartDiscreteStrengthFns startDiscreteStrengthFns)
        {
            Num = startDiscreteStrengthFns.Num;
        }

        public override string ToString()
        {
            return $"\r\nstart discrete strength fns:\r\n  num: {Num}";
        }
    }
}
