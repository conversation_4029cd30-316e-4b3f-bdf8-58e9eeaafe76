namespace Slide.Core.Objects.Sli
{
    public class MaterialGenHoekBrown
    {
        public int Num { get; set; }
        public int Gsi { get; set; } = 50;
        public int Mi { get; set; } = 10;
        public int Disturbance { get; set; }
        public bool UseGsiMiD { get; set; }

        public MaterialGenHoekBrown()
        {

        }

        public MaterialGenHoekBrown(MaterialGenHoekBrown materialGenHoekBrown)
        {
            Num = materialGenHoekBrown.Num;
            Gsi = materialGenHoekBrown.Gsi;
            Mi = materialGenHoekBrown.Mi;
            Disturbance = materialGenHoekBrown.Disturbance;
            UseGsiMiD = materialGenHoekBrown.UseGsiMiD;
        }

        public override string ToString()
        {
            return $"  num: {Num}  gsi: {Gsi}  mi: {Mi}  disturbance: {Disturbance}  use_gsi_mi_d: {Convert.ToInt16(UseGsiMiD)}";
        }
    }
}
