namespace Slide.Core.Objects.Sli
{
    public class ShansepFunction
    {
        public int Index { get; set; }
        public double X { get; set; }
        public double Y { get; set; }

        public ShansepFunction()
        {

        }

        public ShansepFunction(ShansepFunction shansepFunction)
        {
            Index = shansepFunction.Index;
            X = shansepFunction.X;
            Y = shansepFunction.Y;
        }

        public override string ToString()
        {
            return $"\r\n      id1: {Index} x: {X} y: {Y}";
        }
    }
}
