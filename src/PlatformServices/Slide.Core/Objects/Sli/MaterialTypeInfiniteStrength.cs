namespace Slide.Core.Objects.Sli;

public class MaterialTypeInfiniteStrength : MaterialType
{
    public override string ToString()
    {
        return $"  soil{Index} = type: {(int)Type}" +
               $" uw: {Uw}" +
               $"{(Piezo ? " piezo: 1" : WTable ? " wtable: 1" : " wtable: 0")}" +
               $"{(AllowSliding.HasValue && AllowSliding.Value ? " allowsliding: 1" : " allowsliding: 0")}" +
               $" gw_mode: {GwMode}" +
               $" gw_user_index: {GwUserIndex}" +
               $" gw_k_ratio: {GwKRatio}" +
               $" gw_k_angle: {GwKAngle}" +
               $" gw_ks: {GwKs}" +
               $" SK: {SK}" +
               $" STYPE: {SType}" +
               $" gw_wc: {GwWc}" +
               $" gw_wcr: {GwWcr}" +
               $" gw_usemv: {GwUsemv}" +
               $" gw_mv: {GwMv}" +
               $" gw_use_wc_for_phib: {GwUseWcForPhiB}" +
               $" gw_phi_b: {GwPhiB}" +
               $" gw_aev: {GwAev}" +
               $" rd_undrained: {RdUndrained}" +
               $" rd_type: {RdType}" +
               $" rd_cr: {RdCr}" +
               $" rd_phir: {RdPhir}" +
               $" withru: {WithRu}" +
               $" stg2: {Stg2}" +
               $" bbar: {Bbar}" +
               $" use_alternate_strength: {Convert.ToInt32(UseAlternateStrength)}" +
               $" alternate_mat_index: {AlternateMaterialIndex}" +
               $" spatially_variable_material: {SpatiallyVariableMaterial}" +
               $" correlation_length_x: {CorrelationLengthX}" +
               $" correlation_length_y: {CorrelationLengthY}" +
               $" mesh_size: {MeshSize}";
    }
}
