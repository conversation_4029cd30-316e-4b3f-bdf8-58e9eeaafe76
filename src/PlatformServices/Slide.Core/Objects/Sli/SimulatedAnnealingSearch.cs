namespace Slide.Core.Objects.Sli
{
    public class SimulatedAnnealingSearch
    {
        public int NVertices { get; set; } = 8;
        public int NGen { get; set; } = 2000;
        public int MaxSteps { get; set; } = 20;
        public int Nepsilon { get; set; } = 5;
        public double FTol { get; set; } = 0.0001;
        public int C { get; set; } = 8;
        public bool Convex { get; set; } = true;
        public bool Optimize { get; set; } = true;
        public double? Depth { get; set; }

        public SimulatedAnnealingSearch()
        {

        }

        public SimulatedAnnealingSearch(SimulatedAnnealingSearch simulatedAnnealingSearch)
        {
            NVertices = simulatedAnnealingSearch.NVertices;
            NGen = simulatedAnnealingSearch.NGen;
            MaxSteps = simulatedAnnealingSearch.MaxSteps;
            Nepsilon = simulatedAnnealingSearch.Nepsilon;
            FTol = simulatedAnnealingSearch.FTol;
            C = simulatedAnnealingSearch.C;
            Convex = simulatedAnnealingSearch.Convex;
            Optimize = simulatedAnnealingSearch.Optimize;
            Depth = simulatedAnnealingSearch.Depth;
        }

        public override string ToString()
        {
            return $"\r\nsimulatedannealing search:\r\n  nvertices: {NVertices}\r\n  ngen: {NGen}\r\n  max_steps: {MaxSteps}\r\n  nepsilon: {Nepsilon}\r\n  ftol: {FTol}\r\n  c: {C}\r\n  convex: {Convert.ToInt32(Convex)}\r\n  optimize: {Convert.ToInt32(Optimize)}{(Depth.HasValue && Depth != 0 ? $"\r\n  depth: {Depth}" : "")}";
        }
    }
}
