namespace Slide.Core.Objects.Sli
{
    public class ShansepFunctionDefinition
    {
        public int Soil { get; set; }
        public List<ShansepFunction> Functions { get; set; }

        public ShansepFunctionDefinition()
        {

        }

        public ShansepFunctionDefinition(ShansepFunctionDefinition shansepFunctionDefinition)
        {
            Soil = shansepFunctionDefinition.Soil;
            Functions = shansepFunctionDefinition.Functions.Select(x => new ShansepFunction(x)).ToList();
        }

        public override string ToString()
        {
            var text = $"\r\n    soil: {Soil} numdata: {Functions.Count}";

            foreach (var function in Functions)
            {
                text += function.ToString();
            }

            return text;
        }
    }
}
