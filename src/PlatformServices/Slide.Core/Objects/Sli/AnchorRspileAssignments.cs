namespace Slide.Core.Objects.Sli
{
    public class AnchorRspileAssignments
    {
        public List<NumberMatAssign> NumMatAssign { get; set; }

        public AnchorRspileAssignments()
        {

        }

        public AnchorRspileAssignments(AnchorRspileAssignments anchorRspileAssignments)
        {
            NumMatAssign = anchorRspileAssignments.NumMatAssign.Select(x => new NumberMatAssign(x)).ToList();
        }

        public override string ToString()
        {
            var text = $"\r\nanchor_rspile_assignments:\r\nnum_anchors: {NumMatAssign.Count}";

            foreach (var numMatAssign in NumMatAssign)
            {
                text += numMatAssign.ToString();
            }

            return text;
        }
    }
}
