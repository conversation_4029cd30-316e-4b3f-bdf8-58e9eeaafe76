using Slide.Core.Enums;
using Slide.Core.Extensions;

namespace Slide.Core.Objects.Sli
{
    public class SoilProfileStart
    {
        public double SoilExtentsLeft { get; set; } = 0;
        public double SoilExtentsRight { get; set; } = 20;
        public double SoilExtentsTop { get; set; } = 0;
        public double SoilExtentsBottom { get; set; } = -10;
        public Option AlwaysReassign { get; set; } = Option.No;
        public Option ImportedFromRslog { get; set; }
        public SoilExtentsPointsStart SoilExtentsPointsStart { get; set; } = new();
        public EmptyClass SoilExtentsPointsEnd { get; set; }

        public SoilProfileStart()
        {

        }

        public SoilProfileStart(SoilProfileStart soilProfileStart)
        {
            SoilExtentsLeft = soilProfileStart.SoilExtentsLeft;
            SoilExtentsRight = soilProfileStart.SoilExtentsRight;
            SoilExtentsTop = soilProfileStart.SoilExtentsTop;
            SoilExtentsBottom = soilProfileStart.SoilExtentsBottom;
            AlwaysReassign = soilProfileStart.AlwaysReassign;
            ImportedFromRslog = soilProfileStart.ImportedFromRslog;
            SoilExtentsPointsStart = new(soilProfileStart.SoilExtentsPointsStart);
            SoilExtentsPointsEnd = new();
        }

        public override string ToString()
        {
            return $"\r\nsoil profile start:\r\n  soil_extents_left: {SoilExtentsLeft}\r\n  soil_extents_right: {SoilExtentsRight}\r\n  soil_extents_top: {SoilExtentsTop}\r\n  soil_extents_bottom: {SoilExtentsBottom}\r\n  always_reassign: {AlwaysReassign.GetDescription()}\r\n  imported_from_rslog: {ImportedFromRslog.GetDescription()}\r\n{SoilExtentsPointsStart}\r\nsoil profile end:";
        }
    }
}
