namespace Slide.Core.Objects.Sli
{
    public class BlockSearch
    {
        public int Samples { get; set; } = 5000;
        public int LeftStart { get; set; } = 135;
        public int LeftEnd { get; set; } = 135;
        public int RightStart { get; set; } = 45;
        public int RightEnd { get; set; } = 45;
        public bool Random { get; set; } = true;
        public bool Convex { get; set; } = true;
        public bool Optimize { get; set; } = true;
        public bool MultipleGroups { get; set; }
        public double? Depth { get; set; }
        public List<BlockSearchRegion> BlockSearchRegions { get; private set; } = new();

        public BlockSearch()
        {

        }

        public void AddBlockSearchRegions(List<BlockSearchRegion> regions)
        {
            foreach (var region in regions)
            {
                AddBlockSearchRegion(region);
            }
        }

        public void AddBlockSearchRegion(BlockSearchRegion blockSearchRegion)
        {
            blockSearchRegion.GroupId = BlockSearchRegions.Count + 1;

            BlockSearchRegions.Add(blockSearchRegion);
        }

        public BlockSearch(BlockSearch blockSearch)
        {
            Samples = blockSearch.Samples;
            LeftStart = blockSearch.LeftStart;
            LeftEnd = blockSearch.LeftEnd;
            RightStart = blockSearch.RightStart;
            RightEnd = blockSearch.RightEnd;
            Random = blockSearch.Random;
            Convex = blockSearch.Convex;
            Optimize = blockSearch.Optimize;
            MultipleGroups = blockSearch.MultipleGroups;
            Depth = blockSearch.Depth;
        }

        public override string ToString()
        {
            var @string = $"\r\nblock search:\r\n  samples: {Samples}\r\n  leftstart: {LeftStart}\r\n  leftend: {LeftEnd}\r\n  rightstart: {RightStart}\r\n  rightend: {RightEnd}\r\n  random: {Convert.ToInt16(Random)}\r\n  convex: {Convert.ToInt16(Convex)}\r\n  optimize: {Convert.ToInt16(Optimize)}\r\n  multiple_groups: {Convert.ToInt16(MultipleGroups)}{(Depth.HasValue && Depth != 0 ? $"\r\n  depth: {Depth}" : "")}";

            if (BlockSearchRegions.Any())
            {
                foreach (var region in BlockSearchRegions)
                {
                    @string += $"{region.ToString()}";
                }
            }

            return @string;
        }
    }
}
