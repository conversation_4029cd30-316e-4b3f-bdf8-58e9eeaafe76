namespace Slide.Core.Objects.Sli
{
    public class AnchorRspileData
    {
        public int RspileVersionNum { get; set; } = -1;
        public string RspileAnalysisMode { get; set; }
        public Guid? RspileGuid { get; set; }

        public AnchorRspileData()
        {

        }

        public AnchorRspileData(AnchorRspileData anchorRspileData)
        {
            RspileVersionNum = anchorRspileData.RspileVersionNum;
            RspileAnalysisMode = anchorRspileData.RspileAnalysisMode;
            RspileGuid = anchorRspileData.RspileGuid;
        }

        public override string ToString()
        {
            return $"\r\n  rspile_version_num: {RspileVersionNum} rspile_analysis_mode: {RspileAnalysisMode}\r\n  rspile_guid: {RspileGuid}";
        }
    }
}
