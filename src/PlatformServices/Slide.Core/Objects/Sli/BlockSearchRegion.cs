using Slide.Core.Enums;
using Slide.Core.Extensions;

namespace Slide.Core.Objects.Sli
{
    public class BlockSearchRegion
    {
        public BlockSearchType Type { get; set; } = BlockSearchType.Window;
        public int GroupId { get; set; } = 1;
        public Guid UniqueId { get; set; } = Guid.NewGuid();
        public PolyType? LeftPoint { get; set; }
        public PolyType? RightPoint { get; set; }
        public List<Vertice> Vertices { get; private set; } = new();

        public void AddVertice(Vertice vertice)
        {
            Vertices.Add(new()
            {
                X = vertice.X,
                Y = vertice.Y,
                Index = Vertices.Count + 1
            });
        }

        public override string ToString()
        {
            return $"\r\n  type: {Type.GetDescription()} group_id: {GroupId} unique_id: {{{UniqueId}}}\r\n{(LeftPoint.HasValue ? $"  left_point: {LeftPoint.GetDescription()}\r\n" : "")}{(RightPoint.HasValue ? $"  right_point: {RightPoint.GetDescription()}\r\n" : "")}{(Type == BlockSearchType.Polyline ? $"  length: {Vertices.Count}\r\n" : "")}  {string.Join("\r\n  ", Vertices.Select(v => v.ToString()))}";
        }
    }
}
