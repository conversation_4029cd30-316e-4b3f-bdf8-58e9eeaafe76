namespace Slide.Core.Objects.Sli
{
    public class Piezo
    {
        public Guid UniqueId { get; set; }
        public List<int> Vertices { get; set; } = new();
        public int Id { get; set; }

        public Piezo()
        {

        }

        public Piezo(Piezo piezo)
        {
            UniqueId = piezo.UniqueId;
            Vertices = new(piezo.Vertices);
            Id = piezo.Id;
        }

        public override string ToString()
        {
            return $"  {Id} unique_id: {{{UniqueId}}} vertices: [{string.Join(",", Vertices)}] id: {Id}\r\n";
        }
    }
}
