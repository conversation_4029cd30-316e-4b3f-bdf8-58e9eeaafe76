namespace Slide.Core.Objects.Sli
{
    public class Cell
    {
        public int? Index { get; set; }
        public int Vertice1 { get; set; }
        public int Vertice2 { get; set; }
        public int Vertice3 { get; set; }
        public string Material { get; set; }

        public Cell()
        {

        }

        public Cell(Cell cell)
        {
            Index = cell.Index;
            Vertice1 = cell.Vertice1;
            Vertice2 = cell.Vertice2;
            Vertice3 = cell.Vertice3;
            Material = cell.Material;
        }

        public override string ToString()
        {
            return $"  {Index}  vertices: [{Vertice1},{Vertice2},{Vertice3}] material: {Material}";
        }
    }
}
