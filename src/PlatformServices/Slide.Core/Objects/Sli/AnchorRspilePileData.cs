namespace Slide.Core.Objects.Sli
{
    public class AnchorRspilePileData
    {
        public List<AnchorRspileData> AnchorRspileData { get; set; }

        public AnchorRspilePileData()
        {

        }
        public AnchorRspilePileData(AnchorRspilePileData anchorRspilePileData)
        {
            AnchorRspileData = anchorRspilePileData.AnchorRspileData.Select(x => new AnchorRspileData(x)).ToList();
        }

        public override string ToString()
        {
            var text = $"\r\nanchor_rspile_pile_data:\r\nnum_anchors: {AnchorRspileData.Count}";

            foreach (var anchorRspileData in AnchorRspileData)
            {
                text += anchorRspileData.ToString();
            }

            return text;
        }
    }
}
