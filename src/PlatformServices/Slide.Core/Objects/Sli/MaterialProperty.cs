namespace Slide.Core.Objects.Sli
{
    public class MaterialProperty
    {
        public string Name { get; set; }
        public byte Red { get; set; }
        public byte Green { get; set; }
        public byte Blue { get; set; }
        public bool? HasHatch { get; set; }
        public int? Hatch2 { get; set; }
        public int? HatchClr { get; set; }
        public Guid Guid { get; set; } = Guid.NewGuid();

        public MaterialProperty()
        {

        }

        public MaterialProperty(MaterialProperty materialProperty)
        {
            Name = materialProperty.Name;
            Red = materialProperty.Red;
            Green = materialProperty.Green;
            Blue = materialProperty.Blue;
            HasHatch = materialProperty.HasHatch;
            Hatch2 = materialProperty.Hatch2;
            HatchClr = materialProperty.HatchClr;
            Guid = materialProperty.Guid;
        }

        public override string ToString()
        {
            return $"  {Name}    red: {Red}  green: {Green}  blue: {Blue}{(HasHatch.HasValue ? $"  has_hatch: {Convert.ToInt16(HasHatch)}" : "")}{(Hatch2.HasValue ? $"  hatch2: {Hatch2}" : "")}{(HatchClr.HasValue ? $"  hatch_clr: {HatchClr}" : "")} guid: {{{Guid}}}";
        }
    }
}
