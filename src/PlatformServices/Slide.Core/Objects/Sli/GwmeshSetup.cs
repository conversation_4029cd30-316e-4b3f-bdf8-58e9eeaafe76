namespace Slide.Core.Objects.Sli
{
    public class GwmeshSetup
    {
        public List<List<double>> Array { get; set; } = new()
        {
            new(){ 1500, 0 },
            new(){0, 0, 0.1}
        };

        public GwmeshSetup()
        {
        }

        public GwmeshSetup(GwmeshSetup gwmeshSetup)
        {
            Array = new(gwmeshSetup.Array);
        }

        public override string ToString()
        {
            var text = $"\r\ngwmesh setup:";

            foreach (var item in Array)
            {
                var subText = "\r\n";

                foreach (var item2 in item)
                {
                    subText += $"{item2} ";
                }

                text += subText;
            }

            return text;
        }
    }
}
