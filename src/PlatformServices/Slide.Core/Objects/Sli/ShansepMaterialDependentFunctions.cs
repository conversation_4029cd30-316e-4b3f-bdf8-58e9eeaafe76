namespace Slide.Core.Objects.Sli
{
    public class ShansepMaterialDependentFunctions
    {
        public int NumFunctions { get; set; }

        public ShansepMaterialDependentFunctions()
        {

        }

        public ShansepMaterialDependentFunctions(ShansepMaterialDependentFunctions shansepMaterialDependentFunctions)
        {
            NumFunctions = shansepMaterialDependentFunctions.NumFunctions;
        }

        public override string ToString()
        {
            return $"\r\nshansep material dependent functions:\r\n  numfunctions: {NumFunctions}";
        }
    }
}
