namespace Slide.Core.Objects.Sli
{
    public class GenAnisoAdvancedOptions
    {
        public int GaMultijointHandling { get; set; } = 1;
        public int GaConsiderWeakerBedding { get; set; } = 0;

        public GenAnisoAdvancedOptions()
        {

        }

        public GenAnisoAdvancedOptions(GenAnisoAdvancedOptions genAnisoAdvancedOptions)
        {
            GaMultijointHandling = genAnisoAdvancedOptions.GaMultijointHandling;
            GaConsiderWeakerBedding = genAnisoAdvancedOptions.GaConsiderWeakerBedding;
        }

        public override string ToString()
        {
            return $"\r\ngen aniso advanced options:\r\n  ga_multijoint_handling: {GaMultijointHandling}  ga_consider_weaker_bedding: {GaConsiderWeakerBedding}";
        }
    }
}
