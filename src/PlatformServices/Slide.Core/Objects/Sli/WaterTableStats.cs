namespace Slide.Core.Objects.Sli
{
    public class WaterTableStats
    {
        public StatsArray Stats { get; set; } = new()
        {
            Value1 = 2,
            Value2 = 0.5,
            Value3 = 0
        };
        public string DetermWtUsage { get; set; } = "Mean";

        public WaterTableStats()
        {

        }

        public WaterTableStats(WaterTableStats waterTableStats)
        {
            Stats = new StatsArray(waterTableStats.Stats);
            DetermWtUsage = waterTableStats.DetermWtUsage;
        }

        public override string ToString()
        {
            return $"\r\nwater table stats:\r\n    {Stats}\r\n    determ wt usage: {DetermWtUsage}";
        }
    }
}
