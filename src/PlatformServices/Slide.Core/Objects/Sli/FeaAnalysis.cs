namespace Slide.Core.Objects.Sli
{
    public class FeaAnalysis
    {
        public int MaxIter { get; set; } = 500;
        public double Tolerance { get; set; } = 1e-06;
        public bool History { get; set; } = true;
        public int FeaTimesteps { get; set; }
        public int FeaMaxIterTransient { get; set; } = 500;
        public double FeaToleranceTransient { get; set; } = 0.001;

        public FeaAnalysis() { }

        public FeaAnalysis(FeaAnalysis analysis)
        {
            MaxIter = analysis.MaxIter;
            Tolerance = analysis.Tolerance;
            History = analysis.History;
            FeaTimesteps = analysis.FeaTimesteps;
            FeaMaxIterTransient = analysis.FeaMaxIterTransient;
            FeaToleranceTransient = analysis.FeaToleranceTransient;
        }

        public override string ToString()
        {
            return $"\r\nfea analysis:\r\n  maxiter: {MaxIter} tolerance: {Tolerance} history: {Convert.ToInt16(History)} fea_timesteps: {FeaTimesteps} fea_maxitertransient: {FeaMaxIterTransient} fea_tolerancetransient: {FeaToleranceTransient}";
        }
    }
}
