namespace Slide.Core.Objects.Sli
{
    public class PsoSearch
    {
        public int NVertices { get; set; } = 8;
        public int NSurf { get; set; } = 1000;
        public int MaxIterations { get; set; } = 500;
        public int NumParticles { get; set; } = 50;
        public bool Optimize { get; set; } = true;
        public bool Convex { get; set; } = true;
        public bool BUnimodal { get; set; } = true;
        public double? Depth { get; set; }

        public PsoSearch()
        {

        }

        public PsoSearch(PsoSearch psoSearch)
        {
            NVertices = psoSearch.NVertices;
            NSurf = psoSearch.NSurf;
            MaxIterations = psoSearch.MaxIterations;
            NumParticles = psoSearch.NumParticles;
            Optimize = psoSearch.Optimize;
            Convex = psoSearch.Convex;
            BUnimodal = psoSearch.BUnimodal;
            Depth = psoSearch.Depth;
        }

        public override string ToString()
        {
            return $"\r\npso search:\r\n  nvertices: {NVertices}\r\n  nsurf: {NSurf}\r\n  maxiterations: {MaxIterations}\r\n  numparticles: {NumParticles}\r\n  optimize: {Convert.ToInt32(Optimize)}\r\n  convex: {Convert.ToInt32(Convex)}\r\n  b_unimodal: {Convert.ToInt32(BUnimodal)}{(Depth.HasValue && Depth != 0 ? $"\r\n  depth: {Depth}" : "")}";
        }
    }
}
