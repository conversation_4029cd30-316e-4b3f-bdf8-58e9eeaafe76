namespace Slide.Core.Objects.Sli
{
    public class MaterialGhbGsiDepth
    {
        public int Num { get; set; }
        public int Vd { get; set; }
        public int Gsi { get; set; } = 50;
        public int GsiV { get; set; }
        public int DGsi { get; set; }
        public int CGsi { get; set; }
        public int CutGsi { get; set; }
        public int DatGsi { get; set; }

        public MaterialGhbGsiDepth()
        {

        }

        public MaterialGhbGsiDepth(MaterialGhbGsiDepth materialGhbGsiDepth)
        {
            Num = materialGhbGsiDepth.Num;
            Vd = materialGhbGsiDepth.Vd;
            Gsi = materialGhbGsiDepth.Gsi;
            GsiV = materialGhbGsiDepth.GsiV;
            DGsi = materialGhbGsiDepth.DGsi;
            CGsi = materialGhbGsiDepth.CGsi;
            CutGsi = materialGhbGsiDepth.CutGsi;
            DatGsi = materialGhbGsiDepth.DatGsi;
        }

        public override string ToString()
        {
            return $"  num: {Num}  vd: {Vd}  gsi: {Gsi}  gsiv: {GsiV}  dgsi: {DGsi}  cgsi: {CGsi}  cutgsi: {CutGsi}  datgsi: {DatGsi}";
        }
    }
}
