namespace Slide.Core.Objects.Sli
{
    public class OptimizeSurfaces
    {
        public double MinDeltaFos { get; set; } = 1e-09;
        public double StepReductionFactor { get; set; } = 0.5;
        public int MaxIterations { get; set; } = 4000;
        public bool ConvexSurfacesOnly { get; set; }
        public bool WhichSurface { get; set; }
        public double LessThanFs { get; set; } = 1.5;
        public double SnapDistance { get; set; } = 0.01;
        public bool SnapDistanceAuto { get; set; } = true;
        public bool DoSnap { get; set; } = true;
        public bool UseChecks { get; set; } = true;
        public int MaxConcaveAngle { get; set; } = 2;
        public bool UseMaxConcaveAngle { get; set; } = true;
        public int TotalConcaveAngle { get; set; } = 2;
        public bool UseTotalConcaveAngle { get; set; } = true;
        public bool OptimizationOption { get; set; } = true;
        public int SaMaxIterations { get; set; } = 20;
        public double SaIterationTolerance { get; set; } = 0.001;
        public double SaRelativeTolerance { get; set; } = 0.0001;
        public int SaMaxConcaveAngle { get; set; } = 2;
        public bool SaUseMaxConcaveAngle { get; set; } = true;

        public OptimizeSurfaces()
        {

        }

        public OptimizeSurfaces(OptimizeSurfaces optimizeSurfaces)
        {
            MinDeltaFos = optimizeSurfaces.MinDeltaFos;
            StepReductionFactor = optimizeSurfaces.StepReductionFactor;
            MaxIterations = optimizeSurfaces.MaxIterations;
            ConvexSurfacesOnly = optimizeSurfaces.ConvexSurfacesOnly;
            WhichSurface = optimizeSurfaces.WhichSurface;
            LessThanFs = optimizeSurfaces.LessThanFs;
            SnapDistance = optimizeSurfaces.SnapDistance;
            SnapDistanceAuto = optimizeSurfaces.SnapDistanceAuto;
            DoSnap = optimizeSurfaces.DoSnap;
            UseChecks = optimizeSurfaces.UseChecks;
            MaxConcaveAngle = optimizeSurfaces.MaxConcaveAngle;
            UseMaxConcaveAngle = optimizeSurfaces.UseMaxConcaveAngle;
            TotalConcaveAngle = optimizeSurfaces.TotalConcaveAngle;
            UseTotalConcaveAngle = optimizeSurfaces.UseTotalConcaveAngle;
            OptimizationOption = optimizeSurfaces.OptimizationOption;
            SaMaxIterations = optimizeSurfaces.SaMaxIterations;
            SaIterationTolerance = optimizeSurfaces.SaIterationTolerance;
            SaRelativeTolerance = optimizeSurfaces.SaRelativeTolerance;
            SaMaxConcaveAngle = optimizeSurfaces.SaMaxConcaveAngle;
            SaUseMaxConcaveAngle = optimizeSurfaces.SaUseMaxConcaveAngle;
        }

        public override string ToString()
        {
            return $"\r\noptimize surfaces:\r\n  min_delta_fos: {MinDeltaFos}\r\n  step_reduction_factor: {StepReductionFactor}\r\n  max_iterations: {MaxIterations}\r\n  convex_surfaces_only: {Convert.ToInt32(ConvexSurfacesOnly)}\r\n  which_surface: {Convert.ToInt32(WhichSurface)}\r\n  less_than_fs: {LessThanFs}\r\n  snap_distance: {SnapDistance}\r\n  snap_distance_auto: {Convert.ToInt32(SnapDistanceAuto)}\r\n  do_snap: {Convert.ToInt32(DoSnap)}\r\n  use_checks: {Convert.ToInt32(UseChecks)}\r\n  max_concave_angle: {MaxConcaveAngle}\r\n  use_max_concave_angle: {Convert.ToInt32(UseMaxConcaveAngle)}\r\n  total_concave_angle: {TotalConcaveAngle}\r\n  use_total_concave_angle: {Convert.ToInt32(UseTotalConcaveAngle)}\r\n  optimization_option: {Convert.ToInt32(OptimizationOption)}\r\n  sa_max_iterations: {SaMaxIterations}\r\n  sa_iteration_tolerance: {SaIterationTolerance}\r\n  sa_relative_tolerance: {SaRelativeTolerance}\r\n  sa_max_concave_angle: {SaMaxConcaveAngle}\r\n  sa_use_max_concave_angle: {Convert.ToInt32(SaUseMaxConcaveAngle)}";
        }
    }
}
