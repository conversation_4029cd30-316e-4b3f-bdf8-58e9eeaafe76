namespace Slide.Core.Objects.Sli
{
    public class SlopeLimits
    {
        public double X1 { get; set; }
        public double Y1 { get; set; }
        public double X2 { get; set; }
        public double Y2 { get; set; }
        public double? X3 { get; set; }
        public double? Y3 { get; set; }
        public double? X4 { get; set; }
        public double? Y4 { get; set; }

        public SlopeLimits()
        {

        }

        public SlopeLimits(SlopeLimits slope)
        {
            X1 = slope.X1;
            Y1 = slope.Y1;
            X2 = slope.X2;
            Y2 = slope.Y2;
        }

        public override string ToString()
        {
            return $"\r\nslope limits:\r\n  x1: {X1} y1: {Y1} x2: {X2} y2: {Y2}{(X3.HasValue && Y3.HasValue ? $" x3: {X3} y3: {Y3}" : "")}{(X4.HasValue && Y4.HasValue ? $" x4: {X4} y4: {Y4}" : "")}";
        }
    }
}
