namespace Slide.Core.Objects.Sli
{
    public class MaterialGhbMiDepth
    {
        public int Num { get; set; }
        public int Mi { get; set; } = 10;
        public int Miv { get; set; }
        public int Dmi { get; set; }
        public int Cmi { get; set; }
        public int CutMi { get; set; }
        public int DatMi { get; set; }

        public MaterialGhbMiDepth()
        {

        }

        public MaterialGhbMiDepth(MaterialGhbMiDepth materialGhbMiDepth)
        {
            Num = materialGhbMiDepth.Num;
            Mi = materialGhbMiDepth.Mi;
            Miv = materialGhbMiDepth.Miv;
            Dmi = materialGhbMiDepth.Dmi;
            Cmi = materialGhbMiDepth.Cmi;
            CutMi = materialGhbMiDepth.CutMi;
            DatMi = materialGhbMiDepth.DatMi;
        }

        public override string ToString()
        {
            return $"  num: {Num}  mi: {Mi}  miv: {Miv}  dmi: {Dmi}  cmi: {Cmi}  cutmi: {CutMi}  datmi: {DatMi}";
        }
    }
}
