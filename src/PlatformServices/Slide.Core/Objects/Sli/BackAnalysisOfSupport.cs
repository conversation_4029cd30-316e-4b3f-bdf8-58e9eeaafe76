using Slide.Core.Enums;
using Slide.Core.Extensions;

namespace Slide.Core.Objects.Sli
{
    public class BackAnalysisOfSupport
    {
        public Option Compute { get; set; }
        public int Fs { get; set; } = 2;
        public bool LoadElevation { get; set; }

        public BackAnalysisOfSupport()
        {

        }

        public BackAnalysisOfSupport(BackAnalysisOfSupport backAnalysisOfSupport)
        {
            Compute = backAnalysisOfSupport.Compute;
            Fs = backAnalysisOfSupport.Fs;
            LoadElevation = backAnalysisOfSupport.LoadElevation;
        }

        public override string ToString()
        {
            return $"\r\nback analysis of support:\r\n  compute: {Compute.GetDescription()}\r\n  fs: {Fs}\r\n  load elevation: {Convert.ToInt32(LoadElevation)}";
        }
    }
}
