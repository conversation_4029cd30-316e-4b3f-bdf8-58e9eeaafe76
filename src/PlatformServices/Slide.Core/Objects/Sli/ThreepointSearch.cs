namespace Slide.Core.Objects.Sli
{
    public class ThreepointSearch
    {
        public bool Composite { get; set; } = true;
        public bool ReverseCurvature { get; set; } = true;
        public int Samples { get; set; } = 5000;
        public bool Random { get; set; } = true;
        public int WeightLower { get; set; } = 1;
        public double? Depth { get; set; }

        public ThreepointSearch()
        {

        }

        public ThreepointSearch(ThreepointSearch threepointSearch)
        {
            Composite = threepointSearch.Composite;
            ReverseCurvature = threepointSearch.ReverseCurvature;
            Samples = threepointSearch.Samples;
            Random = threepointSearch.Random;
            WeightLower = threepointSearch.WeightLower;
            Depth = threepointSearch.Depth;
        }

        public override string ToString()
        {
            return $"\r\nthreepoint search:\r\n  composite: {Convert.ToInt32(Composite)}\r\n  reverse_curvature: {Convert.ToInt32(ReverseCurvature)}\r\n  samples: {Samples}\r\n  random: {Convert.ToInt16(Random)}\r\n  weightlower: {WeightLower}{(Depth.HasValue && Depth != 0 ? $"\r\n  depth: {Depth}" : "")}";
        }
    }
}
