namespace Slide.Core.Objects.Sli
{
    public class FromBoreholesStart
    {
        public int Num { get; set; }

        public FromBoreholesStart()
        {

        }

        public FromBoreholesStart(FromBoreholesStart fromBoreholesStart)
        {
            Num = fromBoreholesStart.Num;
        }

        public override string ToString()
        {
            return $"  from boreholes start:\r\n    num: {Num}\r\n  from boreholes end:";
        }
    }
}
