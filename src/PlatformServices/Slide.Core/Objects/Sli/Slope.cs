namespace Slide.Core.Objects.Sli
{
    public class Slope
    {
        public int Index { get; set; }
        public List<int> Vertices { get; set; } = new();

        public Slope()
        {

        }

        public Slope(Slope slope)
        {
            Index = slope.Index;
            Vertices = new(slope.Vertices);
        }

        public override string ToString()
        {
            return $"\r\nslope:\r\n  {Index}  vertices: [{string.Join(",", Vertices)}]";
        }
    }
}
