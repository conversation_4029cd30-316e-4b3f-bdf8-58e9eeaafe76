using Slide.Core.Enums;
using Slide.Core.Extensions;

namespace Slide.Core.Objects.Sli
{
    public class SlammerOptionsStart
    {
        public int MAccelerationOptions { get; set; } = 3;
        public int MScalingOption { get; set; }
        public int MSlopeDisplacementOption { get; set; }
        public Option MLinearElasticModel { get; set; } = Option.Yes;
        public int MRigorousTypes { get; set; } = 2;
        public int MScaleFactor { get; set; } = 1;
        public int MSwvAbove { get; set; } = 300;
        public int MSwvBelow { get; set; } = 300;
        public int MDampingRatio { get; set; } = 5;
        public double MReferenceStrain { get; set; } = 0.05;
        public int MDigitizationInterval { get; set; }
        public NumCount MSeismicRecordStart { get; set; } = new();
        public EmptyClass MSeismicRecordEnd { get; set; }
        public NumCount MNewmarkUserTimesStart { get; set; } = new();
        public EmptyClass MNewmarkUserTimesEnd { get; set; }
        public NumCount MNewmarkUserAccelerationsStart { get; set; } = new();
        public EmptyClass MNewmarkUserAccelerationsEnd { get; set; }
        public int MEarthquakeId { get; set; }
        public int MRecordId { get; set; }
        public string MExampleEarthquakeText { get; set; } = "\"\"";

        public SlammerOptionsStart()
        {

        }

        public SlammerOptionsStart(SlammerOptionsStart slammerOptionsStart)
        {
            MAccelerationOptions = slammerOptionsStart.MAccelerationOptions;
            MScalingOption = slammerOptionsStart.MScalingOption;
            MSlopeDisplacementOption = slammerOptionsStart.MSlopeDisplacementOption;
            MLinearElasticModel = slammerOptionsStart.MLinearElasticModel;
            MRigorousTypes = slammerOptionsStart.MRigorousTypes;
            MScaleFactor = slammerOptionsStart.MScaleFactor;
            MSwvAbove = slammerOptionsStart.MSwvAbove;
            MSwvBelow = slammerOptionsStart.MSwvBelow;
            MDampingRatio = slammerOptionsStart.MDampingRatio;
            MReferenceStrain = slammerOptionsStart.MReferenceStrain;
            MDigitizationInterval = slammerOptionsStart.MDigitizationInterval;
            MSeismicRecordStart = new(slammerOptionsStart.MSeismicRecordStart);
            MSeismicRecordEnd = new();
            MNewmarkUserTimesStart = new(slammerOptionsStart.MNewmarkUserTimesStart);
            MNewmarkUserTimesEnd = new();
            MNewmarkUserAccelerationsStart = new(slammerOptionsStart.MNewmarkUserAccelerationsStart);
            MNewmarkUserAccelerationsEnd = new();
            MEarthquakeId = slammerOptionsStart.MEarthquakeId;
            MRecordId = slammerOptionsStart.MRecordId;
            MExampleEarthquakeText = slammerOptionsStart.MExampleEarthquakeText;
        }

        public override string ToString()
        {
            return $"\r\nslammer options start:\r\n  m_acceleration_options: {MAccelerationOptions}\r\n  m_scaling_option: {MScalingOption}\r\n  m_slope_displacement_option: {MSlopeDisplacementOption}\r\n  m_linear_elastic_model: {MLinearElasticModel.GetDescription()}\r\n  m_rigorous_types: {MRigorousTypes}\r\n  m_scale_factor: {MScaleFactor}\r\n  m_swv_above: {MSwvAbove}\r\n  m_swv_below: {MSwvBelow}\r\n  m_damping_ratio: {MDampingRatio}\r\n  m_reference_strain: {MReferenceStrain}\r\n  m_digitization_interval: {MDigitizationInterval}\r\n  m_seismic_record start: {MSeismicRecordStart}\r\n  m_seismic_record end: \r\n  m_newmark_user_times start: {MNewmarkUserTimesStart}\r\n  m_newmark_user_times end: \r\n  m_newmark_user_accelerations start: {MNewmarkUserAccelerationsStart}\r\n  m_newmark_user_accelerations end: \r\n  m_earthquake_id: {MEarthquakeId}\r\n  m_record_id: {MRecordId}\r\n  m_example_earthquake_text: {MExampleEarthquakeText}\r\nslammer options end:";
        }
    }
}
