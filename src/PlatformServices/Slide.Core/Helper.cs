//using System.ComponentModel;
//using System.Reflection;

//namespace Slide.Core
//{
//    public static class Helper
//    {
//        public static object FindEnumValue(Type enumType, string value)
//        {
//            var fields = enumType.GetFields(BindingFlags.Public | BindingFlags.Static);

//            foreach (var field in fields)
//            {
//                var attribute = field.GetCustomAttribute<DescriptionAttribute>();

//                if (attribute != null && attribute.Description == value)
//                {
//                    return field.GetValue(null);
//                }

//                if (field.Name == value)
//                {
//                    return field.GetValue(null);
//                }

//                if (int.TryParse(value, out var number))
//                {
//                    if ((int)field.GetValue(null) == number)
//                    {
//                        return field.GetValue(null);
//                    }
//                }
//            }

//            throw new ArgumentException($"Cannot parse '{value}' to enum {enumType.Name}");
//        }
//    }
//}

