using Slide.Core.Objects.Sli;
using System.Globalization;
using System.Text.RegularExpressions;

namespace Slide.Core
{
    public static class Parser
    {
        public static List<Vertice> ParseVertices(string input)
        {
            var definition = Regex.Match(input, @"vertices:((?:.*?\r?\n)+?(?=\r\n|$))").Groups[1].Value;
            definition = definition.Replace("\r", "");
            var lines = definition.Split("\n", StringSplitOptions.None);

            var vertices = new List<Vertice>();

            foreach (var line in lines)
            {
                if (string.IsNullOrEmpty(line))
                {
                    continue;
                }

                var split = line.Split(" ", StringSplitOptions.RemoveEmptyEntries);

                vertices.Add(new()
                {
                    Index = int.Parse(split[0]),
                    X = double.Parse(split[2], CultureInfo.InvariantCulture),
                    Y = double.Parse(split[4], CultureInfo.InvariantCulture)
                });
            }

            return vertices;
        }

        public static List<Vertice> ParseProfileVertices(string input)
        {
            var definition = Regex.Match(input, @"profile_vertices:((?:.*?\r?\n)+?(?=\r\n|$))").Groups[1].Value;
            definition = definition.Replace("\r", "");
            var lines = definition.Split("\n", StringSplitOptions.None);

            var vertices = new List<Vertice>();

            foreach (var line in lines)
            {
                if (string.IsNullOrEmpty(line))
                {
                    continue;
                }

                var split = line.Split(" ", StringSplitOptions.RemoveEmptyEntries);

                vertices.Add(new()
                {
                    Index = int.Parse(split[0]),
                    X = double.Parse(split[2], CultureInfo.InvariantCulture),
                    Y = double.Parse(split[4], CultureInfo.InvariantCulture)
                });
            }

            return vertices;
        }

        public static List<Cell> ParseCells(string input)
        {
            var definition = Regex.Match(input, @"cells:((?:.*?\r?\n)+?(?=\r\n|$))").Groups[1].Value;
            definition = definition.Replace("\r", "");
            var lines = definition.Split("\n", StringSplitOptions.None);

            var cells = new List<Cell>();

            foreach (var line in lines)
            {
                if (string.IsNullOrEmpty(line))
                {
                    continue;
                }

                var split = line.Split(" ", StringSplitOptions.RemoveEmptyEntries);
                var vertices = split[2].Replace("[", "").Replace("]", "").Split(",");

                cells.Add(new()
                {
                    Index = int.Parse(split[0]),
                    Vertice1 = int.Parse(vertices[0]),
                    Vertice2 = int.Parse(vertices[1]),
                    Vertice3 = int.Parse(vertices[2]),
                    Material = split[4]
                });
            }

            return cells;
        }

        public static List<Cell> ParseProfileCells(string input)
        {
            var definition = Regex.Match(input, @"profile_cells:((?:.*?\r?\n)+?(?=\r\n|$))").Groups[1].Value;
            definition = definition.Replace("\r", "");
            var lines = definition.Split("\n", StringSplitOptions.None);

            var cells = new List<Cell>();

            foreach (var line in lines)
            {
                if (string.IsNullOrEmpty(line))
                {
                    continue;
                }

                var split = line.Split(" ", StringSplitOptions.RemoveEmptyEntries);
                var vertices = split[2].Replace("[", "").Replace("]", "").Split(",");

                cells.Add(new()
                {
                    Index = int.Parse(split[0]),
                    Vertice1 = int.Parse(vertices[0]),
                    Vertice2 = int.Parse(vertices[1]),
                    Vertice3 = int.Parse(vertices[2]),
                    Material = split[4]
                });
            }

            return cells;
        }

        public static Slope ParseSlope(string input)
        {
            var definition = Regex.Match(input, @"slope:((?:.*?\r?\n)+?(?=\r\n|$))").Groups[1].Value;
            definition = definition.Replace("\r", "");
            var lines = definition.Split("\n", StringSplitOptions.RemoveEmptyEntries);
            var split = lines[0].Split(" ", StringSplitOptions.RemoveEmptyEntries);

            var vertices = split[2].Replace("[", "").Replace("]", "").Split(",");

            return new Slope()
            {
                Index = int.Parse(split[0]),
                Vertices = vertices.Select(x => int.Parse(x)).ToList()
            };
        }

        public static Exterior ParseExterior(string input)
        {
            var definition = Regex.Match(input, @"exterior:((?:.*?\r?\n)+?(?=\r\n|$))").Groups[1].Value;
            definition = definition.Replace("\r", "");
            var lines = definition.Split("\n", StringSplitOptions.RemoveEmptyEntries);
            var split = lines[0].Split(" ", StringSplitOptions.RemoveEmptyEntries);

            var vertices = split[2].Replace("[", "").Replace("]", "").Split(",");

            return new Exterior()
            {
                Index = int.Parse(split[0]),
                Vertices = vertices.Select(x => int.Parse(x)).ToList()
            };
        }

        public static SlopeLimits ParseSlopeLimits(string input)
        {
            var definition = Regex.Match(input, @"slope limits:((?:.*?\r?\n)+?(?=\r\n|$))").Groups[1].Value;
            definition = definition.Replace("\r", "");
            var lines = definition.Split("\n", StringSplitOptions.RemoveEmptyEntries);
            var split = lines[0].Split(" ", StringSplitOptions.RemoveEmptyEntries);


            return new SlopeLimits()
            {
                X1 = double.Parse(split[1], CultureInfo.InvariantCulture),
                Y1 = double.Parse(split[3], CultureInfo.InvariantCulture),
                X2 = double.Parse(split[5], CultureInfo.InvariantCulture),
                Y2 = double.Parse(split[7], CultureInfo.InvariantCulture)
            };
        }

        public static List<GeometryInfo> ParseGeometryInfo(string input)
        {
            var definition = Regex.Match(input, @"geometry info:((?:.*?\r?\n)+?(?=\r\n|$))").Groups[1].Value;
            definition = definition.Replace("\r", "");
            var lines = definition.Split("\n", StringSplitOptions.RemoveEmptyEntries);

            var geometryInfo = new List<GeometryInfo>();

            foreach (var line in lines)
            {
                if (string.IsNullOrEmpty(line) || line.StartsWith("#"))
                {
                    continue;
                }

                var split = line.Split(" ", StringSplitOptions.RemoveEmptyEntries);
                var coordinates = split[2].Split(",");

                geometryInfo.Add(new()
                {
                    MaterialType = int.Parse(split[0]),
                    EndFlag = split[1] == "1",
                    X = double.Parse(coordinates[0], CultureInfo.InvariantCulture),
                    Y = double.Parse(coordinates[1], CultureInfo.InvariantCulture),
                    Disc = split[3] == "1",
                    ABI = split[4] == "1",
                    Visible = split[5] == "1",
                    Temporary = split[6] == "1"
                });
            }

            return geometryInfo;
        }

        public static SoilProfileStart ParseSoilProfileStart(string input)
        {
            var definition = Regex.Match(input, @"soil profile start:((?:.*?\r?\n)+?(?=\r\n|$))").Groups[1].Value;
            definition = definition.Replace("\r", "");
            var lines = definition.Split("\n", StringSplitOptions.RemoveEmptyEntries);

            var dPointsDefinition = Regex.Matches(definition, @"\d+: \d+, -?\d+");
            var dPointsList = new List<DPoint>();

            foreach (var dPoint in dPointsDefinition)
            {
                var split = dPoint
                    .ToString()
                    .Replace(":", "")
                    .Replace(",", "")
                    .Split(" ", StringSplitOptions.RemoveEmptyEntries);

                if (split.Length > 0)
                {
                    dPointsList.Add(new()
                    {
                        Index = int.Parse(split[0]),
                        X = double.Parse(split[1], CultureInfo.InvariantCulture),
                        Y = double.Parse(split[2], CultureInfo.InvariantCulture)
                    });
                }
            }

            var soilProfile = new SoilProfileStart()
            {
                SoilExtentsLeft = double.Parse(lines[0].Split(" ", StringSplitOptions.RemoveEmptyEntries)[1], CultureInfo.InvariantCulture),
                SoilExtentsRight = double.Parse(lines[1].Split(" ", StringSplitOptions.RemoveEmptyEntries)[1], CultureInfo.InvariantCulture),
                SoilExtentsTop = double.Parse(lines[2].Split(" ", StringSplitOptions.RemoveEmptyEntries)[1], CultureInfo.InvariantCulture),
                SoilExtentsBottom = double.Parse(lines[3].Split(" ", StringSplitOptions.RemoveEmptyEntries)[1], CultureInfo.InvariantCulture),
                SoilExtentsPointsStart = new SoilExtentsPointsStart()
                {
                    DPointArrayStart = new()
                    {
                        DPoints = dPointsList
                    }
                }
            };

            return soilProfile;
        }
    }
}
