using Slide.Core.Objects.Sltm;
using System.Text;

namespace Slide.Core
{
    public class SltmFile
    {
        public string Version { get; set; } = "9.02";
        public ToolsData ToolsData { get; set; } = new();
        public ProjectSummary ProjectSummary { get; set; } = new();

        public string Save()
        {
            var sb = new StringBuilder();

            AppendVersion(sb);
            AppendToolsData(sb);
            AppendProjectSummary(sb);

            return sb.ToString();
        }

        private void AppendVersion(StringBuilder sb)
        {
            sb.AppendLine($"Slide Tools File:\r\n  version: {Version}");
        }

        private void AppendToolsData(StringBuilder sb)
        {
            sb.AppendLine(ToolsData.ToString());
        }

        private void AppendProjectSummary(StringBuilder sb)
        {
            sb.AppendLine($"project summary start:\r\n{ProjectSummary.ToString()}\r\nproject summary end:\r\n");
        }
    }
}
