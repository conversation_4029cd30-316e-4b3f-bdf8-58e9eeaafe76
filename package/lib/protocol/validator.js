"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "ValidationError", {
  enumerable: true,
  get: function () {
    return _validatorPrimitives.ValidationError;
  }
});
Object.defineProperty(exports, "createMetadataValidator", {
  enumerable: true,
  get: function () {
    return _validatorPrimitives.createMetadataValidator;
  }
});
Object.defineProperty(exports, "findValidator", {
  enumerable: true,
  get: function () {
    return _validatorPrimitives.findValidator;
  }
});
Object.defineProperty(exports, "maybeFindValidator", {
  enumerable: true,
  get: function () {
    return _validatorPrimitives.maybeFindValidator;
  }
});
var _validatorPrimitives = require("./validatorPrimitives");
/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// This file is generated by generate_channels.js, do not edit manually.

_validatorPrimitives.scheme.StackFrame = (0, _validatorPrimitives.tObject)({
  file: _validatorPrimitives.tString,
  line: _validatorPrimitives.tNumber,
  column: _validatorPrimitives.tNumber,
  function: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.Metadata = (0, _validatorPrimitives.tObject)({
  location: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    file: _validatorPrimitives.tString,
    line: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
    column: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
  })),
  apiName: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  internal: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  wallTime: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.ClientSideCallMetadata = (0, _validatorPrimitives.tObject)({
  id: _validatorPrimitives.tNumber,
  stack: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('StackFrame')))
});
_validatorPrimitives.scheme.Point = (0, _validatorPrimitives.tObject)({
  x: _validatorPrimitives.tNumber,
  y: _validatorPrimitives.tNumber
});
_validatorPrimitives.scheme.Rect = (0, _validatorPrimitives.tObject)({
  x: _validatorPrimitives.tNumber,
  y: _validatorPrimitives.tNumber,
  width: _validatorPrimitives.tNumber,
  height: _validatorPrimitives.tNumber
});
_validatorPrimitives.scheme.SerializedValue = (0, _validatorPrimitives.tObject)({
  n: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  b: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  s: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  v: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['null', 'undefined', 'NaN', 'Infinity', '-Infinity', '-0'])),
  d: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  u: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  bi: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  r: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    p: _validatorPrimitives.tString,
    f: _validatorPrimitives.tString
  })),
  a: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('SerializedValue'))),
  o: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tObject)({
    k: _validatorPrimitives.tString,
    v: (0, _validatorPrimitives.tType)('SerializedValue')
  }))),
  h: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  id: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  ref: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.SerializedArgument = (0, _validatorPrimitives.tObject)({
  value: (0, _validatorPrimitives.tType)('SerializedValue'),
  handles: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tChannel)('*'))
});
_validatorPrimitives.scheme.ExpectedTextValue = (0, _validatorPrimitives.tObject)({
  string: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  regexSource: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  regexFlags: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  matchSubstring: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  ignoreCase: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  normalizeWhiteSpace: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.AXNode = (0, _validatorPrimitives.tObject)({
  role: _validatorPrimitives.tString,
  name: _validatorPrimitives.tString,
  valueString: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  valueNumber: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  description: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  keyshortcuts: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  roledescription: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  valuetext: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  disabled: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  expanded: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  focused: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  modal: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  multiline: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  multiselectable: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  readonly: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  required: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  selected: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  checked: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['checked', 'unchecked', 'mixed'])),
  pressed: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['pressed', 'released', 'mixed'])),
  level: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  valuemin: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  valuemax: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  autocomplete: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  haspopup: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  invalid: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  orientation: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  children: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('AXNode')))
});
_validatorPrimitives.scheme.SetNetworkCookie = (0, _validatorPrimitives.tObject)({
  name: _validatorPrimitives.tString,
  value: _validatorPrimitives.tString,
  url: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  domain: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  path: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  expires: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  httpOnly: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  secure: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  sameSite: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['Strict', 'Lax', 'None']))
});
_validatorPrimitives.scheme.NetworkCookie = (0, _validatorPrimitives.tObject)({
  name: _validatorPrimitives.tString,
  value: _validatorPrimitives.tString,
  domain: _validatorPrimitives.tString,
  path: _validatorPrimitives.tString,
  expires: _validatorPrimitives.tNumber,
  httpOnly: _validatorPrimitives.tBoolean,
  secure: _validatorPrimitives.tBoolean,
  sameSite: (0, _validatorPrimitives.tEnum)(['Strict', 'Lax', 'None'])
});
_validatorPrimitives.scheme.NameValue = (0, _validatorPrimitives.tObject)({
  name: _validatorPrimitives.tString,
  value: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.OriginStorage = (0, _validatorPrimitives.tObject)({
  origin: _validatorPrimitives.tString,
  localStorage: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue'))
});
_validatorPrimitives.scheme.SerializedError = (0, _validatorPrimitives.tObject)({
  error: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    message: _validatorPrimitives.tString,
    name: _validatorPrimitives.tString,
    stack: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
  })),
  value: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('SerializedValue'))
});
_validatorPrimitives.scheme.RecordHarOptions = (0, _validatorPrimitives.tObject)({
  path: _validatorPrimitives.tString,
  content: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['embed', 'attach', 'omit'])),
  mode: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['full', 'minimal'])),
  urlGlob: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  urlRegexSource: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  urlRegexFlags: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.FormField = (0, _validatorPrimitives.tObject)({
  name: _validatorPrimitives.tString,
  value: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  file: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    name: _validatorPrimitives.tString,
    mimeType: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    buffer: _validatorPrimitives.tBinary
  }))
});
_validatorPrimitives.scheme.APIRequestContextInitializer = (0, _validatorPrimitives.tObject)({
  tracing: (0, _validatorPrimitives.tChannel)(['Tracing'])
});
_validatorPrimitives.scheme.APIRequestContextFetchParams = (0, _validatorPrimitives.tObject)({
  url: _validatorPrimitives.tString,
  params: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue'))),
  method: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  headers: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue'))),
  postData: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBinary),
  jsonData: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  formData: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue'))),
  multipartData: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('FormField'))),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  failOnStatusCode: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  ignoreHTTPSErrors: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  maxRedirects: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.APIRequestContextFetchResult = (0, _validatorPrimitives.tObject)({
  response: (0, _validatorPrimitives.tType)('APIResponse')
});
_validatorPrimitives.scheme.APIRequestContextFetchResponseBodyParams = (0, _validatorPrimitives.tObject)({
  fetchUid: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.APIRequestContextFetchResponseBodyResult = (0, _validatorPrimitives.tObject)({
  binary: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBinary)
});
_validatorPrimitives.scheme.APIRequestContextFetchLogParams = (0, _validatorPrimitives.tObject)({
  fetchUid: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.APIRequestContextFetchLogResult = (0, _validatorPrimitives.tObject)({
  log: (0, _validatorPrimitives.tArray)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.APIRequestContextStorageStateParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.APIRequestContextStorageStateResult = (0, _validatorPrimitives.tObject)({
  cookies: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NetworkCookie')),
  origins: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('OriginStorage'))
});
_validatorPrimitives.scheme.APIRequestContextDisposeAPIResponseParams = (0, _validatorPrimitives.tObject)({
  fetchUid: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.APIRequestContextDisposeAPIResponseResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.APIRequestContextDisposeParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.APIRequestContextDisposeResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.APIResponse = (0, _validatorPrimitives.tObject)({
  fetchUid: _validatorPrimitives.tString,
  url: _validatorPrimitives.tString,
  status: _validatorPrimitives.tNumber,
  statusText: _validatorPrimitives.tString,
  headers: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue'))
});
_validatorPrimitives.scheme.LifecycleEvent = (0, _validatorPrimitives.tEnum)(['load', 'domcontentloaded', 'networkidle', 'commit']);
_validatorPrimitives.scheme.LocalUtilsInitializer = (0, _validatorPrimitives.tObject)({
  deviceDescriptors: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tObject)({
    name: _validatorPrimitives.tString,
    descriptor: (0, _validatorPrimitives.tObject)({
      userAgent: _validatorPrimitives.tString,
      viewport: (0, _validatorPrimitives.tObject)({
        width: _validatorPrimitives.tNumber,
        height: _validatorPrimitives.tNumber
      }),
      screen: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
        width: _validatorPrimitives.tNumber,
        height: _validatorPrimitives.tNumber
      })),
      deviceScaleFactor: _validatorPrimitives.tNumber,
      isMobile: _validatorPrimitives.tBoolean,
      hasTouch: _validatorPrimitives.tBoolean,
      defaultBrowserType: (0, _validatorPrimitives.tEnum)(['chromium', 'firefox', 'webkit'])
    })
  }))
});
_validatorPrimitives.scheme.LocalUtilsZipParams = (0, _validatorPrimitives.tObject)({
  zipFile: _validatorPrimitives.tString,
  entries: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue')),
  stacksId: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  mode: (0, _validatorPrimitives.tEnum)(['write', 'append']),
  includeSources: _validatorPrimitives.tBoolean
});
_validatorPrimitives.scheme.LocalUtilsZipResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.LocalUtilsHarOpenParams = (0, _validatorPrimitives.tObject)({
  file: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.LocalUtilsHarOpenResult = (0, _validatorPrimitives.tObject)({
  harId: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  error: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.LocalUtilsHarLookupParams = (0, _validatorPrimitives.tObject)({
  harId: _validatorPrimitives.tString,
  url: _validatorPrimitives.tString,
  method: _validatorPrimitives.tString,
  headers: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue')),
  postData: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBinary),
  isNavigationRequest: _validatorPrimitives.tBoolean
});
_validatorPrimitives.scheme.LocalUtilsHarLookupResult = (0, _validatorPrimitives.tObject)({
  action: (0, _validatorPrimitives.tEnum)(['error', 'redirect', 'fulfill', 'noentry']),
  message: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  redirectURL: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  status: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  headers: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue'))),
  body: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBinary)
});
_validatorPrimitives.scheme.LocalUtilsHarCloseParams = (0, _validatorPrimitives.tObject)({
  harId: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.LocalUtilsHarCloseResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.LocalUtilsHarUnzipParams = (0, _validatorPrimitives.tObject)({
  zipFile: _validatorPrimitives.tString,
  harFile: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.LocalUtilsHarUnzipResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.LocalUtilsConnectParams = (0, _validatorPrimitives.tObject)({
  wsEndpoint: _validatorPrimitives.tString,
  headers: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tAny),
  exposeNetwork: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  slowMo: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  socksProxyRedirectPortForTest: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.LocalUtilsConnectResult = (0, _validatorPrimitives.tObject)({
  pipe: (0, _validatorPrimitives.tChannel)(['JsonPipe']),
  headers: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue'))
});
_validatorPrimitives.scheme.LocalUtilsTracingStartedParams = (0, _validatorPrimitives.tObject)({
  tracesDir: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  traceName: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.LocalUtilsTracingStartedResult = (0, _validatorPrimitives.tObject)({
  stacksId: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.LocalUtilsAddStackToTracingNoReplyParams = (0, _validatorPrimitives.tObject)({
  callData: (0, _validatorPrimitives.tType)('ClientSideCallMetadata')
});
_validatorPrimitives.scheme.LocalUtilsAddStackToTracingNoReplyResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.LocalUtilsTraceDiscardedParams = (0, _validatorPrimitives.tObject)({
  stacksId: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.LocalUtilsTraceDiscardedResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.RootInitializer = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.RootInitializeParams = (0, _validatorPrimitives.tObject)({
  sdkLanguage: (0, _validatorPrimitives.tEnum)(['javascript', 'python', 'java', 'csharp'])
});
_validatorPrimitives.scheme.RootInitializeResult = (0, _validatorPrimitives.tObject)({
  playwright: (0, _validatorPrimitives.tChannel)(['Playwright'])
});
_validatorPrimitives.scheme.PlaywrightInitializer = (0, _validatorPrimitives.tObject)({
  chromium: (0, _validatorPrimitives.tChannel)(['BrowserType']),
  firefox: (0, _validatorPrimitives.tChannel)(['BrowserType']),
  webkit: (0, _validatorPrimitives.tChannel)(['BrowserType']),
  android: (0, _validatorPrimitives.tChannel)(['Android']),
  electron: (0, _validatorPrimitives.tChannel)(['Electron']),
  utils: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['LocalUtils'])),
  selectors: (0, _validatorPrimitives.tChannel)(['Selectors']),
  preLaunchedBrowser: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['Browser'])),
  preConnectedAndroidDevice: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['AndroidDevice'])),
  socksSupport: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['SocksSupport']))
});
_validatorPrimitives.scheme.PlaywrightNewRequestParams = (0, _validatorPrimitives.tObject)({
  baseURL: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  userAgent: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  ignoreHTTPSErrors: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  extraHTTPHeaders: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue'))),
  httpCredentials: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    username: _validatorPrimitives.tString,
    password: _validatorPrimitives.tString,
    origin: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
  })),
  proxy: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    server: _validatorPrimitives.tString,
    bypass: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    username: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    password: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
  })),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  storageState: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    cookies: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NetworkCookie'))),
    origins: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('OriginStorage')))
  })),
  tracesDir: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.PlaywrightNewRequestResult = (0, _validatorPrimitives.tObject)({
  request: (0, _validatorPrimitives.tChannel)(['APIRequestContext'])
});
_validatorPrimitives.scheme.RecorderSource = (0, _validatorPrimitives.tObject)({
  isRecorded: _validatorPrimitives.tBoolean,
  id: _validatorPrimitives.tString,
  label: _validatorPrimitives.tString,
  text: _validatorPrimitives.tString,
  language: _validatorPrimitives.tString,
  highlight: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tObject)({
    line: _validatorPrimitives.tNumber,
    type: _validatorPrimitives.tString
  })),
  revealLine: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  group: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.DebugControllerInitializer = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.DebugControllerInspectRequestedEvent = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  locator: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.DebugControllerSetModeRequestedEvent = (0, _validatorPrimitives.tObject)({
  mode: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.DebugControllerStateChangedEvent = (0, _validatorPrimitives.tObject)({
  pageCount: _validatorPrimitives.tNumber
});
_validatorPrimitives.scheme.DebugControllerSourceChangedEvent = (0, _validatorPrimitives.tObject)({
  text: _validatorPrimitives.tString,
  header: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  footer: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  actions: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)(_validatorPrimitives.tString))
});
_validatorPrimitives.scheme.DebugControllerPausedEvent = (0, _validatorPrimitives.tObject)({
  paused: _validatorPrimitives.tBoolean
});
_validatorPrimitives.scheme.DebugControllerInitializeParams = (0, _validatorPrimitives.tObject)({
  codegenId: _validatorPrimitives.tString,
  sdkLanguage: (0, _validatorPrimitives.tEnum)(['javascript', 'python', 'java', 'csharp'])
});
_validatorPrimitives.scheme.DebugControllerInitializeResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.DebugControllerSetReportStateChangedParams = (0, _validatorPrimitives.tObject)({
  enabled: _validatorPrimitives.tBoolean
});
_validatorPrimitives.scheme.DebugControllerSetReportStateChangedResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.DebugControllerResetForReuseParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.DebugControllerResetForReuseResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.DebugControllerNavigateParams = (0, _validatorPrimitives.tObject)({
  url: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.DebugControllerNavigateResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.DebugControllerSetRecorderModeParams = (0, _validatorPrimitives.tObject)({
  mode: (0, _validatorPrimitives.tEnum)(['inspecting', 'recording', 'none']),
  testIdAttributeName: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.DebugControllerSetRecorderModeResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.DebugControllerHighlightParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.DebugControllerHighlightResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.DebugControllerHideHighlightParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.DebugControllerHideHighlightResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.DebugControllerResumeParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.DebugControllerResumeResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.DebugControllerKillParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.DebugControllerKillResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.DebugControllerCloseAllBrowsersParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.DebugControllerCloseAllBrowsersResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.SocksSupportInitializer = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.SocksSupportSocksRequestedEvent = (0, _validatorPrimitives.tObject)({
  uid: _validatorPrimitives.tString,
  host: _validatorPrimitives.tString,
  port: _validatorPrimitives.tNumber
});
_validatorPrimitives.scheme.SocksSupportSocksDataEvent = (0, _validatorPrimitives.tObject)({
  uid: _validatorPrimitives.tString,
  data: _validatorPrimitives.tBinary
});
_validatorPrimitives.scheme.SocksSupportSocksClosedEvent = (0, _validatorPrimitives.tObject)({
  uid: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.SocksSupportSocksConnectedParams = (0, _validatorPrimitives.tObject)({
  uid: _validatorPrimitives.tString,
  host: _validatorPrimitives.tString,
  port: _validatorPrimitives.tNumber
});
_validatorPrimitives.scheme.SocksSupportSocksConnectedResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.SocksSupportSocksFailedParams = (0, _validatorPrimitives.tObject)({
  uid: _validatorPrimitives.tString,
  errorCode: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.SocksSupportSocksFailedResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.SocksSupportSocksDataParams = (0, _validatorPrimitives.tObject)({
  uid: _validatorPrimitives.tString,
  data: _validatorPrimitives.tBinary
});
_validatorPrimitives.scheme.SocksSupportSocksDataResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.SocksSupportSocksErrorParams = (0, _validatorPrimitives.tObject)({
  uid: _validatorPrimitives.tString,
  error: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.SocksSupportSocksErrorResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.SocksSupportSocksEndParams = (0, _validatorPrimitives.tObject)({
  uid: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.SocksSupportSocksEndResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.SelectorsInitializer = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.SelectorsRegisterParams = (0, _validatorPrimitives.tObject)({
  name: _validatorPrimitives.tString,
  source: _validatorPrimitives.tString,
  contentScript: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.SelectorsRegisterResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.SelectorsSetTestIdAttributeNameParams = (0, _validatorPrimitives.tObject)({
  testIdAttributeName: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.SelectorsSetTestIdAttributeNameResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserTypeInitializer = (0, _validatorPrimitives.tObject)({
  executablePath: _validatorPrimitives.tString,
  name: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.BrowserTypeLaunchParams = (0, _validatorPrimitives.tObject)({
  channel: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  executablePath: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  args: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)(_validatorPrimitives.tString)),
  ignoreAllDefaultArgs: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  ignoreDefaultArgs: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)(_validatorPrimitives.tString)),
  handleSIGINT: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  handleSIGTERM: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  handleSIGHUP: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  env: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue'))),
  headless: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  devtools: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  proxy: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    server: _validatorPrimitives.tString,
    bypass: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    username: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    password: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
  })),
  downloadsPath: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  tracesDir: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  chromiumSandbox: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  firefoxUserPrefs: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tAny),
  slowMo: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.BrowserTypeLaunchResult = (0, _validatorPrimitives.tObject)({
  browser: (0, _validatorPrimitives.tChannel)(['Browser'])
});
_validatorPrimitives.scheme.BrowserTypeLaunchPersistentContextParams = (0, _validatorPrimitives.tObject)({
  channel: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  executablePath: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  args: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)(_validatorPrimitives.tString)),
  ignoreAllDefaultArgs: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  ignoreDefaultArgs: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)(_validatorPrimitives.tString)),
  handleSIGINT: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  handleSIGTERM: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  handleSIGHUP: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  env: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue'))),
  headless: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  devtools: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  proxy: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    server: _validatorPrimitives.tString,
    bypass: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    username: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    password: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
  })),
  downloadsPath: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  tracesDir: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  chromiumSandbox: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  firefoxUserPrefs: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tAny),
  noDefaultViewport: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  viewport: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    width: _validatorPrimitives.tNumber,
    height: _validatorPrimitives.tNumber
  })),
  screen: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    width: _validatorPrimitives.tNumber,
    height: _validatorPrimitives.tNumber
  })),
  ignoreHTTPSErrors: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  javaScriptEnabled: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  bypassCSP: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  userAgent: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  locale: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  timezoneId: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  geolocation: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    longitude: _validatorPrimitives.tNumber,
    latitude: _validatorPrimitives.tNumber,
    accuracy: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
  })),
  permissions: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)(_validatorPrimitives.tString)),
  extraHTTPHeaders: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue'))),
  offline: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  httpCredentials: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    username: _validatorPrimitives.tString,
    password: _validatorPrimitives.tString,
    origin: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
  })),
  deviceScaleFactor: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  isMobile: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  hasTouch: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  colorScheme: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['dark', 'light', 'no-preference', 'no-override'])),
  reducedMotion: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['reduce', 'no-preference', 'no-override'])),
  forcedColors: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['active', 'none', 'no-override'])),
  acceptDownloads: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['accept', 'deny', 'internal-browser-default'])),
  baseURL: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  recordVideo: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    dir: _validatorPrimitives.tString,
    size: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
      width: _validatorPrimitives.tNumber,
      height: _validatorPrimitives.tNumber
    }))
  })),
  recordHar: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('RecordHarOptions')),
  strictSelectors: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  serviceWorkers: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['allow', 'block'])),
  userDataDir: _validatorPrimitives.tString,
  slowMo: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.BrowserTypeLaunchPersistentContextResult = (0, _validatorPrimitives.tObject)({
  context: (0, _validatorPrimitives.tChannel)(['BrowserContext'])
});
_validatorPrimitives.scheme.BrowserTypeConnectOverCDPParams = (0, _validatorPrimitives.tObject)({
  endpointURL: _validatorPrimitives.tString,
  headers: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue'))),
  slowMo: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.BrowserTypeConnectOverCDPResult = (0, _validatorPrimitives.tObject)({
  browser: (0, _validatorPrimitives.tChannel)(['Browser']),
  defaultContext: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['BrowserContext']))
});
_validatorPrimitives.scheme.BrowserInitializer = (0, _validatorPrimitives.tObject)({
  version: _validatorPrimitives.tString,
  name: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.BrowserCloseEvent = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserCloseParams = (0, _validatorPrimitives.tObject)({
  reason: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.BrowserCloseResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserKillForTestsParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserKillForTestsResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserDefaultUserAgentForTestParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserDefaultUserAgentForTestResult = (0, _validatorPrimitives.tObject)({
  userAgent: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.BrowserNewContextParams = (0, _validatorPrimitives.tObject)({
  noDefaultViewport: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  viewport: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    width: _validatorPrimitives.tNumber,
    height: _validatorPrimitives.tNumber
  })),
  screen: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    width: _validatorPrimitives.tNumber,
    height: _validatorPrimitives.tNumber
  })),
  ignoreHTTPSErrors: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  javaScriptEnabled: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  bypassCSP: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  userAgent: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  locale: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  timezoneId: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  geolocation: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    longitude: _validatorPrimitives.tNumber,
    latitude: _validatorPrimitives.tNumber,
    accuracy: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
  })),
  permissions: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)(_validatorPrimitives.tString)),
  extraHTTPHeaders: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue'))),
  offline: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  httpCredentials: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    username: _validatorPrimitives.tString,
    password: _validatorPrimitives.tString,
    origin: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
  })),
  deviceScaleFactor: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  isMobile: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  hasTouch: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  colorScheme: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['dark', 'light', 'no-preference', 'no-override'])),
  reducedMotion: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['reduce', 'no-preference', 'no-override'])),
  forcedColors: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['active', 'none', 'no-override'])),
  acceptDownloads: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['accept', 'deny', 'internal-browser-default'])),
  baseURL: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  recordVideo: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    dir: _validatorPrimitives.tString,
    size: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
      width: _validatorPrimitives.tNumber,
      height: _validatorPrimitives.tNumber
    }))
  })),
  recordHar: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('RecordHarOptions')),
  strictSelectors: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  serviceWorkers: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['allow', 'block'])),
  proxy: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    server: _validatorPrimitives.tString,
    bypass: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    username: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    password: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
  })),
  storageState: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    cookies: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('SetNetworkCookie'))),
    origins: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('OriginStorage')))
  }))
});
_validatorPrimitives.scheme.BrowserNewContextResult = (0, _validatorPrimitives.tObject)({
  context: (0, _validatorPrimitives.tChannel)(['BrowserContext'])
});
_validatorPrimitives.scheme.BrowserNewContextForReuseParams = (0, _validatorPrimitives.tObject)({
  noDefaultViewport: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  viewport: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    width: _validatorPrimitives.tNumber,
    height: _validatorPrimitives.tNumber
  })),
  screen: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    width: _validatorPrimitives.tNumber,
    height: _validatorPrimitives.tNumber
  })),
  ignoreHTTPSErrors: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  javaScriptEnabled: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  bypassCSP: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  userAgent: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  locale: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  timezoneId: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  geolocation: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    longitude: _validatorPrimitives.tNumber,
    latitude: _validatorPrimitives.tNumber,
    accuracy: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
  })),
  permissions: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)(_validatorPrimitives.tString)),
  extraHTTPHeaders: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue'))),
  offline: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  httpCredentials: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    username: _validatorPrimitives.tString,
    password: _validatorPrimitives.tString,
    origin: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
  })),
  deviceScaleFactor: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  isMobile: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  hasTouch: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  colorScheme: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['dark', 'light', 'no-preference', 'no-override'])),
  reducedMotion: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['reduce', 'no-preference', 'no-override'])),
  forcedColors: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['active', 'none', 'no-override'])),
  acceptDownloads: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['accept', 'deny', 'internal-browser-default'])),
  baseURL: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  recordVideo: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    dir: _validatorPrimitives.tString,
    size: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
      width: _validatorPrimitives.tNumber,
      height: _validatorPrimitives.tNumber
    }))
  })),
  recordHar: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('RecordHarOptions')),
  strictSelectors: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  serviceWorkers: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['allow', 'block'])),
  proxy: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    server: _validatorPrimitives.tString,
    bypass: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    username: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    password: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
  })),
  storageState: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    cookies: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('SetNetworkCookie'))),
    origins: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('OriginStorage')))
  }))
});
_validatorPrimitives.scheme.BrowserNewContextForReuseResult = (0, _validatorPrimitives.tObject)({
  context: (0, _validatorPrimitives.tChannel)(['BrowserContext'])
});
_validatorPrimitives.scheme.BrowserStopPendingOperationsParams = (0, _validatorPrimitives.tObject)({
  reason: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.BrowserStopPendingOperationsResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserNewBrowserCDPSessionParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserNewBrowserCDPSessionResult = (0, _validatorPrimitives.tObject)({
  session: (0, _validatorPrimitives.tChannel)(['CDPSession'])
});
_validatorPrimitives.scheme.BrowserStartTracingParams = (0, _validatorPrimitives.tObject)({
  page: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['Page'])),
  screenshots: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  categories: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)(_validatorPrimitives.tString))
});
_validatorPrimitives.scheme.BrowserStartTracingResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserStopTracingParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserStopTracingResult = (0, _validatorPrimitives.tObject)({
  artifact: (0, _validatorPrimitives.tChannel)(['Artifact'])
});
_validatorPrimitives.scheme.EventTargetInitializer = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.EventTargetWaitForEventInfoParams = (0, _validatorPrimitives.tObject)({
  info: (0, _validatorPrimitives.tObject)({
    waitId: _validatorPrimitives.tString,
    phase: (0, _validatorPrimitives.tEnum)(['before', 'after', 'log']),
    event: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    message: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    error: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
  })
});
_validatorPrimitives.scheme.BrowserContextWaitForEventInfoParams = (0, _validatorPrimitives.tType)('EventTargetWaitForEventInfoParams');
_validatorPrimitives.scheme.PageWaitForEventInfoParams = (0, _validatorPrimitives.tType)('EventTargetWaitForEventInfoParams');
_validatorPrimitives.scheme.WebSocketWaitForEventInfoParams = (0, _validatorPrimitives.tType)('EventTargetWaitForEventInfoParams');
_validatorPrimitives.scheme.ElectronApplicationWaitForEventInfoParams = (0, _validatorPrimitives.tType)('EventTargetWaitForEventInfoParams');
_validatorPrimitives.scheme.AndroidDeviceWaitForEventInfoParams = (0, _validatorPrimitives.tType)('EventTargetWaitForEventInfoParams');
_validatorPrimitives.scheme.EventTargetWaitForEventInfoResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserContextWaitForEventInfoResult = (0, _validatorPrimitives.tType)('EventTargetWaitForEventInfoResult');
_validatorPrimitives.scheme.PageWaitForEventInfoResult = (0, _validatorPrimitives.tType)('EventTargetWaitForEventInfoResult');
_validatorPrimitives.scheme.WebSocketWaitForEventInfoResult = (0, _validatorPrimitives.tType)('EventTargetWaitForEventInfoResult');
_validatorPrimitives.scheme.ElectronApplicationWaitForEventInfoResult = (0, _validatorPrimitives.tType)('EventTargetWaitForEventInfoResult');
_validatorPrimitives.scheme.AndroidDeviceWaitForEventInfoResult = (0, _validatorPrimitives.tType)('EventTargetWaitForEventInfoResult');
_validatorPrimitives.scheme.BrowserContextInitializer = (0, _validatorPrimitives.tObject)({
  isChromium: _validatorPrimitives.tBoolean,
  requestContext: (0, _validatorPrimitives.tChannel)(['APIRequestContext']),
  tracing: (0, _validatorPrimitives.tChannel)(['Tracing'])
});
_validatorPrimitives.scheme.BrowserContextBindingCallEvent = (0, _validatorPrimitives.tObject)({
  binding: (0, _validatorPrimitives.tChannel)(['BindingCall'])
});
_validatorPrimitives.scheme.BrowserContextConsoleEvent = (0, _validatorPrimitives.tObject)({
  type: _validatorPrimitives.tString,
  text: _validatorPrimitives.tString,
  args: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tChannel)(['ElementHandle', 'JSHandle'])),
  location: (0, _validatorPrimitives.tObject)({
    url: _validatorPrimitives.tString,
    lineNumber: _validatorPrimitives.tNumber,
    columnNumber: _validatorPrimitives.tNumber
  }),
  page: (0, _validatorPrimitives.tChannel)(['Page'])
});
_validatorPrimitives.scheme.BrowserContextCloseEvent = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserContextDialogEvent = (0, _validatorPrimitives.tObject)({
  dialog: (0, _validatorPrimitives.tChannel)(['Dialog'])
});
_validatorPrimitives.scheme.BrowserContextPageEvent = (0, _validatorPrimitives.tObject)({
  page: (0, _validatorPrimitives.tChannel)(['Page'])
});
_validatorPrimitives.scheme.BrowserContextPageErrorEvent = (0, _validatorPrimitives.tObject)({
  error: (0, _validatorPrimitives.tType)('SerializedError'),
  page: (0, _validatorPrimitives.tChannel)(['Page'])
});
_validatorPrimitives.scheme.BrowserContextRouteEvent = (0, _validatorPrimitives.tObject)({
  route: (0, _validatorPrimitives.tChannel)(['Route'])
});
_validatorPrimitives.scheme.BrowserContextVideoEvent = (0, _validatorPrimitives.tObject)({
  artifact: (0, _validatorPrimitives.tChannel)(['Artifact'])
});
_validatorPrimitives.scheme.BrowserContextBackgroundPageEvent = (0, _validatorPrimitives.tObject)({
  page: (0, _validatorPrimitives.tChannel)(['Page'])
});
_validatorPrimitives.scheme.BrowserContextServiceWorkerEvent = (0, _validatorPrimitives.tObject)({
  worker: (0, _validatorPrimitives.tChannel)(['Worker'])
});
_validatorPrimitives.scheme.BrowserContextRequestEvent = (0, _validatorPrimitives.tObject)({
  request: (0, _validatorPrimitives.tChannel)(['Request']),
  page: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['Page']))
});
_validatorPrimitives.scheme.BrowserContextRequestFailedEvent = (0, _validatorPrimitives.tObject)({
  request: (0, _validatorPrimitives.tChannel)(['Request']),
  failureText: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  responseEndTiming: _validatorPrimitives.tNumber,
  page: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['Page']))
});
_validatorPrimitives.scheme.BrowserContextRequestFinishedEvent = (0, _validatorPrimitives.tObject)({
  request: (0, _validatorPrimitives.tChannel)(['Request']),
  response: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['Response'])),
  responseEndTiming: _validatorPrimitives.tNumber,
  page: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['Page']))
});
_validatorPrimitives.scheme.BrowserContextResponseEvent = (0, _validatorPrimitives.tObject)({
  response: (0, _validatorPrimitives.tChannel)(['Response']),
  page: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['Page']))
});
_validatorPrimitives.scheme.BrowserContextAddCookiesParams = (0, _validatorPrimitives.tObject)({
  cookies: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('SetNetworkCookie'))
});
_validatorPrimitives.scheme.BrowserContextAddCookiesResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserContextAddInitScriptParams = (0, _validatorPrimitives.tObject)({
  source: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.BrowserContextAddInitScriptResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserContextClearCookiesParams = (0, _validatorPrimitives.tObject)({
  name: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  nameRegexSource: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  nameRegexFlags: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  domain: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  domainRegexSource: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  domainRegexFlags: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  path: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  pathRegexSource: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  pathRegexFlags: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.BrowserContextClearCookiesResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserContextClearPermissionsParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserContextClearPermissionsResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserContextCloseParams = (0, _validatorPrimitives.tObject)({
  reason: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.BrowserContextCloseResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserContextCookiesParams = (0, _validatorPrimitives.tObject)({
  urls: (0, _validatorPrimitives.tArray)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.BrowserContextCookiesResult = (0, _validatorPrimitives.tObject)({
  cookies: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NetworkCookie'))
});
_validatorPrimitives.scheme.BrowserContextExposeBindingParams = (0, _validatorPrimitives.tObject)({
  name: _validatorPrimitives.tString,
  needsHandle: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.BrowserContextExposeBindingResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserContextGrantPermissionsParams = (0, _validatorPrimitives.tObject)({
  permissions: (0, _validatorPrimitives.tArray)(_validatorPrimitives.tString),
  origin: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.BrowserContextGrantPermissionsResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserContextNewPageParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserContextNewPageResult = (0, _validatorPrimitives.tObject)({
  page: (0, _validatorPrimitives.tChannel)(['Page'])
});
_validatorPrimitives.scheme.BrowserContextSetDefaultNavigationTimeoutNoReplyParams = (0, _validatorPrimitives.tObject)({
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.BrowserContextSetDefaultNavigationTimeoutNoReplyResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserContextSetDefaultTimeoutNoReplyParams = (0, _validatorPrimitives.tObject)({
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.BrowserContextSetDefaultTimeoutNoReplyResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserContextSetExtraHTTPHeadersParams = (0, _validatorPrimitives.tObject)({
  headers: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue'))
});
_validatorPrimitives.scheme.BrowserContextSetExtraHTTPHeadersResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserContextSetGeolocationParams = (0, _validatorPrimitives.tObject)({
  geolocation: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    longitude: _validatorPrimitives.tNumber,
    latitude: _validatorPrimitives.tNumber,
    accuracy: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
  }))
});
_validatorPrimitives.scheme.BrowserContextSetGeolocationResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserContextSetHTTPCredentialsParams = (0, _validatorPrimitives.tObject)({
  httpCredentials: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    username: _validatorPrimitives.tString,
    password: _validatorPrimitives.tString,
    origin: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
  }))
});
_validatorPrimitives.scheme.BrowserContextSetHTTPCredentialsResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserContextSetNetworkInterceptionPatternsParams = (0, _validatorPrimitives.tObject)({
  patterns: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tObject)({
    glob: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    regexSource: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    regexFlags: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
  }))
});
_validatorPrimitives.scheme.BrowserContextSetNetworkInterceptionPatternsResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserContextSetOfflineParams = (0, _validatorPrimitives.tObject)({
  offline: _validatorPrimitives.tBoolean
});
_validatorPrimitives.scheme.BrowserContextSetOfflineResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserContextStorageStateParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserContextStorageStateResult = (0, _validatorPrimitives.tObject)({
  cookies: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NetworkCookie')),
  origins: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('OriginStorage'))
});
_validatorPrimitives.scheme.BrowserContextPauseParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserContextPauseResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserContextRecorderSupplementEnableParams = (0, _validatorPrimitives.tObject)({
  language: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  mode: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['inspecting', 'recording'])),
  pauseOnNextStatement: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  testIdAttributeName: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  launchOptions: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tAny),
  contextOptions: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tAny),
  device: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  saveStorage: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  outputFile: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  handleSIGINT: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  omitCallTracking: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.BrowserContextRecorderSupplementEnableResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BrowserContextNewCDPSessionParams = (0, _validatorPrimitives.tObject)({
  page: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['Page'])),
  frame: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['Frame']))
});
_validatorPrimitives.scheme.BrowserContextNewCDPSessionResult = (0, _validatorPrimitives.tObject)({
  session: (0, _validatorPrimitives.tChannel)(['CDPSession'])
});
_validatorPrimitives.scheme.BrowserContextHarStartParams = (0, _validatorPrimitives.tObject)({
  page: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['Page'])),
  options: (0, _validatorPrimitives.tType)('RecordHarOptions')
});
_validatorPrimitives.scheme.BrowserContextHarStartResult = (0, _validatorPrimitives.tObject)({
  harId: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.BrowserContextHarExportParams = (0, _validatorPrimitives.tObject)({
  harId: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.BrowserContextHarExportResult = (0, _validatorPrimitives.tObject)({
  artifact: (0, _validatorPrimitives.tChannel)(['Artifact'])
});
_validatorPrimitives.scheme.BrowserContextCreateTempFileParams = (0, _validatorPrimitives.tObject)({
  name: _validatorPrimitives.tString,
  lastModifiedMs: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.BrowserContextCreateTempFileResult = (0, _validatorPrimitives.tObject)({
  writableStream: (0, _validatorPrimitives.tChannel)(['WritableStream'])
});
_validatorPrimitives.scheme.BrowserContextUpdateSubscriptionParams = (0, _validatorPrimitives.tObject)({
  event: (0, _validatorPrimitives.tEnum)(['console', 'dialog', 'request', 'response', 'requestFinished', 'requestFailed']),
  enabled: _validatorPrimitives.tBoolean
});
_validatorPrimitives.scheme.BrowserContextUpdateSubscriptionResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageInitializer = (0, _validatorPrimitives.tObject)({
  mainFrame: (0, _validatorPrimitives.tChannel)(['Frame']),
  viewportSize: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    width: _validatorPrimitives.tNumber,
    height: _validatorPrimitives.tNumber
  })),
  isClosed: _validatorPrimitives.tBoolean,
  opener: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['Page']))
});
_validatorPrimitives.scheme.PageBindingCallEvent = (0, _validatorPrimitives.tObject)({
  binding: (0, _validatorPrimitives.tChannel)(['BindingCall'])
});
_validatorPrimitives.scheme.PageCloseEvent = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageCrashEvent = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageDownloadEvent = (0, _validatorPrimitives.tObject)({
  url: _validatorPrimitives.tString,
  suggestedFilename: _validatorPrimitives.tString,
  artifact: (0, _validatorPrimitives.tChannel)(['Artifact'])
});
_validatorPrimitives.scheme.PageFileChooserEvent = (0, _validatorPrimitives.tObject)({
  element: (0, _validatorPrimitives.tChannel)(['ElementHandle']),
  isMultiple: _validatorPrimitives.tBoolean
});
_validatorPrimitives.scheme.PageFrameAttachedEvent = (0, _validatorPrimitives.tObject)({
  frame: (0, _validatorPrimitives.tChannel)(['Frame'])
});
_validatorPrimitives.scheme.PageFrameDetachedEvent = (0, _validatorPrimitives.tObject)({
  frame: (0, _validatorPrimitives.tChannel)(['Frame'])
});
_validatorPrimitives.scheme.PageLocatorHandlerTriggeredEvent = (0, _validatorPrimitives.tObject)({
  uid: _validatorPrimitives.tNumber
});
_validatorPrimitives.scheme.PageRouteEvent = (0, _validatorPrimitives.tObject)({
  route: (0, _validatorPrimitives.tChannel)(['Route'])
});
_validatorPrimitives.scheme.PageVideoEvent = (0, _validatorPrimitives.tObject)({
  artifact: (0, _validatorPrimitives.tChannel)(['Artifact'])
});
_validatorPrimitives.scheme.PageWebSocketEvent = (0, _validatorPrimitives.tObject)({
  webSocket: (0, _validatorPrimitives.tChannel)(['WebSocket'])
});
_validatorPrimitives.scheme.PageWorkerEvent = (0, _validatorPrimitives.tObject)({
  worker: (0, _validatorPrimitives.tChannel)(['Worker'])
});
_validatorPrimitives.scheme.PageSetDefaultNavigationTimeoutNoReplyParams = (0, _validatorPrimitives.tObject)({
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.PageSetDefaultNavigationTimeoutNoReplyResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageSetDefaultTimeoutNoReplyParams = (0, _validatorPrimitives.tObject)({
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.PageSetDefaultTimeoutNoReplyResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageAddInitScriptParams = (0, _validatorPrimitives.tObject)({
  source: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.PageAddInitScriptResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageCloseParams = (0, _validatorPrimitives.tObject)({
  runBeforeUnload: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  reason: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.PageCloseResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageEmulateMediaParams = (0, _validatorPrimitives.tObject)({
  media: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['screen', 'print', 'no-override'])),
  colorScheme: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['dark', 'light', 'no-preference', 'no-override'])),
  reducedMotion: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['reduce', 'no-preference', 'no-override'])),
  forcedColors: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['active', 'none', 'no-override']))
});
_validatorPrimitives.scheme.PageEmulateMediaResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageExposeBindingParams = (0, _validatorPrimitives.tObject)({
  name: _validatorPrimitives.tString,
  needsHandle: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.PageExposeBindingResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageGoBackParams = (0, _validatorPrimitives.tObject)({
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  waitUntil: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('LifecycleEvent'))
});
_validatorPrimitives.scheme.PageGoBackResult = (0, _validatorPrimitives.tObject)({
  response: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['Response']))
});
_validatorPrimitives.scheme.PageGoForwardParams = (0, _validatorPrimitives.tObject)({
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  waitUntil: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('LifecycleEvent'))
});
_validatorPrimitives.scheme.PageGoForwardResult = (0, _validatorPrimitives.tObject)({
  response: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['Response']))
});
_validatorPrimitives.scheme.PageRegisterLocatorHandlerParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.PageRegisterLocatorHandlerResult = (0, _validatorPrimitives.tObject)({
  uid: _validatorPrimitives.tNumber
});
_validatorPrimitives.scheme.PageResolveLocatorHandlerNoReplyParams = (0, _validatorPrimitives.tObject)({
  uid: _validatorPrimitives.tNumber
});
_validatorPrimitives.scheme.PageResolveLocatorHandlerNoReplyResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageReloadParams = (0, _validatorPrimitives.tObject)({
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  waitUntil: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('LifecycleEvent'))
});
_validatorPrimitives.scheme.PageReloadResult = (0, _validatorPrimitives.tObject)({
  response: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['Response']))
});
_validatorPrimitives.scheme.PageExpectScreenshotParams = (0, _validatorPrimitives.tObject)({
  expected: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBinary),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  isNot: _validatorPrimitives.tBoolean,
  locator: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    frame: (0, _validatorPrimitives.tChannel)(['Frame']),
    selector: _validatorPrimitives.tString
  })),
  comparator: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  maxDiffPixels: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  maxDiffPixelRatio: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  threshold: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  fullPage: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  clip: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('Rect')),
  omitBackground: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  caret: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['hide', 'initial'])),
  animations: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['disabled', 'allow'])),
  scale: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['css', 'device'])),
  mask: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tObject)({
    frame: (0, _validatorPrimitives.tChannel)(['Frame']),
    selector: _validatorPrimitives.tString
  }))),
  maskColor: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  style: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.PageExpectScreenshotResult = (0, _validatorPrimitives.tObject)({
  diff: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBinary),
  errorMessage: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  actual: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBinary),
  previous: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBinary),
  log: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)(_validatorPrimitives.tString))
});
_validatorPrimitives.scheme.PageScreenshotParams = (0, _validatorPrimitives.tObject)({
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  type: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['png', 'jpeg'])),
  quality: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  fullPage: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  clip: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('Rect')),
  omitBackground: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  caret: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['hide', 'initial'])),
  animations: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['disabled', 'allow'])),
  scale: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['css', 'device'])),
  mask: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tObject)({
    frame: (0, _validatorPrimitives.tChannel)(['Frame']),
    selector: _validatorPrimitives.tString
  }))),
  maskColor: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  style: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.PageScreenshotResult = (0, _validatorPrimitives.tObject)({
  binary: _validatorPrimitives.tBinary
});
_validatorPrimitives.scheme.PageSetExtraHTTPHeadersParams = (0, _validatorPrimitives.tObject)({
  headers: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue'))
});
_validatorPrimitives.scheme.PageSetExtraHTTPHeadersResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageSetNetworkInterceptionPatternsParams = (0, _validatorPrimitives.tObject)({
  patterns: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tObject)({
    glob: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    regexSource: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    regexFlags: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
  }))
});
_validatorPrimitives.scheme.PageSetNetworkInterceptionPatternsResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageSetViewportSizeParams = (0, _validatorPrimitives.tObject)({
  viewportSize: (0, _validatorPrimitives.tObject)({
    width: _validatorPrimitives.tNumber,
    height: _validatorPrimitives.tNumber
  })
});
_validatorPrimitives.scheme.PageSetViewportSizeResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageKeyboardDownParams = (0, _validatorPrimitives.tObject)({
  key: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.PageKeyboardDownResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageKeyboardUpParams = (0, _validatorPrimitives.tObject)({
  key: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.PageKeyboardUpResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageKeyboardInsertTextParams = (0, _validatorPrimitives.tObject)({
  text: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.PageKeyboardInsertTextResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageKeyboardTypeParams = (0, _validatorPrimitives.tObject)({
  text: _validatorPrimitives.tString,
  delay: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.PageKeyboardTypeResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageKeyboardPressParams = (0, _validatorPrimitives.tObject)({
  key: _validatorPrimitives.tString,
  delay: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.PageKeyboardPressResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageMouseMoveParams = (0, _validatorPrimitives.tObject)({
  x: _validatorPrimitives.tNumber,
  y: _validatorPrimitives.tNumber,
  steps: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.PageMouseMoveResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageMouseDownParams = (0, _validatorPrimitives.tObject)({
  button: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['left', 'right', 'middle'])),
  clickCount: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.PageMouseDownResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageMouseUpParams = (0, _validatorPrimitives.tObject)({
  button: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['left', 'right', 'middle'])),
  clickCount: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.PageMouseUpResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageMouseClickParams = (0, _validatorPrimitives.tObject)({
  x: _validatorPrimitives.tNumber,
  y: _validatorPrimitives.tNumber,
  delay: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  button: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['left', 'right', 'middle'])),
  clickCount: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.PageMouseClickResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageMouseWheelParams = (0, _validatorPrimitives.tObject)({
  deltaX: _validatorPrimitives.tNumber,
  deltaY: _validatorPrimitives.tNumber
});
_validatorPrimitives.scheme.PageMouseWheelResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageTouchscreenTapParams = (0, _validatorPrimitives.tObject)({
  x: _validatorPrimitives.tNumber,
  y: _validatorPrimitives.tNumber
});
_validatorPrimitives.scheme.PageTouchscreenTapResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageAccessibilitySnapshotParams = (0, _validatorPrimitives.tObject)({
  interestingOnly: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  root: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['ElementHandle']))
});
_validatorPrimitives.scheme.PageAccessibilitySnapshotResult = (0, _validatorPrimitives.tObject)({
  rootAXNode: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('AXNode'))
});
_validatorPrimitives.scheme.PagePdfParams = (0, _validatorPrimitives.tObject)({
  scale: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  displayHeaderFooter: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  headerTemplate: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  footerTemplate: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  printBackground: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  landscape: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  pageRanges: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  format: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  width: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  height: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  preferCSSPageSize: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  margin: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    top: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    bottom: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    left: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    right: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
  })),
  tagged: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  outline: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.PagePdfResult = (0, _validatorPrimitives.tObject)({
  pdf: _validatorPrimitives.tBinary
});
_validatorPrimitives.scheme.PageStartJSCoverageParams = (0, _validatorPrimitives.tObject)({
  resetOnNavigation: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  reportAnonymousScripts: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.PageStartJSCoverageResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageStopJSCoverageParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageStopJSCoverageResult = (0, _validatorPrimitives.tObject)({
  entries: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tObject)({
    url: _validatorPrimitives.tString,
    scriptId: _validatorPrimitives.tString,
    source: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    functions: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tObject)({
      functionName: _validatorPrimitives.tString,
      isBlockCoverage: _validatorPrimitives.tBoolean,
      ranges: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tObject)({
        startOffset: _validatorPrimitives.tNumber,
        endOffset: _validatorPrimitives.tNumber,
        count: _validatorPrimitives.tNumber
      }))
    }))
  }))
});
_validatorPrimitives.scheme.PageStartCSSCoverageParams = (0, _validatorPrimitives.tObject)({
  resetOnNavigation: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.PageStartCSSCoverageResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageStopCSSCoverageParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageStopCSSCoverageResult = (0, _validatorPrimitives.tObject)({
  entries: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tObject)({
    url: _validatorPrimitives.tString,
    text: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    ranges: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tObject)({
      start: _validatorPrimitives.tNumber,
      end: _validatorPrimitives.tNumber
    }))
  }))
});
_validatorPrimitives.scheme.PageBringToFrontParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageBringToFrontResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.PageUpdateSubscriptionParams = (0, _validatorPrimitives.tObject)({
  event: (0, _validatorPrimitives.tEnum)(['console', 'dialog', 'fileChooser', 'request', 'response', 'requestFinished', 'requestFailed']),
  enabled: _validatorPrimitives.tBoolean
});
_validatorPrimitives.scheme.PageUpdateSubscriptionResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.FrameInitializer = (0, _validatorPrimitives.tObject)({
  url: _validatorPrimitives.tString,
  name: _validatorPrimitives.tString,
  parentFrame: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['Frame'])),
  loadStates: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('LifecycleEvent'))
});
_validatorPrimitives.scheme.FrameLoadstateEvent = (0, _validatorPrimitives.tObject)({
  add: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('LifecycleEvent')),
  remove: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('LifecycleEvent'))
});
_validatorPrimitives.scheme.FrameNavigatedEvent = (0, _validatorPrimitives.tObject)({
  url: _validatorPrimitives.tString,
  name: _validatorPrimitives.tString,
  newDocument: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    request: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['Request']))
  })),
  error: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.FrameEvalOnSelectorParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  expression: _validatorPrimitives.tString,
  isFunction: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  arg: (0, _validatorPrimitives.tType)('SerializedArgument')
});
_validatorPrimitives.scheme.FrameEvalOnSelectorResult = (0, _validatorPrimitives.tObject)({
  value: (0, _validatorPrimitives.tType)('SerializedValue')
});
_validatorPrimitives.scheme.FrameEvalOnSelectorAllParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  expression: _validatorPrimitives.tString,
  isFunction: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  arg: (0, _validatorPrimitives.tType)('SerializedArgument')
});
_validatorPrimitives.scheme.FrameEvalOnSelectorAllResult = (0, _validatorPrimitives.tObject)({
  value: (0, _validatorPrimitives.tType)('SerializedValue')
});
_validatorPrimitives.scheme.FrameAddScriptTagParams = (0, _validatorPrimitives.tObject)({
  url: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  content: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  type: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.FrameAddScriptTagResult = (0, _validatorPrimitives.tObject)({
  element: (0, _validatorPrimitives.tChannel)(['ElementHandle'])
});
_validatorPrimitives.scheme.FrameAddStyleTagParams = (0, _validatorPrimitives.tObject)({
  url: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  content: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.FrameAddStyleTagResult = (0, _validatorPrimitives.tObject)({
  element: (0, _validatorPrimitives.tChannel)(['ElementHandle'])
});
_validatorPrimitives.scheme.FrameBlurParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.FrameBlurResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.FrameCheckParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  force: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  noWaitAfter: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  position: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('Point')),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  trial: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.FrameCheckResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.FrameClickParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  force: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  noWaitAfter: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  modifiers: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tEnum)(['Alt', 'Control', 'Meta', 'Shift']))),
  position: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('Point')),
  delay: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  button: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['left', 'right', 'middle'])),
  clickCount: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  trial: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.FrameClickResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.FrameContentParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.FrameContentResult = (0, _validatorPrimitives.tObject)({
  value: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.FrameDragAndDropParams = (0, _validatorPrimitives.tObject)({
  source: _validatorPrimitives.tString,
  target: _validatorPrimitives.tString,
  force: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  noWaitAfter: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  trial: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  sourcePosition: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('Point')),
  targetPosition: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('Point')),
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.FrameDragAndDropResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.FrameDblclickParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  force: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  noWaitAfter: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  modifiers: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tEnum)(['Alt', 'Control', 'Meta', 'Shift']))),
  position: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('Point')),
  delay: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  button: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['left', 'right', 'middle'])),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  trial: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.FrameDblclickResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.FrameDispatchEventParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  type: _validatorPrimitives.tString,
  eventInit: (0, _validatorPrimitives.tType)('SerializedArgument'),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.FrameDispatchEventResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.FrameEvaluateExpressionParams = (0, _validatorPrimitives.tObject)({
  expression: _validatorPrimitives.tString,
  isFunction: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  exposeUtilityScript: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  arg: (0, _validatorPrimitives.tType)('SerializedArgument')
});
_validatorPrimitives.scheme.FrameEvaluateExpressionResult = (0, _validatorPrimitives.tObject)({
  value: (0, _validatorPrimitives.tType)('SerializedValue')
});
_validatorPrimitives.scheme.FrameEvaluateExpressionHandleParams = (0, _validatorPrimitives.tObject)({
  expression: _validatorPrimitives.tString,
  isFunction: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  arg: (0, _validatorPrimitives.tType)('SerializedArgument')
});
_validatorPrimitives.scheme.FrameEvaluateExpressionHandleResult = (0, _validatorPrimitives.tObject)({
  handle: (0, _validatorPrimitives.tChannel)(['ElementHandle', 'JSHandle'])
});
_validatorPrimitives.scheme.FrameFillParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  value: _validatorPrimitives.tString,
  force: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  noWaitAfter: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.FrameFillResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.FrameFocusParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.FrameFocusResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.FrameFrameElementParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.FrameFrameElementResult = (0, _validatorPrimitives.tObject)({
  element: (0, _validatorPrimitives.tChannel)(['ElementHandle'])
});
_validatorPrimitives.scheme.FrameHighlightParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.FrameHighlightResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.FrameGetAttributeParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  name: _validatorPrimitives.tString,
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.FrameGetAttributeResult = (0, _validatorPrimitives.tObject)({
  value: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.FrameGotoParams = (0, _validatorPrimitives.tObject)({
  url: _validatorPrimitives.tString,
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  waitUntil: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('LifecycleEvent')),
  referer: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.FrameGotoResult = (0, _validatorPrimitives.tObject)({
  response: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['Response']))
});
_validatorPrimitives.scheme.FrameHoverParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  force: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  modifiers: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tEnum)(['Alt', 'Control', 'Meta', 'Shift']))),
  position: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('Point')),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  trial: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  noWaitAfter: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.FrameHoverResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.FrameInnerHTMLParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.FrameInnerHTMLResult = (0, _validatorPrimitives.tObject)({
  value: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.FrameInnerTextParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.FrameInnerTextResult = (0, _validatorPrimitives.tObject)({
  value: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.FrameInputValueParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.FrameInputValueResult = (0, _validatorPrimitives.tObject)({
  value: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.FrameIsCheckedParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.FrameIsCheckedResult = (0, _validatorPrimitives.tObject)({
  value: _validatorPrimitives.tBoolean
});
_validatorPrimitives.scheme.FrameIsDisabledParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.FrameIsDisabledResult = (0, _validatorPrimitives.tObject)({
  value: _validatorPrimitives.tBoolean
});
_validatorPrimitives.scheme.FrameIsEnabledParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.FrameIsEnabledResult = (0, _validatorPrimitives.tObject)({
  value: _validatorPrimitives.tBoolean
});
_validatorPrimitives.scheme.FrameIsHiddenParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.FrameIsHiddenResult = (0, _validatorPrimitives.tObject)({
  value: _validatorPrimitives.tBoolean
});
_validatorPrimitives.scheme.FrameIsVisibleParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.FrameIsVisibleResult = (0, _validatorPrimitives.tObject)({
  value: _validatorPrimitives.tBoolean
});
_validatorPrimitives.scheme.FrameIsEditableParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.FrameIsEditableResult = (0, _validatorPrimitives.tObject)({
  value: _validatorPrimitives.tBoolean
});
_validatorPrimitives.scheme.FramePressParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  key: _validatorPrimitives.tString,
  delay: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  noWaitAfter: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.FramePressResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.FrameQuerySelectorParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.FrameQuerySelectorResult = (0, _validatorPrimitives.tObject)({
  element: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['ElementHandle']))
});
_validatorPrimitives.scheme.FrameQuerySelectorAllParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.FrameQuerySelectorAllResult = (0, _validatorPrimitives.tObject)({
  elements: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tChannel)(['ElementHandle']))
});
_validatorPrimitives.scheme.FrameQueryCountParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.FrameQueryCountResult = (0, _validatorPrimitives.tObject)({
  value: _validatorPrimitives.tNumber
});
_validatorPrimitives.scheme.FrameSelectOptionParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  elements: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tChannel)(['ElementHandle']))),
  options: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tObject)({
    valueOrLabel: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    value: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    label: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    index: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
  }))),
  force: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  noWaitAfter: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.FrameSelectOptionResult = (0, _validatorPrimitives.tObject)({
  values: (0, _validatorPrimitives.tArray)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.FrameSetContentParams = (0, _validatorPrimitives.tObject)({
  html: _validatorPrimitives.tString,
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  waitUntil: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('LifecycleEvent'))
});
_validatorPrimitives.scheme.FrameSetContentResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.FrameSetInputFilesParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  payloads: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tObject)({
    name: _validatorPrimitives.tString,
    mimeType: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    buffer: _validatorPrimitives.tBinary
  }))),
  localPaths: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)(_validatorPrimitives.tString)),
  streams: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tChannel)(['WritableStream']))),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  noWaitAfter: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.FrameSetInputFilesResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.FrameTapParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  force: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  noWaitAfter: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  modifiers: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tEnum)(['Alt', 'Control', 'Meta', 'Shift']))),
  position: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('Point')),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  trial: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.FrameTapResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.FrameTextContentParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.FrameTextContentResult = (0, _validatorPrimitives.tObject)({
  value: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.FrameTitleParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.FrameTitleResult = (0, _validatorPrimitives.tObject)({
  value: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.FrameTypeParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  text: _validatorPrimitives.tString,
  delay: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  noWaitAfter: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.FrameTypeResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.FrameUncheckParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  force: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  noWaitAfter: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  position: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('Point')),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  trial: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.FrameUncheckResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.FrameWaitForTimeoutParams = (0, _validatorPrimitives.tObject)({
  timeout: _validatorPrimitives.tNumber
});
_validatorPrimitives.scheme.FrameWaitForTimeoutResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.FrameWaitForFunctionParams = (0, _validatorPrimitives.tObject)({
  expression: _validatorPrimitives.tString,
  isFunction: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  arg: (0, _validatorPrimitives.tType)('SerializedArgument'),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  pollingInterval: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.FrameWaitForFunctionResult = (0, _validatorPrimitives.tObject)({
  handle: (0, _validatorPrimitives.tChannel)(['ElementHandle', 'JSHandle'])
});
_validatorPrimitives.scheme.FrameWaitForSelectorParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  state: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['attached', 'detached', 'visible', 'hidden'])),
  omitReturnValue: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.FrameWaitForSelectorResult = (0, _validatorPrimitives.tObject)({
  element: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['ElementHandle']))
});
_validatorPrimitives.scheme.FrameExpectParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  expression: _validatorPrimitives.tString,
  expressionArg: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tAny),
  expectedText: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('ExpectedTextValue'))),
  expectedNumber: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  expectedValue: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('SerializedArgument')),
  useInnerText: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  isNot: _validatorPrimitives.tBoolean,
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.FrameExpectResult = (0, _validatorPrimitives.tObject)({
  matches: _validatorPrimitives.tBoolean,
  received: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('SerializedValue')),
  timedOut: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  log: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)(_validatorPrimitives.tString))
});
_validatorPrimitives.scheme.WorkerInitializer = (0, _validatorPrimitives.tObject)({
  url: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.WorkerCloseEvent = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.WorkerEvaluateExpressionParams = (0, _validatorPrimitives.tObject)({
  expression: _validatorPrimitives.tString,
  isFunction: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  arg: (0, _validatorPrimitives.tType)('SerializedArgument')
});
_validatorPrimitives.scheme.WorkerEvaluateExpressionResult = (0, _validatorPrimitives.tObject)({
  value: (0, _validatorPrimitives.tType)('SerializedValue')
});
_validatorPrimitives.scheme.WorkerEvaluateExpressionHandleParams = (0, _validatorPrimitives.tObject)({
  expression: _validatorPrimitives.tString,
  isFunction: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  arg: (0, _validatorPrimitives.tType)('SerializedArgument')
});
_validatorPrimitives.scheme.WorkerEvaluateExpressionHandleResult = (0, _validatorPrimitives.tObject)({
  handle: (0, _validatorPrimitives.tChannel)(['ElementHandle', 'JSHandle'])
});
_validatorPrimitives.scheme.JSHandleInitializer = (0, _validatorPrimitives.tObject)({
  preview: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.JSHandlePreviewUpdatedEvent = (0, _validatorPrimitives.tObject)({
  preview: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.ElementHandlePreviewUpdatedEvent = (0, _validatorPrimitives.tType)('JSHandlePreviewUpdatedEvent');
_validatorPrimitives.scheme.JSHandleDisposeParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleDisposeParams = (0, _validatorPrimitives.tType)('JSHandleDisposeParams');
_validatorPrimitives.scheme.JSHandleDisposeResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleDisposeResult = (0, _validatorPrimitives.tType)('JSHandleDisposeResult');
_validatorPrimitives.scheme.JSHandleEvaluateExpressionParams = (0, _validatorPrimitives.tObject)({
  expression: _validatorPrimitives.tString,
  isFunction: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  arg: (0, _validatorPrimitives.tType)('SerializedArgument')
});
_validatorPrimitives.scheme.ElementHandleEvaluateExpressionParams = (0, _validatorPrimitives.tType)('JSHandleEvaluateExpressionParams');
_validatorPrimitives.scheme.JSHandleEvaluateExpressionResult = (0, _validatorPrimitives.tObject)({
  value: (0, _validatorPrimitives.tType)('SerializedValue')
});
_validatorPrimitives.scheme.ElementHandleEvaluateExpressionResult = (0, _validatorPrimitives.tType)('JSHandleEvaluateExpressionResult');
_validatorPrimitives.scheme.JSHandleEvaluateExpressionHandleParams = (0, _validatorPrimitives.tObject)({
  expression: _validatorPrimitives.tString,
  isFunction: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  arg: (0, _validatorPrimitives.tType)('SerializedArgument')
});
_validatorPrimitives.scheme.ElementHandleEvaluateExpressionHandleParams = (0, _validatorPrimitives.tType)('JSHandleEvaluateExpressionHandleParams');
_validatorPrimitives.scheme.JSHandleEvaluateExpressionHandleResult = (0, _validatorPrimitives.tObject)({
  handle: (0, _validatorPrimitives.tChannel)(['ElementHandle', 'JSHandle'])
});
_validatorPrimitives.scheme.ElementHandleEvaluateExpressionHandleResult = (0, _validatorPrimitives.tType)('JSHandleEvaluateExpressionHandleResult');
_validatorPrimitives.scheme.JSHandleGetPropertyListParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleGetPropertyListParams = (0, _validatorPrimitives.tType)('JSHandleGetPropertyListParams');
_validatorPrimitives.scheme.JSHandleGetPropertyListResult = (0, _validatorPrimitives.tObject)({
  properties: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tObject)({
    name: _validatorPrimitives.tString,
    value: (0, _validatorPrimitives.tChannel)(['ElementHandle', 'JSHandle'])
  }))
});
_validatorPrimitives.scheme.ElementHandleGetPropertyListResult = (0, _validatorPrimitives.tType)('JSHandleGetPropertyListResult');
_validatorPrimitives.scheme.JSHandleGetPropertyParams = (0, _validatorPrimitives.tObject)({
  name: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.ElementHandleGetPropertyParams = (0, _validatorPrimitives.tType)('JSHandleGetPropertyParams');
_validatorPrimitives.scheme.JSHandleGetPropertyResult = (0, _validatorPrimitives.tObject)({
  handle: (0, _validatorPrimitives.tChannel)(['ElementHandle', 'JSHandle'])
});
_validatorPrimitives.scheme.ElementHandleGetPropertyResult = (0, _validatorPrimitives.tType)('JSHandleGetPropertyResult');
_validatorPrimitives.scheme.JSHandleJsonValueParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleJsonValueParams = (0, _validatorPrimitives.tType)('JSHandleJsonValueParams');
_validatorPrimitives.scheme.JSHandleJsonValueResult = (0, _validatorPrimitives.tObject)({
  value: (0, _validatorPrimitives.tType)('SerializedValue')
});
_validatorPrimitives.scheme.ElementHandleJsonValueResult = (0, _validatorPrimitives.tType)('JSHandleJsonValueResult');
_validatorPrimitives.scheme.JSHandleObjectCountParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleObjectCountParams = (0, _validatorPrimitives.tType)('JSHandleObjectCountParams');
_validatorPrimitives.scheme.JSHandleObjectCountResult = (0, _validatorPrimitives.tObject)({
  count: _validatorPrimitives.tNumber
});
_validatorPrimitives.scheme.ElementHandleObjectCountResult = (0, _validatorPrimitives.tType)('JSHandleObjectCountResult');
_validatorPrimitives.scheme.ElementHandleInitializer = (0, _validatorPrimitives.tObject)({
  preview: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.ElementHandleEvalOnSelectorParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  expression: _validatorPrimitives.tString,
  isFunction: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  arg: (0, _validatorPrimitives.tType)('SerializedArgument')
});
_validatorPrimitives.scheme.ElementHandleEvalOnSelectorResult = (0, _validatorPrimitives.tObject)({
  value: (0, _validatorPrimitives.tType)('SerializedValue')
});
_validatorPrimitives.scheme.ElementHandleEvalOnSelectorAllParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  expression: _validatorPrimitives.tString,
  isFunction: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  arg: (0, _validatorPrimitives.tType)('SerializedArgument')
});
_validatorPrimitives.scheme.ElementHandleEvalOnSelectorAllResult = (0, _validatorPrimitives.tObject)({
  value: (0, _validatorPrimitives.tType)('SerializedValue')
});
_validatorPrimitives.scheme.ElementHandleBoundingBoxParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleBoundingBoxResult = (0, _validatorPrimitives.tObject)({
  value: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('Rect'))
});
_validatorPrimitives.scheme.ElementHandleCheckParams = (0, _validatorPrimitives.tObject)({
  force: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  noWaitAfter: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  position: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('Point')),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  trial: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.ElementHandleCheckResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleClickParams = (0, _validatorPrimitives.tObject)({
  force: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  noWaitAfter: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  modifiers: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tEnum)(['Alt', 'Control', 'Meta', 'Shift']))),
  position: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('Point')),
  delay: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  button: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['left', 'right', 'middle'])),
  clickCount: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  trial: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.ElementHandleClickResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleContentFrameParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleContentFrameResult = (0, _validatorPrimitives.tObject)({
  frame: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['Frame']))
});
_validatorPrimitives.scheme.ElementHandleDblclickParams = (0, _validatorPrimitives.tObject)({
  force: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  noWaitAfter: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  modifiers: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tEnum)(['Alt', 'Control', 'Meta', 'Shift']))),
  position: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('Point')),
  delay: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  button: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['left', 'right', 'middle'])),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  trial: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.ElementHandleDblclickResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleDispatchEventParams = (0, _validatorPrimitives.tObject)({
  type: _validatorPrimitives.tString,
  eventInit: (0, _validatorPrimitives.tType)('SerializedArgument')
});
_validatorPrimitives.scheme.ElementHandleDispatchEventResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleFillParams = (0, _validatorPrimitives.tObject)({
  value: _validatorPrimitives.tString,
  force: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  noWaitAfter: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.ElementHandleFillResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleFocusParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleFocusResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleGetAttributeParams = (0, _validatorPrimitives.tObject)({
  name: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.ElementHandleGetAttributeResult = (0, _validatorPrimitives.tObject)({
  value: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.ElementHandleHoverParams = (0, _validatorPrimitives.tObject)({
  force: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  modifiers: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tEnum)(['Alt', 'Control', 'Meta', 'Shift']))),
  position: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('Point')),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  trial: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  noWaitAfter: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.ElementHandleHoverResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleInnerHTMLParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleInnerHTMLResult = (0, _validatorPrimitives.tObject)({
  value: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.ElementHandleInnerTextParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleInnerTextResult = (0, _validatorPrimitives.tObject)({
  value: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.ElementHandleInputValueParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleInputValueResult = (0, _validatorPrimitives.tObject)({
  value: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.ElementHandleIsCheckedParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleIsCheckedResult = (0, _validatorPrimitives.tObject)({
  value: _validatorPrimitives.tBoolean
});
_validatorPrimitives.scheme.ElementHandleIsDisabledParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleIsDisabledResult = (0, _validatorPrimitives.tObject)({
  value: _validatorPrimitives.tBoolean
});
_validatorPrimitives.scheme.ElementHandleIsEditableParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleIsEditableResult = (0, _validatorPrimitives.tObject)({
  value: _validatorPrimitives.tBoolean
});
_validatorPrimitives.scheme.ElementHandleIsEnabledParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleIsEnabledResult = (0, _validatorPrimitives.tObject)({
  value: _validatorPrimitives.tBoolean
});
_validatorPrimitives.scheme.ElementHandleIsHiddenParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleIsHiddenResult = (0, _validatorPrimitives.tObject)({
  value: _validatorPrimitives.tBoolean
});
_validatorPrimitives.scheme.ElementHandleIsVisibleParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleIsVisibleResult = (0, _validatorPrimitives.tObject)({
  value: _validatorPrimitives.tBoolean
});
_validatorPrimitives.scheme.ElementHandleOwnerFrameParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleOwnerFrameResult = (0, _validatorPrimitives.tObject)({
  frame: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['Frame']))
});
_validatorPrimitives.scheme.ElementHandlePressParams = (0, _validatorPrimitives.tObject)({
  key: _validatorPrimitives.tString,
  delay: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  noWaitAfter: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.ElementHandlePressResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleQuerySelectorParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.ElementHandleQuerySelectorResult = (0, _validatorPrimitives.tObject)({
  element: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['ElementHandle']))
});
_validatorPrimitives.scheme.ElementHandleQuerySelectorAllParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.ElementHandleQuerySelectorAllResult = (0, _validatorPrimitives.tObject)({
  elements: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tChannel)(['ElementHandle']))
});
_validatorPrimitives.scheme.ElementHandleScreenshotParams = (0, _validatorPrimitives.tObject)({
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  type: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['png', 'jpeg'])),
  quality: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  omitBackground: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  caret: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['hide', 'initial'])),
  animations: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['disabled', 'allow'])),
  scale: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['css', 'device'])),
  mask: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tObject)({
    frame: (0, _validatorPrimitives.tChannel)(['Frame']),
    selector: _validatorPrimitives.tString
  }))),
  maskColor: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  style: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.ElementHandleScreenshotResult = (0, _validatorPrimitives.tObject)({
  binary: _validatorPrimitives.tBinary
});
_validatorPrimitives.scheme.ElementHandleScrollIntoViewIfNeededParams = (0, _validatorPrimitives.tObject)({
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.ElementHandleScrollIntoViewIfNeededResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleSelectOptionParams = (0, _validatorPrimitives.tObject)({
  elements: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tChannel)(['ElementHandle']))),
  options: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tObject)({
    valueOrLabel: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    value: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    label: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    index: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
  }))),
  force: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  noWaitAfter: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.ElementHandleSelectOptionResult = (0, _validatorPrimitives.tObject)({
  values: (0, _validatorPrimitives.tArray)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.ElementHandleSelectTextParams = (0, _validatorPrimitives.tObject)({
  force: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.ElementHandleSelectTextResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleSetInputFilesParams = (0, _validatorPrimitives.tObject)({
  payloads: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tObject)({
    name: _validatorPrimitives.tString,
    mimeType: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    buffer: _validatorPrimitives.tBinary
  }))),
  localPaths: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)(_validatorPrimitives.tString)),
  streams: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tChannel)(['WritableStream']))),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  noWaitAfter: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.ElementHandleSetInputFilesResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleTapParams = (0, _validatorPrimitives.tObject)({
  force: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  noWaitAfter: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  modifiers: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tEnum)(['Alt', 'Control', 'Meta', 'Shift']))),
  position: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('Point')),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  trial: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.ElementHandleTapResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleTextContentParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleTextContentResult = (0, _validatorPrimitives.tObject)({
  value: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.ElementHandleTypeParams = (0, _validatorPrimitives.tObject)({
  text: _validatorPrimitives.tString,
  delay: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  noWaitAfter: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.ElementHandleTypeResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleUncheckParams = (0, _validatorPrimitives.tObject)({
  force: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  noWaitAfter: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  position: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('Point')),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  trial: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.ElementHandleUncheckResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleWaitForElementStateParams = (0, _validatorPrimitives.tObject)({
  state: (0, _validatorPrimitives.tEnum)(['visible', 'hidden', 'stable', 'enabled', 'disabled', 'editable']),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.ElementHandleWaitForElementStateResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElementHandleWaitForSelectorParams = (0, _validatorPrimitives.tObject)({
  selector: _validatorPrimitives.tString,
  strict: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  state: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['attached', 'detached', 'visible', 'hidden']))
});
_validatorPrimitives.scheme.ElementHandleWaitForSelectorResult = (0, _validatorPrimitives.tObject)({
  element: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['ElementHandle']))
});
_validatorPrimitives.scheme.RequestInitializer = (0, _validatorPrimitives.tObject)({
  frame: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['Frame'])),
  serviceWorker: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['Worker'])),
  url: _validatorPrimitives.tString,
  resourceType: _validatorPrimitives.tString,
  method: _validatorPrimitives.tString,
  postData: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBinary),
  headers: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue')),
  isNavigationRequest: _validatorPrimitives.tBoolean,
  redirectedFrom: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['Request']))
});
_validatorPrimitives.scheme.RequestResponseParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.RequestResponseResult = (0, _validatorPrimitives.tObject)({
  response: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['Response']))
});
_validatorPrimitives.scheme.RequestRawRequestHeadersParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.RequestRawRequestHeadersResult = (0, _validatorPrimitives.tObject)({
  headers: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue'))
});
_validatorPrimitives.scheme.RouteInitializer = (0, _validatorPrimitives.tObject)({
  request: (0, _validatorPrimitives.tChannel)(['Request'])
});
_validatorPrimitives.scheme.RouteRedirectNavigationRequestParams = (0, _validatorPrimitives.tObject)({
  url: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.RouteRedirectNavigationRequestResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.RouteAbortParams = (0, _validatorPrimitives.tObject)({
  errorCode: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  requestUrl: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.RouteAbortResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.RouteContinueParams = (0, _validatorPrimitives.tObject)({
  url: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  method: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  headers: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue'))),
  postData: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBinary),
  requestUrl: _validatorPrimitives.tString,
  isFallback: _validatorPrimitives.tBoolean
});
_validatorPrimitives.scheme.RouteContinueResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.RouteFulfillParams = (0, _validatorPrimitives.tObject)({
  status: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  headers: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue'))),
  body: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  isBase64: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  fetchResponseUid: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  requestUrl: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.RouteFulfillResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ResourceTiming = (0, _validatorPrimitives.tObject)({
  startTime: _validatorPrimitives.tNumber,
  domainLookupStart: _validatorPrimitives.tNumber,
  domainLookupEnd: _validatorPrimitives.tNumber,
  connectStart: _validatorPrimitives.tNumber,
  secureConnectionStart: _validatorPrimitives.tNumber,
  connectEnd: _validatorPrimitives.tNumber,
  requestStart: _validatorPrimitives.tNumber,
  responseStart: _validatorPrimitives.tNumber
});
_validatorPrimitives.scheme.ResponseInitializer = (0, _validatorPrimitives.tObject)({
  request: (0, _validatorPrimitives.tChannel)(['Request']),
  url: _validatorPrimitives.tString,
  status: _validatorPrimitives.tNumber,
  statusText: _validatorPrimitives.tString,
  headers: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue')),
  timing: (0, _validatorPrimitives.tType)('ResourceTiming'),
  fromServiceWorker: _validatorPrimitives.tBoolean
});
_validatorPrimitives.scheme.ResponseBodyParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ResponseBodyResult = (0, _validatorPrimitives.tObject)({
  binary: _validatorPrimitives.tBinary
});
_validatorPrimitives.scheme.ResponseSecurityDetailsParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ResponseSecurityDetailsResult = (0, _validatorPrimitives.tObject)({
  value: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('SecurityDetails'))
});
_validatorPrimitives.scheme.ResponseServerAddrParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ResponseServerAddrResult = (0, _validatorPrimitives.tObject)({
  value: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('RemoteAddr'))
});
_validatorPrimitives.scheme.ResponseRawResponseHeadersParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ResponseRawResponseHeadersResult = (0, _validatorPrimitives.tObject)({
  headers: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue'))
});
_validatorPrimitives.scheme.ResponseSizesParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ResponseSizesResult = (0, _validatorPrimitives.tObject)({
  sizes: (0, _validatorPrimitives.tType)('RequestSizes')
});
_validatorPrimitives.scheme.SecurityDetails = (0, _validatorPrimitives.tObject)({
  issuer: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  protocol: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  subjectName: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  validFrom: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  validTo: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.RequestSizes = (0, _validatorPrimitives.tObject)({
  requestBodySize: _validatorPrimitives.tNumber,
  requestHeadersSize: _validatorPrimitives.tNumber,
  responseBodySize: _validatorPrimitives.tNumber,
  responseHeadersSize: _validatorPrimitives.tNumber
});
_validatorPrimitives.scheme.RemoteAddr = (0, _validatorPrimitives.tObject)({
  ipAddress: _validatorPrimitives.tString,
  port: _validatorPrimitives.tNumber
});
_validatorPrimitives.scheme.WebSocketInitializer = (0, _validatorPrimitives.tObject)({
  url: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.WebSocketOpenEvent = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.WebSocketFrameSentEvent = (0, _validatorPrimitives.tObject)({
  opcode: _validatorPrimitives.tNumber,
  data: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.WebSocketFrameReceivedEvent = (0, _validatorPrimitives.tObject)({
  opcode: _validatorPrimitives.tNumber,
  data: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.WebSocketSocketErrorEvent = (0, _validatorPrimitives.tObject)({
  error: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.WebSocketCloseEvent = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BindingCallInitializer = (0, _validatorPrimitives.tObject)({
  frame: (0, _validatorPrimitives.tChannel)(['Frame']),
  name: _validatorPrimitives.tString,
  args: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('SerializedValue'))),
  handle: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['ElementHandle', 'JSHandle']))
});
_validatorPrimitives.scheme.BindingCallRejectParams = (0, _validatorPrimitives.tObject)({
  error: (0, _validatorPrimitives.tType)('SerializedError')
});
_validatorPrimitives.scheme.BindingCallRejectResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.BindingCallResolveParams = (0, _validatorPrimitives.tObject)({
  result: (0, _validatorPrimitives.tType)('SerializedArgument')
});
_validatorPrimitives.scheme.BindingCallResolveResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.DialogInitializer = (0, _validatorPrimitives.tObject)({
  page: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['Page'])),
  type: _validatorPrimitives.tString,
  message: _validatorPrimitives.tString,
  defaultValue: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.DialogAcceptParams = (0, _validatorPrimitives.tObject)({
  promptText: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.DialogAcceptResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.DialogDismissParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.DialogDismissResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.TracingInitializer = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.TracingTracingStartParams = (0, _validatorPrimitives.tObject)({
  name: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  snapshots: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  screenshots: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  live: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.TracingTracingStartResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.TracingTracingStartChunkParams = (0, _validatorPrimitives.tObject)({
  name: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  title: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.TracingTracingStartChunkResult = (0, _validatorPrimitives.tObject)({
  traceName: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.TracingTracingStopChunkParams = (0, _validatorPrimitives.tObject)({
  mode: (0, _validatorPrimitives.tEnum)(['archive', 'discard', 'entries'])
});
_validatorPrimitives.scheme.TracingTracingStopChunkResult = (0, _validatorPrimitives.tObject)({
  artifact: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tChannel)(['Artifact'])),
  entries: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue')))
});
_validatorPrimitives.scheme.TracingTracingStopParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.TracingTracingStopResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ArtifactInitializer = (0, _validatorPrimitives.tObject)({
  absolutePath: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.ArtifactPathAfterFinishedParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ArtifactPathAfterFinishedResult = (0, _validatorPrimitives.tObject)({
  value: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.ArtifactSaveAsParams = (0, _validatorPrimitives.tObject)({
  path: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.ArtifactSaveAsResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ArtifactSaveAsStreamParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ArtifactSaveAsStreamResult = (0, _validatorPrimitives.tObject)({
  stream: (0, _validatorPrimitives.tChannel)(['Stream'])
});
_validatorPrimitives.scheme.ArtifactFailureParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ArtifactFailureResult = (0, _validatorPrimitives.tObject)({
  error: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.ArtifactStreamParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ArtifactStreamResult = (0, _validatorPrimitives.tObject)({
  stream: (0, _validatorPrimitives.tChannel)(['Stream'])
});
_validatorPrimitives.scheme.ArtifactCancelParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ArtifactCancelResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ArtifactDeleteParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ArtifactDeleteResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.StreamInitializer = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.StreamReadParams = (0, _validatorPrimitives.tObject)({
  size: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.StreamReadResult = (0, _validatorPrimitives.tObject)({
  binary: _validatorPrimitives.tBinary
});
_validatorPrimitives.scheme.StreamCloseParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.StreamCloseResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.WritableStreamInitializer = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.WritableStreamWriteParams = (0, _validatorPrimitives.tObject)({
  binary: _validatorPrimitives.tBinary
});
_validatorPrimitives.scheme.WritableStreamWriteResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.WritableStreamCloseParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.WritableStreamCloseResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.CDPSessionInitializer = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.CDPSessionEventEvent = (0, _validatorPrimitives.tObject)({
  method: _validatorPrimitives.tString,
  params: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tAny)
});
_validatorPrimitives.scheme.CDPSessionSendParams = (0, _validatorPrimitives.tObject)({
  method: _validatorPrimitives.tString,
  params: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tAny)
});
_validatorPrimitives.scheme.CDPSessionSendResult = (0, _validatorPrimitives.tObject)({
  result: _validatorPrimitives.tAny
});
_validatorPrimitives.scheme.CDPSessionDetachParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.CDPSessionDetachResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElectronInitializer = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElectronLaunchParams = (0, _validatorPrimitives.tObject)({
  executablePath: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  args: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)(_validatorPrimitives.tString)),
  cwd: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  env: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue'))),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  acceptDownloads: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['accept', 'deny', 'internal-browser-default'])),
  bypassCSP: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  colorScheme: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['dark', 'light', 'no-preference', 'no-override'])),
  extraHTTPHeaders: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue'))),
  geolocation: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    longitude: _validatorPrimitives.tNumber,
    latitude: _validatorPrimitives.tNumber,
    accuracy: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
  })),
  httpCredentials: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    username: _validatorPrimitives.tString,
    password: _validatorPrimitives.tString,
    origin: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
  })),
  ignoreHTTPSErrors: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  locale: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  offline: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  recordHar: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('RecordHarOptions')),
  recordVideo: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    dir: _validatorPrimitives.tString,
    size: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
      width: _validatorPrimitives.tNumber,
      height: _validatorPrimitives.tNumber
    }))
  })),
  strictSelectors: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  timezoneId: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  tracesDir: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.ElectronLaunchResult = (0, _validatorPrimitives.tObject)({
  electronApplication: (0, _validatorPrimitives.tChannel)(['ElectronApplication'])
});
_validatorPrimitives.scheme.ElectronApplicationInitializer = (0, _validatorPrimitives.tObject)({
  context: (0, _validatorPrimitives.tChannel)(['BrowserContext'])
});
_validatorPrimitives.scheme.ElectronApplicationCloseEvent = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElectronApplicationConsoleEvent = (0, _validatorPrimitives.tObject)({
  type: _validatorPrimitives.tString,
  text: _validatorPrimitives.tString,
  args: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tChannel)(['ElementHandle', 'JSHandle'])),
  location: (0, _validatorPrimitives.tObject)({
    url: _validatorPrimitives.tString,
    lineNumber: _validatorPrimitives.tNumber,
    columnNumber: _validatorPrimitives.tNumber
  })
});
_validatorPrimitives.scheme.ElectronApplicationBrowserWindowParams = (0, _validatorPrimitives.tObject)({
  page: (0, _validatorPrimitives.tChannel)(['Page'])
});
_validatorPrimitives.scheme.ElectronApplicationBrowserWindowResult = (0, _validatorPrimitives.tObject)({
  handle: (0, _validatorPrimitives.tChannel)(['ElementHandle', 'JSHandle'])
});
_validatorPrimitives.scheme.ElectronApplicationEvaluateExpressionParams = (0, _validatorPrimitives.tObject)({
  expression: _validatorPrimitives.tString,
  isFunction: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  arg: (0, _validatorPrimitives.tType)('SerializedArgument')
});
_validatorPrimitives.scheme.ElectronApplicationEvaluateExpressionResult = (0, _validatorPrimitives.tObject)({
  value: (0, _validatorPrimitives.tType)('SerializedValue')
});
_validatorPrimitives.scheme.ElectronApplicationEvaluateExpressionHandleParams = (0, _validatorPrimitives.tObject)({
  expression: _validatorPrimitives.tString,
  isFunction: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  arg: (0, _validatorPrimitives.tType)('SerializedArgument')
});
_validatorPrimitives.scheme.ElectronApplicationEvaluateExpressionHandleResult = (0, _validatorPrimitives.tObject)({
  handle: (0, _validatorPrimitives.tChannel)(['ElementHandle', 'JSHandle'])
});
_validatorPrimitives.scheme.ElectronApplicationUpdateSubscriptionParams = (0, _validatorPrimitives.tObject)({
  event: (0, _validatorPrimitives.tEnum)(['console']),
  enabled: _validatorPrimitives.tBoolean
});
_validatorPrimitives.scheme.ElectronApplicationUpdateSubscriptionResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElectronApplicationCloseParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.ElectronApplicationCloseResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidInitializer = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidDevicesParams = (0, _validatorPrimitives.tObject)({
  host: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  port: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  omitDriverInstall: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean)
});
_validatorPrimitives.scheme.AndroidDevicesResult = (0, _validatorPrimitives.tObject)({
  devices: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tChannel)(['AndroidDevice']))
});
_validatorPrimitives.scheme.AndroidSetDefaultTimeoutNoReplyParams = (0, _validatorPrimitives.tObject)({
  timeout: _validatorPrimitives.tNumber
});
_validatorPrimitives.scheme.AndroidSetDefaultTimeoutNoReplyResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidSocketInitializer = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidSocketDataEvent = (0, _validatorPrimitives.tObject)({
  data: _validatorPrimitives.tBinary
});
_validatorPrimitives.scheme.AndroidSocketCloseEvent = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidSocketWriteParams = (0, _validatorPrimitives.tObject)({
  data: _validatorPrimitives.tBinary
});
_validatorPrimitives.scheme.AndroidSocketWriteResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidSocketCloseParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidSocketCloseResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidDeviceInitializer = (0, _validatorPrimitives.tObject)({
  model: _validatorPrimitives.tString,
  serial: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.AndroidDeviceCloseEvent = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidDeviceWebViewAddedEvent = (0, _validatorPrimitives.tObject)({
  webView: (0, _validatorPrimitives.tType)('AndroidWebView')
});
_validatorPrimitives.scheme.AndroidDeviceWebViewRemovedEvent = (0, _validatorPrimitives.tObject)({
  socketName: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.AndroidDeviceWaitParams = (0, _validatorPrimitives.tObject)({
  selector: (0, _validatorPrimitives.tType)('AndroidSelector'),
  state: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['gone'])),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.AndroidDeviceWaitResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidDeviceFillParams = (0, _validatorPrimitives.tObject)({
  selector: (0, _validatorPrimitives.tType)('AndroidSelector'),
  text: _validatorPrimitives.tString,
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.AndroidDeviceFillResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidDeviceTapParams = (0, _validatorPrimitives.tObject)({
  selector: (0, _validatorPrimitives.tType)('AndroidSelector'),
  duration: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.AndroidDeviceTapResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidDeviceDragParams = (0, _validatorPrimitives.tObject)({
  selector: (0, _validatorPrimitives.tType)('AndroidSelector'),
  dest: (0, _validatorPrimitives.tType)('Point'),
  speed: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.AndroidDeviceDragResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidDeviceFlingParams = (0, _validatorPrimitives.tObject)({
  selector: (0, _validatorPrimitives.tType)('AndroidSelector'),
  direction: (0, _validatorPrimitives.tEnum)(['up', 'down', 'left', 'right']),
  speed: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.AndroidDeviceFlingResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidDeviceLongTapParams = (0, _validatorPrimitives.tObject)({
  selector: (0, _validatorPrimitives.tType)('AndroidSelector'),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.AndroidDeviceLongTapResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidDevicePinchCloseParams = (0, _validatorPrimitives.tObject)({
  selector: (0, _validatorPrimitives.tType)('AndroidSelector'),
  percent: _validatorPrimitives.tNumber,
  speed: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.AndroidDevicePinchCloseResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidDevicePinchOpenParams = (0, _validatorPrimitives.tObject)({
  selector: (0, _validatorPrimitives.tType)('AndroidSelector'),
  percent: _validatorPrimitives.tNumber,
  speed: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.AndroidDevicePinchOpenResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidDeviceScrollParams = (0, _validatorPrimitives.tObject)({
  selector: (0, _validatorPrimitives.tType)('AndroidSelector'),
  direction: (0, _validatorPrimitives.tEnum)(['up', 'down', 'left', 'right']),
  percent: _validatorPrimitives.tNumber,
  speed: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.AndroidDeviceScrollResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidDeviceSwipeParams = (0, _validatorPrimitives.tObject)({
  selector: (0, _validatorPrimitives.tType)('AndroidSelector'),
  direction: (0, _validatorPrimitives.tEnum)(['up', 'down', 'left', 'right']),
  percent: _validatorPrimitives.tNumber,
  speed: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  timeout: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.AndroidDeviceSwipeResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidDeviceInfoParams = (0, _validatorPrimitives.tObject)({
  selector: (0, _validatorPrimitives.tType)('AndroidSelector')
});
_validatorPrimitives.scheme.AndroidDeviceInfoResult = (0, _validatorPrimitives.tObject)({
  info: (0, _validatorPrimitives.tType)('AndroidElementInfo')
});
_validatorPrimitives.scheme.AndroidDeviceScreenshotParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidDeviceScreenshotResult = (0, _validatorPrimitives.tObject)({
  binary: _validatorPrimitives.tBinary
});
_validatorPrimitives.scheme.AndroidDeviceInputTypeParams = (0, _validatorPrimitives.tObject)({
  text: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.AndroidDeviceInputTypeResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidDeviceInputPressParams = (0, _validatorPrimitives.tObject)({
  key: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.AndroidDeviceInputPressResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidDeviceInputTapParams = (0, _validatorPrimitives.tObject)({
  point: (0, _validatorPrimitives.tType)('Point')
});
_validatorPrimitives.scheme.AndroidDeviceInputTapResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidDeviceInputSwipeParams = (0, _validatorPrimitives.tObject)({
  segments: (0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('Point')),
  steps: _validatorPrimitives.tNumber
});
_validatorPrimitives.scheme.AndroidDeviceInputSwipeResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidDeviceInputDragParams = (0, _validatorPrimitives.tObject)({
  from: (0, _validatorPrimitives.tType)('Point'),
  to: (0, _validatorPrimitives.tType)('Point'),
  steps: _validatorPrimitives.tNumber
});
_validatorPrimitives.scheme.AndroidDeviceInputDragResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidDeviceLaunchBrowserParams = (0, _validatorPrimitives.tObject)({
  noDefaultViewport: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  viewport: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    width: _validatorPrimitives.tNumber,
    height: _validatorPrimitives.tNumber
  })),
  screen: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    width: _validatorPrimitives.tNumber,
    height: _validatorPrimitives.tNumber
  })),
  ignoreHTTPSErrors: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  javaScriptEnabled: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  bypassCSP: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  userAgent: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  locale: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  timezoneId: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  geolocation: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    longitude: _validatorPrimitives.tNumber,
    latitude: _validatorPrimitives.tNumber,
    accuracy: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
  })),
  permissions: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)(_validatorPrimitives.tString)),
  extraHTTPHeaders: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('NameValue'))),
  offline: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  httpCredentials: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    username: _validatorPrimitives.tString,
    password: _validatorPrimitives.tString,
    origin: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
  })),
  deviceScaleFactor: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  isMobile: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  hasTouch: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  colorScheme: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['dark', 'light', 'no-preference', 'no-override'])),
  reducedMotion: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['reduce', 'no-preference', 'no-override'])),
  forcedColors: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['active', 'none', 'no-override'])),
  acceptDownloads: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['accept', 'deny', 'internal-browser-default'])),
  baseURL: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  recordVideo: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    dir: _validatorPrimitives.tString,
    size: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
      width: _validatorPrimitives.tNumber,
      height: _validatorPrimitives.tNumber
    }))
  })),
  recordHar: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('RecordHarOptions')),
  strictSelectors: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  serviceWorkers: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tEnum)(['allow', 'block'])),
  pkg: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  args: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)(_validatorPrimitives.tString)),
  proxy: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    server: _validatorPrimitives.tString,
    bypass: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    username: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
    password: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
  }))
});
_validatorPrimitives.scheme.AndroidDeviceLaunchBrowserResult = (0, _validatorPrimitives.tObject)({
  context: (0, _validatorPrimitives.tChannel)(['BrowserContext'])
});
_validatorPrimitives.scheme.AndroidDeviceOpenParams = (0, _validatorPrimitives.tObject)({
  command: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.AndroidDeviceOpenResult = (0, _validatorPrimitives.tObject)({
  socket: (0, _validatorPrimitives.tChannel)(['AndroidSocket'])
});
_validatorPrimitives.scheme.AndroidDeviceShellParams = (0, _validatorPrimitives.tObject)({
  command: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.AndroidDeviceShellResult = (0, _validatorPrimitives.tObject)({
  result: _validatorPrimitives.tBinary
});
_validatorPrimitives.scheme.AndroidDeviceInstallApkParams = (0, _validatorPrimitives.tObject)({
  file: _validatorPrimitives.tBinary,
  args: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)(_validatorPrimitives.tString))
});
_validatorPrimitives.scheme.AndroidDeviceInstallApkResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidDevicePushParams = (0, _validatorPrimitives.tObject)({
  file: _validatorPrimitives.tBinary,
  path: _validatorPrimitives.tString,
  mode: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
});
_validatorPrimitives.scheme.AndroidDevicePushResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidDeviceSetDefaultTimeoutNoReplyParams = (0, _validatorPrimitives.tObject)({
  timeout: _validatorPrimitives.tNumber
});
_validatorPrimitives.scheme.AndroidDeviceSetDefaultTimeoutNoReplyResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidDeviceConnectToWebViewParams = (0, _validatorPrimitives.tObject)({
  socketName: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.AndroidDeviceConnectToWebViewResult = (0, _validatorPrimitives.tObject)({
  context: (0, _validatorPrimitives.tChannel)(['BrowserContext'])
});
_validatorPrimitives.scheme.AndroidDeviceCloseParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidDeviceCloseResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.AndroidWebView = (0, _validatorPrimitives.tObject)({
  pid: _validatorPrimitives.tNumber,
  pkg: _validatorPrimitives.tString,
  socketName: _validatorPrimitives.tString
});
_validatorPrimitives.scheme.AndroidSelector = (0, _validatorPrimitives.tObject)({
  checkable: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  checked: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  clazz: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  clickable: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  depth: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber),
  desc: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  enabled: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  focusable: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  focused: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  hasChild: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    selector: (0, _validatorPrimitives.tType)('AndroidSelector')
  })),
  hasDescendant: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({
    selector: (0, _validatorPrimitives.tType)('AndroidSelector'),
    maxDepth: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tNumber)
  })),
  longClickable: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  pkg: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  res: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString),
  scrollable: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  selected: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tBoolean),
  text: (0, _validatorPrimitives.tOptional)(_validatorPrimitives.tString)
});
_validatorPrimitives.scheme.AndroidElementInfo = (0, _validatorPrimitives.tObject)({
  children: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tArray)((0, _validatorPrimitives.tType)('AndroidElementInfo'))),
  clazz: _validatorPrimitives.tString,
  desc: _validatorPrimitives.tString,
  res: _validatorPrimitives.tString,
  pkg: _validatorPrimitives.tString,
  text: _validatorPrimitives.tString,
  bounds: (0, _validatorPrimitives.tType)('Rect'),
  checkable: _validatorPrimitives.tBoolean,
  checked: _validatorPrimitives.tBoolean,
  clickable: _validatorPrimitives.tBoolean,
  enabled: _validatorPrimitives.tBoolean,
  focusable: _validatorPrimitives.tBoolean,
  focused: _validatorPrimitives.tBoolean,
  longClickable: _validatorPrimitives.tBoolean,
  scrollable: _validatorPrimitives.tBoolean,
  selected: _validatorPrimitives.tBoolean
});
_validatorPrimitives.scheme.JsonPipeInitializer = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.JsonPipeMessageEvent = (0, _validatorPrimitives.tObject)({
  message: _validatorPrimitives.tAny
});
_validatorPrimitives.scheme.JsonPipeClosedEvent = (0, _validatorPrimitives.tObject)({
  error: (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tType)('SerializedError'))
});
_validatorPrimitives.scheme.JsonPipeSendParams = (0, _validatorPrimitives.tObject)({
  message: _validatorPrimitives.tAny
});
_validatorPrimitives.scheme.JsonPipeSendResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.JsonPipeCloseParams = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));
_validatorPrimitives.scheme.JsonPipeCloseResult = (0, _validatorPrimitives.tOptional)((0, _validatorPrimitives.tObject)({}));