"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.launchBrowserServer = launchBrowserServer;
exports.printApiJson = printApiJson;
exports.runDriver = runDriver;
exports.runServer = runServer;
var _fs = _interopRequireDefault(require("fs"));
var playwright = _interopRequireWildcard(require("../.."));
var _server = require("../server");
var _transport = require("../protocol/transport");
var _playwrightServer = require("../remote/playwrightServer");
var _processLauncher = require("../utils/processLauncher");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the 'License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable no-console */

function printApiJson() {
  // Note: this file is generated by build-playwright-driver.sh
  console.log(JSON.stringify(require('../../api.json')));
}
function runDriver() {
  const dispatcherConnection = new _server.DispatcherConnection();
  new _server.RootDispatcher(dispatcherConnection, async (rootScope, {
    sdkLanguage
  }) => {
    const playwright = (0, _server.createPlaywright)({
      sdkLanguage
    });
    return new _server.PlaywrightDispatcher(rootScope, playwright);
  });
  const transport = new _transport.PipeTransport(process.stdout, process.stdin);
  transport.onmessage = message => dispatcherConnection.dispatch(JSON.parse(message));
  // Certain Language Binding JSON parsers (e.g. .NET) do not like strings with lone surrogates.
  const isJavaScriptLanguageBinding = !process.env.PW_LANG_NAME || process.env.PW_LANG_NAME === 'javascript';
  const replacer = !isJavaScriptLanguageBinding && String.prototype.toWellFormed ? (key, value) => {
    if (typeof value === 'string')
      // @ts-expect-error
      return value.toWellFormed();
    return value;
  } : undefined;
  dispatcherConnection.onmessage = message => transport.send(JSON.stringify(message, replacer));
  transport.onclose = () => {
    // Drop any messages during shutdown on the floor.
    dispatcherConnection.onmessage = () => {};
    (0, _processLauncher.gracefullyProcessExitDoNotHang)(0);
  };
  // Ignore the SIGINT signal in the driver process so the parent can gracefully close the connection.
  // We still will destruct everything (close browsers and exit) when the transport pipe closes.
  process.on('SIGINT', () => {
    // Keep the process running.
  });
}
async function runServer(options) {
  const {
    port,
    host,
    path = '/',
    maxConnections = Infinity,
    extension
  } = options;
  const server = new _playwrightServer.PlaywrightServer({
    mode: extension ? 'extension' : 'default',
    path,
    maxConnections
  });
  const wsEndpoint = await server.listen(port, host);
  process.on('exit', () => server.close().catch(console.error));
  console.log('Listening on ' + wsEndpoint);
  process.stdin.on('close', () => (0, _processLauncher.gracefullyProcessExitDoNotHang)(0));
}
async function launchBrowserServer(browserName, configFile) {
  let options = {};
  if (configFile) options = JSON.parse(_fs.default.readFileSync(configFile).toString());
  const browserType = playwright[browserName];
  const server = await browserType.launchServer(options);
  console.log(server.wsEndpoint());
}