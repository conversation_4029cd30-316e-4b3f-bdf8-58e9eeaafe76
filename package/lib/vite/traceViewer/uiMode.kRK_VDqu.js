var bt=Object.defineProperty;var Tt=(o,t,e)=>t in o?bt(o,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[t]=e;var F=(o,t,e)=>(Tt(o,typeof t!="symbol"?t+"":t,e),e);import{u as St,r as $,d as kt,_ as jt,e as Et,f as yt,j as r,R as h,s as dt,m as It,g as X,a as N,h as Rt,i as Bt,k as rt,W as Ct,M as Nt,l as Pt,T as Mt,S as Lt,t as Dt,b as Ft,c as At}from"./assets/testServerConnection-tVqhPPSp.js";var Wt={};class nt{constructor(t,e){this._tests=new Map,this._rootSuite=new U("","root"),this._options=e,this._reporter=t}reset(){this._rootSuite.suites=[],this._rootSuite.tests=[],this._tests.clear()}dispatch(t){const{method:e,params:s}=t;if(e==="onConfigure"){this._onConfigure(s.config);return}if(e==="onProject"){this._onProject(s.project);return}if(e==="onBegin"){this._onBegin();return}if(e==="onTestBegin"){this._onTestBegin(s.testId,s.result);return}if(e==="onTestEnd"){this._onTestEnd(s.test,s.result);return}if(e==="onStepBegin"){this._onStepBegin(s.testId,s.resultId,s.step);return}if(e==="onStepEnd"){this._onStepEnd(s.testId,s.resultId,s.step);return}if(e==="onError"){this._onError(s.error);return}if(e==="onStdIO"){this._onStdIO(s.type,s.testId,s.resultId,s.data,s.isBase64);return}if(e==="onEnd")return this._onEnd(s.result);if(e==="onExit")return this._onExit()}_onConfigure(t){var e,s;this._rootDir=t.rootDir,this._config=this._parseConfig(t),(s=(e=this._reporter).onConfigure)==null||s.call(e,this._config)}_onProject(t){let e=this._options.mergeProjects?this._rootSuite.suites.find(s=>s.project().name===t.name):void 0;e||(e=new U(t.name,"project"),this._rootSuite.suites.push(e),e.parent=this._rootSuite),e._project=this._parseProject(t),this._mergeSuitesInto(t.suites,e)}_onBegin(){var t,e;(e=(t=this._reporter).onBegin)==null||e.call(t,this._rootSuite)}_onTestBegin(t,e){var l,a;const s=this._tests.get(t);this._options.clearPreviousResultsWhenTestBegins&&s._clearResults();const i=s._createTestResult(e.id);i.retry=e.retry,i.workerIndex=e.workerIndex,i.parallelIndex=e.parallelIndex,i.setStartTimeNumber(e.startTime),(a=(l=this._reporter).onTestBegin)==null||a.call(l,s,i)}_onTestEnd(t,e){var l,a,f;const s=this._tests.get(t.testId);s.timeout=t.timeout,s.expectedStatus=t.expectedStatus,s.annotations=t.annotations;const i=s._resultsMap.get(e.id);i.duration=e.duration,i.status=e.status,i.errors=e.errors,i.error=(l=i.errors)==null?void 0:l[0],i.attachments=this._parseAttachments(e.attachments),(f=(a=this._reporter).onTestEnd)==null||f.call(a,s,i),i._stepMap=new Map}_onStepBegin(t,e,s){var m,d;const i=this._tests.get(t),l=i._resultsMap.get(e),a=s.parentStepId?l._stepMap.get(s.parentStepId):void 0,f=this._absoluteLocation(s.location),n=new Ut(s,a,f);a?a.steps.push(n):l.steps.push(n),l._stepMap.set(s.id,n),(d=(m=this._reporter).onStepBegin)==null||d.call(m,i,l,n)}_onStepEnd(t,e,s){var f,n;const i=this._tests.get(t),l=i._resultsMap.get(e),a=l._stepMap.get(s.id);a.duration=s.duration,a.error=s.error,(n=(f=this._reporter).onStepEnd)==null||n.call(f,i,l,a)}_onError(t){var e,s;(s=(e=this._reporter).onError)==null||s.call(e,t)}_onStdIO(t,e,s,i,l){var m,d,g,k;const a=l?globalThis.Buffer?Buffer.from(i,"base64"):atob(i):i,f=e?this._tests.get(e):void 0,n=f&&s?f._resultsMap.get(s):void 0;t==="stdout"?(n==null||n.stdout.push(a),(d=(m=this._reporter).onStdOut)==null||d.call(m,a,f,n)):(n==null||n.stderr.push(a),(k=(g=this._reporter).onStdErr)==null||k.call(g,a,f,n))}async _onEnd(t){var e,s;await((s=(e=this._reporter).onEnd)==null?void 0:s.call(e,{status:t.status,startTime:new Date(t.startTime),duration:t.duration}))}_onExit(){var t,e;return(e=(t=this._reporter).onExit)==null?void 0:e.call(t)}_parseConfig(t){const e={...Kt,...t};return this._options.configOverrides&&(e.configFile=this._options.configOverrides.configFile,e.reportSlowTests=this._options.configOverrides.reportSlowTests,e.quiet=this._options.configOverrides.quiet,e.reporter=[...this._options.configOverrides.reporter]),e}_parseProject(t){return{metadata:t.metadata,name:t.name,outputDir:this._absolutePath(t.outputDir),repeatEach:t.repeatEach,retries:t.retries,testDir:this._absolutePath(t.testDir),testIgnore:Q(t.testIgnore),testMatch:Q(t.testMatch),timeout:t.timeout,grep:Q(t.grep),grepInvert:Q(t.grepInvert),dependencies:t.dependencies,teardown:t.teardown,snapshotDir:this._absolutePath(t.snapshotDir),use:{}}}_parseAttachments(t){return t.map(e=>({...e,body:e.base64&&globalThis.Buffer?Buffer.from(e.base64,"base64"):void 0}))}_mergeSuitesInto(t,e){for(const s of t){let i=e.suites.find(l=>l.title===s.title);i||(i=new U(s.title,e._type==="project"?"file":"describe"),i.parent=e,e.suites.push(i)),i.location=this._absoluteLocation(s.location),this._mergeSuitesInto(s.suites,i),this._mergeTestsInto(s.tests,i)}}_mergeTestsInto(t,e){for(const s of t){let i=this._options.mergeTestCases?e.tests.find(l=>l.title===s.title&&l.repeatEachIndex===s.repeatEachIndex):void 0;i||(i=new Ot(s.testId,s.title,this._absoluteLocation(s.location),s.repeatEachIndex),i.parent=e,e.tests.push(i),this._tests.set(i.id,i)),this._updateTest(s,i)}}_updateTest(t,e){return e.id=t.testId,e.location=this._absoluteLocation(t.location),e.retries=t.retries,e.tags=t.tags??[],e}_absoluteLocation(t){return t&&{...t,file:this._absolutePath(t.file)}}_absolutePath(t){if(t!==void 0)return this._options.resolvePath(this._rootDir,t)}}class U{constructor(t,e){this._requireFile="",this.suites=[],this.tests=[],this._parallelMode="none",this.title=t,this._type=e}allTests(){const t=[],e=s=>{for(const i of[...s.suites,...s.tests])i instanceof U?e(i):t.push(i)};return e(this),t}titlePath(){const t=this.parent?this.parent.titlePath():[];return(this.title||this._type!=="describe")&&t.push(this.title),t}project(){var t;return this._project??((t=this.parent)==null?void 0:t.project())}}class Ot{constructor(t,e,s,i){this.fn=()=>{},this.results=[],this.expectedStatus="passed",this.timeout=0,this.annotations=[],this.retries=0,this.tags=[],this.repeatEachIndex=0,this._resultsMap=new Map,this.id=t,this.title=e,this.location=s,this.repeatEachIndex=i}titlePath(){const t=this.parent?this.parent.titlePath():[];return t.push(this.title),t}outcome(){var s,i;const t=[...this.results];for(;((s=t[0])==null?void 0:s.status)==="skipped"||((i=t[0])==null?void 0:i.status)==="interrupted";)t.shift();if(!t.length)return"skipped";const e=t.filter(l=>l.status!=="skipped"&&l.status!=="interrupted"&&l.status!==this.expectedStatus);return e.length?e.length===t.length?"unexpected":"flaky":"expected"}ok(){const t=this.outcome();return t==="expected"||t==="flaky"||t==="skipped"}_clearResults(){this.results=[],this._resultsMap.clear()}_createTestResult(t){const e=new zt(this.results.length);return this.results.push(e),this._resultsMap.set(t,e),e}}class Ut{constructor(t,e,s){this.duration=-1,this.steps=[],this._startTime=0,this.title=t.title,this.category=t.category,this.location=s,this.parent=e,this._startTime=t.startTime}titlePath(){var e;return[...((e=this.parent)==null?void 0:e.titlePath())||[],this.title]}get startTime(){return new Date(this._startTime)}set startTime(t){this._startTime=+t}}class zt{constructor(t){this.parallelIndex=-1,this.workerIndex=-1,this.duration=-1,this.stdout=[],this.stderr=[],this.attachments=[],this.status="skipped",this.steps=[],this.errors=[],this._stepMap=new Map,this._startTime=0,this.retry=t}setStartTimeNumber(t){this._startTime=t}get startTime(){return new Date(this._startTime)}set startTime(t){this._startTime=+t}}const Kt={forbidOnly:!1,fullyParallel:!1,globalSetup:null,globalTeardown:null,globalTimeout:0,grep:/.*/,grepInvert:null,maxFailures:0,metadata:{},preserveOutput:"always",projects:[],reporter:[[Wt.CI?"dot":"list"]],reportSlowTests:{max:5,threshold:15e3},configFile:"",rootDir:"",quiet:!1,shard:null,updateSnapshots:"missing",version:"",workers:0,webServer:null};function Q(o){return o.map(t=>t.s?t.s:new RegExp(t.r.source,t.r.flags))}class at{constructor(t,e,s,i,l){this._treeItemById=new Map,this._treeItemByTestId=new Map;const a=i&&[...i.values()].some(Boolean);this.pathSeparator=l,this.rootItem={kind:"group",subKind:"folder",id:t,title:"",location:{file:"",line:0,column:0},duration:0,parent:void 0,children:[],status:"none",hasLoadErrors:!1},this._treeItemById.set(t,this.rootItem);const f=(n,m,d)=>{for(const g of m.suites){const k=g.title||"<anonymous>";let c=d.children.find(p=>p.kind==="group"&&p.title===k);c||(c={kind:"group",subKind:"describe",id:"suite:"+m.titlePath().join("")+""+k,title:k,location:g.location,duration:0,parent:d,children:[],status:"none",hasLoadErrors:!1},this._addChild(d,c)),f(n,g,c)}for(const g of m.tests){const k=g.title;let c=d.children.find(I=>I.kind!=="group"&&I.title===k);c||(c={kind:"case",id:"test:"+g.titlePath().join(""),title:k,parent:d,children:[],tests:[],location:g.location,duration:0,status:"none",project:void 0,test:void 0,tags:g.tags},this._addChild(d,c));const p=g.results[0];let x="none";(p==null?void 0:p[H])==="scheduled"?x="scheduled":(p==null?void 0:p[H])==="running"?x="running":(p==null?void 0:p.status)==="skipped"?x="skipped":(p==null?void 0:p.status)==="interrupted"?x="none":p&&g.outcome()!=="expected"?x="failed":p&&g.outcome()==="expected"&&(x="passed"),c.tests.push(g);const y={kind:"test",id:g.id,title:n.name,location:g.location,test:g,parent:c,children:[],status:x,duration:g.results.length?Math.max(0,g.results[0].duration):0,project:n};this._addChild(c,y),this._treeItemByTestId.set(g.id,y),c.duration=c.children.reduce((I,S)=>I+S.duration,0)}};for(const n of(e==null?void 0:e.suites)||[])if(!(a&&!i.get(n.title)))for(const m of n.suites){const d=this._fileItem(m.location.file.split(l),!0);f(n.project(),m,d)}for(const n of s){if(!n.location)continue;const m=this._fileItem(n.location.file.split(l),!0);m.hasLoadErrors=!0}}_addChild(t,e){t.children.push(e),e.parent=t,this._treeItemById.set(e.id,e)}filterTree(t,e,s){const i=t.trim().toLowerCase().split(" "),l=[...e.values()].some(Boolean),a=n=>{const m=[...n.tests[0].titlePath(),...n.tests[0].tags].join(" ").toLowerCase();return!i.every(d=>m.includes(d))&&!n.tests.some(d=>s==null?void 0:s.has(d.id))?!1:(n.children=n.children.filter(d=>!l||(s==null?void 0:s.has(d.test.id))||e.get(d.status)),n.tests=n.children.map(d=>d.test),!!n.children.length)},f=n=>{const m=[];for(const d of n.children)d.kind==="case"?a(d)&&m.push(d):(f(d),(d.children.length||d.hasLoadErrors)&&m.push(d));n.children=m};f(this.rootItem)}_fileItem(t,e){if(t.length===0)return this.rootItem;const s=t.join(this.pathSeparator),i=this._treeItemById.get(s);if(i)return i;const l=this._fileItem(t.slice(0,t.length-1),!1),a={kind:"group",subKind:e?"file":"folder",id:s,title:t[t.length-1],location:{file:s,line:0,column:0},duration:0,parent:l,children:[],status:"none",hasLoadErrors:!1};return this._addChild(l,a),a}sortAndPropagateStatus(){ht(this.rootItem)}flattenForSingleProject(){const t=e=>{e.kind==="case"&&e.children.length===1?(e.project=e.children[0].project,e.test=e.children[0].test,e.children=[],this._treeItemByTestId.set(e.test.id,e)):e.children.forEach(t)};t(this.rootItem)}shortenRoot(){let t=this.rootItem;for(;t.children.length===1&&t.children[0].kind==="group"&&t.children[0].subKind==="folder";)t=t.children[0];t.location=this.rootItem.location,this.rootItem=t}testIds(){const t=new Set,e=s=>{s.kind==="case"&&s.tests.forEach(i=>t.add(i.id)),s.children.forEach(e)};return e(this.rootItem),t}fileNames(){const t=new Set,e=s=>{s.kind==="group"&&s.subKind==="file"?t.add(s.id):s.children.forEach(e)};return e(this.rootItem),[...t]}flatTreeItems(){const t=[],e=s=>{t.push(s),s.children.forEach(e)};return e(this.rootItem),t}treeItemById(t){return this._treeItemById.get(t)}collectTestIds(t){return t?Vt(t):new Set}}function ht(o){for(const a of o.children)ht(a);o.kind==="group"&&o.children.sort((a,f)=>a.location.file.localeCompare(f.location.file)||a.location.line-f.location.line);let t=o.children.length>0,e=o.children.length>0,s=!1,i=!1,l=!1;for(const a of o.children)e=e&&a.status==="skipped",t=t&&(a.status==="passed"||a.status==="skipped"),s=s||a.status==="failed",i=i||a.status==="running",l=l||a.status==="scheduled";i?o.status="running":l?o.status="scheduled":s?o.status="failed":e?o.status="skipped":t&&(o.status="passed")}function Vt(o){const t=new Set,e=s=>{var i;s.kind==="case"?s.tests.map(l=>l.id).forEach(l=>t.add(l)):s.kind==="test"?t.add(s.id):(i=s.children)==null||i.forEach(e)};return e(o),t}const H=Symbol("statusEx");class $t{constructor(t){F(this,"rootSuite");F(this,"config");F(this,"loadErrors",[]);F(this,"progress",{total:0,passed:0,failed:0,skipped:0});F(this,"_receiver");F(this,"_lastRunReceiver");F(this,"_lastRunTestCount",0);F(this,"_options");this._receiver=new nt(this._createReporter(),{mergeProjects:!0,mergeTestCases:!0,resolvePath:(e,s)=>e+t.pathSeparator+s,clearPreviousResultsWhenTestBegins:!0}),this._options=t}_createReporter(){return{version:()=>"v2",onConfigure:t=>{this.config=t,this._lastRunReceiver=new nt({onBegin:e=>{this._lastRunTestCount=e.allTests().length,this._lastRunReceiver=void 0}},{mergeProjects:!0,mergeTestCases:!1,resolvePath:(e,s)=>e+this._options.pathSeparator+s})},onBegin:t=>{this.rootSuite||(this.rootSuite=t),this.progress.total=this._lastRunTestCount,this.progress.passed=0,this.progress.failed=0,this.progress.skipped=0,this._options.onUpdate(!0)},onEnd:()=>{this._options.onUpdate(!0)},onTestBegin:(t,e)=>{e[H]="running",this._options.onUpdate()},onTestEnd:(t,e)=>{t.outcome()==="skipped"?++this.progress.skipped:t.outcome()==="unexpected"?++this.progress.failed:++this.progress.passed,e[H]=e.status,this._options.onUpdate()},onError:t=>{var e,s;this.loadErrors.push(t),(s=(e=this._options).onError)==null||s.call(e,t),this._options.onUpdate()},printsToStdio:()=>!1,onStdOut:()=>{},onStdErr:()=>{},onExit:()=>{},onStepBegin:()=>{},onStepEnd:()=>{}}}processListReport(t){this._receiver.reset();for(const e of t)this._receiver.dispatch(e)}processTestReportEvent(t){var e,s,i;(s=(e=this._lastRunReceiver)==null?void 0:e.dispatch(t))==null||s.catch(()=>{}),(i=this._receiver.dispatch(t))==null||i.catch(()=>{})}asModel(){return{rootSuite:this.rootSuite||new U("","root"),config:this.config,loadErrors:this.loadErrors,progress:this.progress}}}const qt=({source:o})=>{const[t,e]=St(),[s,i]=$.useState(kt()),[l]=$.useState(jt(()=>import("./assets/xtermModule-Yt6xwiJ_.js"),__vite__mapDeps([0,1]),import.meta.url).then(f=>f.default)),a=$.useRef(null);return $.useEffect(()=>(Et(i),()=>yt(i)),[]),$.useEffect(()=>{const f=o.write,n=o.clear;return(async()=>{const{Terminal:m,FitAddon:d}=await l,g=e.current;if(!g)return;const k=s==="dark-mode"?Yt:Ht;if(a.current&&a.current.terminal.options.theme===k)return;a.current&&(g.textContent="");const c=new m({convertEol:!0,fontSize:13,scrollback:1e4,fontFamily:"var(--vscode-editor-font-family)",theme:k}),p=new d;c.loadAddon(p);for(const x of o.pending)c.write(x);o.write=x=>{o.pending.push(x),c.write(x)},o.clear=()=>{o.pending=[],c.clear()},c.open(g),p.fit(),a.current={terminal:c,fitAddon:p}})(),()=>{o.clear=n,o.write=f}},[l,a,e,o,s]),$.useEffect(()=>{setTimeout(()=>{a.current&&(a.current.fitAddon.fit(),o.resize(a.current.terminal.cols,a.current.terminal.rows))},250)},[t,o]),r.jsx("div",{"data-testid":"output",className:"xterm-wrapper",style:{flex:"auto"},ref:e})},Ht={foreground:"#383a42",background:"#fafafa",cursor:"#383a42",black:"#000000",red:"#e45649",green:"#50a14f",yellow:"#c18401",blue:"#4078f2",magenta:"#a626a4",cyan:"#0184bc",white:"#a0a0a0",brightBlack:"#000000",brightRed:"#e06c75",brightGreen:"#98c379",brightYellow:"#d19a66",brightBlue:"#4078f2",brightMagenta:"#a626a4",brightCyan:"#0184bc",brightWhite:"#383a42",selectionBackground:"#d7d7d7",selectionForeground:"#383a42"},Yt={foreground:"#f8f8f2",background:"#1e1e1e",cursor:"#f8f8f0",black:"#000000",red:"#ff5555",green:"#50fa7b",yellow:"#f1fa8c",blue:"#bd93f9",magenta:"#ff79c6",cyan:"#8be9fd",white:"#bfbfbf",brightBlack:"#4d4d4d",brightRed:"#ff6e6e",brightGreen:"#69ff94",brightYellow:"#ffffa5",brightBlue:"#d6acff",brightMagenta:"#ff92df",brightCyan:"#a4ffff",brightWhite:"#e6e6e6",selectionBackground:"#44475a",selectionForeground:"#f8f8f2"},et=navigator.userAgent.toLowerCase().includes("windows")?"\\":"/",Qt=({title:o,children:t,setExpanded:e,expanded:s,expandOnTitleClick:i})=>r.jsxs("div",{className:"expandable"+(s?" expanded":""),children:[r.jsxs("div",{className:"expandable-title",onClick:()=>i&&e(!s),children:[r.jsx("div",{className:"codicon codicon-"+(s?"chevron-down":"chevron-right"),style:{cursor:"pointer",color:"var(--vscode-foreground)",marginLeft:"5px"},onClick:()=>!i&&e(!s)}),o]}),s&&r.jsx("div",{style:{marginLeft:25},children:t})]}),Xt=({filterText:o,setFilterText:t,statusFilters:e,setStatusFilters:s,projectFilters:i,setProjectFilters:l,testModel:a,runTests:f})=>{const[n,m]=h.useState(!1),d=h.useRef(null);h.useEffect(()=>{var c;(c=d.current)==null||c.focus()},[]);const g=[...e.entries()].filter(([c,p])=>p).map(([c])=>c).join(" ")||"all",k=[...i.entries()].filter(([c,p])=>p).map(([c])=>c).join(" ")||"all";return r.jsxs("div",{className:"filters",children:[r.jsx(Qt,{expanded:n,setExpanded:m,title:r.jsx("input",{ref:d,type:"search",placeholder:"Filter (e.g. text, @tag)",spellCheck:!1,value:o,onChange:c=>{t(c.target.value)},onKeyDown:c=>{c.key==="Enter"&&f()}})}),r.jsxs("div",{className:"filter-summary",title:"Status: "+g+`
Projects: `+k,onClick:()=>m(!n),children:[r.jsx("span",{className:"filter-label",children:"Status:"})," ",g,r.jsx("span",{className:"filter-label",children:"Projects:"})," ",k]}),n&&r.jsxs("div",{className:"hbox",style:{marginLeft:14,maxHeight:200,overflowY:"auto"},children:[r.jsx("div",{className:"filter-list",children:[...e.entries()].map(([c,p])=>r.jsx("div",{className:"filter-entry",children:r.jsxs("label",{children:[r.jsx("input",{type:"checkbox",checked:p,onClick:()=>{const x=new Map(e);x.set(c,!x.get(c)),s(x)}}),r.jsx("div",{children:c})]})}))}),r.jsx("div",{className:"filter-list",children:[...i.entries()].map(([c,p])=>r.jsx("div",{className:"filter-entry",children:r.jsxs("label",{children:[r.jsx("input",{type:"checkbox",checked:p,onClick:()=>{var I;const x=new Map(i);x.set(c,!x.get(c)),l(x);const y=(I=a==null?void 0:a.config)==null?void 0:I.configFile;y&&dt.setObject(y+":projects",[...x.entries()].filter(([S,z])=>z).map(([S])=>S))}}),r.jsx("div",{children:c||"untitled"})]})}))})]})]})},Jt=({tag:o,style:t,onClick:e})=>r.jsx("span",{className:`tag tag-color-${Zt(o)}`,onClick:e,style:{margin:"6px 0 0 6px",...t},title:`Click to filter by tag: ${o}`,children:o});function Zt(o){let t=0;for(let e=0;e<o.length;e++)t=o.charCodeAt(e)+((t<<8)-t);return Math.abs(t%6)}const Gt=Bt,te=({filterText:o,testModel:t,testServerConnection:e,testTree:s,runTests:i,runningState:l,watchAll:a,watchedTreeIds:f,setWatchedTreeIds:n,isLoading:m,onItemSelected:d,requestedCollapseAllCount:g,setFilterText:k})=>{const[c,p]=h.useState({expandedItems:new Map}),[x,y]=h.useState(),[I,S]=h.useState(g);h.useEffect(()=>{if(I!==g){c.expandedItems.clear();for(const b of s.flatTreeItems())c.expandedItems.set(b.id,!1);S(g),y(void 0),p({...c});return}if(!l||l.itemSelectedByUser)return;let u;const j=b=>{var C;b.children.forEach(j),!u&&b.status==="failed"&&(b.kind==="test"&&l.testIds.has(b.test.id)||b.kind==="case"&&l.testIds.has((C=b.tests[0])==null?void 0:C.id))&&(u=b)};j(s.rootItem),u&&y(u.id)},[l,y,s,I,S,g,c,p]);const{selectedTreeItem:z}=h.useMemo(()=>{if(!t)return{selectedTreeItem:void 0};const u=x?s.treeItemById(x):void 0;let j;u&&(j={file:u.location.file,line:u.location.line,source:{errors:t.loadErrors.filter(C=>{var q;return((q=C.location)==null?void 0:q.file)===u.location.file}).map(C=>({line:C.location.line,message:C.message})),content:void 0}});let b;return(u==null?void 0:u.kind)==="test"?b=u.test:(u==null?void 0:u.kind)==="case"&&u.tests.length===1&&(b=u.tests[0]),d({treeItem:u,testCase:b,testFile:j}),{selectedTreeItem:u}},[d,x,t,s]);h.useEffect(()=>{if(!m)if(a)e==null||e.watchNoReply({fileNames:s.fileNames()});else{const u=new Set;for(const j of f.value){const b=s.treeItemById(j),C=b==null?void 0:b.location.file;C&&u.add(C)}e==null||e.watchNoReply({fileNames:[...u]})}},[m,s,a,f,e]);const A=u=>{y(u.id),i("bounce-if-busy",s.collectTestIds(u))},J=(u,j)=>{if(u.preventDefault(),u.stopPropagation(),u.metaKey||u.ctrlKey){const b=o.split(" ");b.includes(j)?k(b.filter(C=>C!==j).join(" ").trim()):k((o+" "+j).trim())}else k((o.split(" ").filter(b=>!b.startsWith("@")).join(" ")+" "+j).trim())};return r.jsx(Gt,{name:"tests",treeState:c,setTreeState:p,rootItem:s.rootItem,dataTestId:"test-tree",render:u=>r.jsxs("div",{className:"hbox ui-mode-list-item",children:[r.jsxs("div",{className:"ui-mode-list-item-title",children:[r.jsx("span",{title:u.title,children:u.title}),u.kind==="case"?u.tags.map(j=>r.jsx(Jt,{tag:j.slice(1),onClick:b=>J(b,j)},j)):null]}),!!u.duration&&u.status!=="skipped"&&r.jsx("div",{className:"ui-mode-list-item-time",children:It(u.duration)}),r.jsxs(X,{noMinHeight:!0,noShadow:!0,children:[r.jsx(N,{icon:"play",title:"Run",onClick:()=>A(u),disabled:!!l}),r.jsx(N,{icon:"go-to-file",title:"Open in VS Code",onClick:()=>e==null?void 0:e.openNoReply({location:u.location}),style:u.kind==="group"&&u.subKind==="folder"?{visibility:"hidden"}:{}}),!a&&r.jsx(N,{icon:"eye",title:"Watch",onClick:()=>{f.value.has(u.id)?f.value.delete(u.id):f.value.add(u.id),n({...f})},toggled:f.value.has(u.id)})]})]}),icon:u=>Rt(u.status),selectedItem:z,onAccepted:A,onSelected:u=>{l&&(l.itemSelectedByUser=!0),y(u.id)},isError:u=>u.kind==="group"?u.hasLoadErrors:!1,autoExpandDepth:o?5:1,noItemsMessage:m?"Loading…":"No tests"})};function ee(o){return`.playwright-artifacts-${o}`}const se=({item:o,rootDir:t})=>{var k;const[e,s]=h.useState(),[i,l]=h.useState(0),a=h.useRef(null),{outputDir:f}=h.useMemo(()=>({outputDir:o.testCase?ie(o.testCase):void 0}),[o]),[n,m]=h.useState(),d=h.useCallback(c=>m(rt(c)),[m]),g=n?e==null?void 0:e.model.actions.find(c=>rt(c)===n):void 0;return h.useEffect(()=>{var y,I;a.current&&clearTimeout(a.current);const c=(y=o.testCase)==null?void 0:y.results[0];if(!c){s(void 0);return}const p=c&&c.duration>=0&&c.attachments.find(S=>S.name==="trace");if(p&&p.path){lt(p.path).then(S=>s({model:S,isLive:!1}));return}if(!f){s(void 0);return}const x=`${f}/${ee(c.workerIndex)}/traces/${(I=o.testCase)==null?void 0:I.id}.json`;return a.current=setTimeout(async()=>{try{const S=await lt(x);s({model:S,isLive:!0})}catch{s(void 0)}finally{l(i+1)}},500),()=>{a.current&&clearTimeout(a.current)}},[f,o,s,i,l]),r.jsx(Ct,{model:e==null?void 0:e.model,showSourcesFirst:!0,rootDir:t,initialSelection:g,onSelectionChanged:d,fallbackLocation:o.testFile,isLive:e==null?void 0:e.isLive,status:(k=o.treeItem)==null?void 0:k.status},"workbench")},ie=o=>{var t;for(let e=o.parent;e;e=e.parent)if(e.project())return(t=e.project())==null?void 0:t.outputDir};async function lt(o){const t=new URLSearchParams;t.set("trace",o);const s=await(await fetch(`contexts?${t.toString()}`)).json();return new Nt(s)}let ct={cols:80,rows:24};const W={pending:[],clear:()=>{},write:o=>W.pending.push(o),resize:()=>{}},M=new URLSearchParams(window.location.search),oe=M.get("ws"),ft=new URL(`../${oe}`,window.location.toString());ft.protocol=window.location.protocol==="https:"?"wss:":"ws:";const P={args:M.getAll("arg"),grep:M.get("grep")||void 0,grepInvert:M.get("grepInvert")||void 0,projects:M.getAll("project"),workers:M.get("workers")||void 0,timeout:M.has("timeout")?+M.get("timeout"):void 0,headed:M.has("headed"),reporters:M.has("reporter")?M.getAll("reporter"):void 0},ut=navigator.platform==="MacIntel",re=({})=>{var ot;const[o,t]=h.useState(""),[e,s]=h.useState(!1),[i,l]=h.useState(new Map([["passed",!1],["failed",!1],["skipped",!1]])),[a,f]=h.useState(new Map),[n,m]=h.useState(),[d,g]=h.useState(),[k,c]=h.useState({}),[p,x]=h.useState(new Set),[y,I]=h.useState(!1),[S,z]=h.useState(),[A,J]=Pt("watch-all",!1),[u,j]=h.useState({value:new Set}),b=h.useRef(Promise.resolve()),C=h.useRef(new Set),[q,pt]=h.useState(0),[gt,mt]=h.useState(!1),[st,it]=h.useState(!0),[_,wt]=h.useState(),_t=h.useRef(null),Y=h.useCallback(()=>{wt(new Mt(ft.toString()))},[]);h.useEffect(()=>{var v;(v=_t.current)==null||v.focus(),I(!0),Y()},[Y]),h.useEffect(()=>{if(!_)return;const v=[_.onStdio(w=>{if(w.buffer){const E=atob(w.buffer);W.write(E)}else W.write(w.text)}),_.onClose(()=>mt(!0))];return W.resize=(w,E)=>{ct={cols:w,rows:E},_.resizeTerminalNoReply({cols:w,rows:E})},()=>{for(const w of v)w.dispose()}},[_]),h.useEffect(()=>{if(!_)return;let v;const w=new $t({onUpdate:T=>{clearTimeout(v),v=void 0,T?m(w.asModel()):v||(v=setTimeout(()=>{m(w.asModel())},250))},onError:T=>{W.write((T.stack||T.value||"")+`
`)},pathSeparator:et}),E=async()=>{b.current=b.current.then(async()=>{I(!0);try{const T=await _.listTests({projects:P.projects,locations:P.args});w.processListReport(T.report)}catch(T){console.log(T)}finally{I(!1)}})};return m(void 0),I(!0),j({value:new Set}),(async()=>{await _.initialize({interceptStdio:!0,watchTestDirs:!0});const{status:T}=await _.runGlobalSetup({});if(T!=="passed")return;const B=await _.listTests({projects:P.projects,locations:P.args});w.processListReport(B.report),_.onListChanged(E),_.onReport(L=>{w.processTestReportEvent(L)}),I(!1);const{hasBrowsers:R}=await _.checkBrowsers({});it(R)})(),()=>{clearTimeout(v)}},[_]),h.useEffect(()=>{if(!n)return;const{config:v,rootSuite:w}=n,E=v.configFile?dt.getObject(v.configFile+":projects",void 0):void 0,T=new Map(a);for(const B of T.keys())w.suites.find(R=>R.title===B)||T.delete(B);for(const B of w.suites)T.has(B.title)||T.set(B.title,!!(E!=null&&E.includes(B.title)));!E&&T.size&&![...T.values()].includes(!0)&&T.set(T.entries().next().value[0],!0),(a.size!==T.size||[...a].some(([B,R])=>T.get(B)!==R))&&f(T)},[a,n]),h.useEffect(()=>{S&&(n!=null&&n.progress)?g(n.progress):n||g(void 0)},[n,S]);const{testTree:K}=h.useMemo(()=>{if(!n)return{testTree:new at("",new U("","root"),[],a,et)};const v=new at("",n.rootSuite,n.loadErrors,a,et);return v.filterTree(o,i,S==null?void 0:S.testIds),v.sortAndPropagateStatus(),v.shortenRoot(),v.flattenForSingleProject(),x(v.testIds()),{testTree:v}},[o,n,i,a,x,S]),O=h.useCallback((v,w)=>{!_||!n||v==="bounce-if-busy"&&S||(C.current=new Set([...C.current,...w]),b.current=b.current.then(async()=>{var B,R,L;const E=C.current;if(C.current=new Set,!E.size)return;{for(const D of((B=n.rootSuite)==null?void 0:B.allTests())||[])if(E.has(D.id)){D._clearResults();const tt=D._createTestResult("pending");tt[H]="scheduled"}m({...n})}const T="  ["+new Date().toLocaleTimeString()+"]";W.write("\x1B[2m—".repeat(Math.max(0,ct.cols-T.length))+T+"\x1B[22m"),g({total:0,passed:0,failed:0,skipped:0}),z({testIds:E}),await _.runTests({locations:P.args,grep:P.grep,grepInvert:P.grepInvert,testIds:[...E],projects:[...a].filter(([D,tt])=>tt).map(([D])=>D),workers:P.workers,timeout:P.timeout,headed:P.headed,reporters:P.reporters,trace:"on"});for(const D of((R=n.rootSuite)==null?void 0:R.allTests())||[])((L=D.results[0])==null?void 0:L.duration)===-1&&D._clearResults();m({...n}),z(void 0)}))},[a,S,n,_]);h.useEffect(()=>{if(!_)return;const v=_.onTestFilesChanged(w=>{const E=[],T=new Set(w.testFiles);if(A){const B=R=>{const L=R.location.file;L&&T.has(L)&&E.push(...K.collectTestIds(R)),R.kind==="group"&&R.subKind==="folder"&&R.children.forEach(B)};B(K.rootItem)}else for(const B of u.value){const R=K.treeItemById(B),L=R==null?void 0:R.location.file;L&&T.has(L)&&E.push(...K.collectTestIds(R))}O("queue-if-busy",new Set(E))});return()=>v.dispose()},[O,_,K,A,u]),h.useEffect(()=>{if(!_)return;const v=w=>{w.code==="Backquote"&&w.ctrlKey?(w.preventDefault(),s(!e)):w.code==="F5"&&w.shiftKey?(w.preventDefault(),_==null||_.stopTestsNoReply({})):w.code==="F5"&&(w.preventDefault(),O("bounce-if-busy",p))};return addEventListener("keydown",v),()=>{removeEventListener("keydown",v)}},[O,Y,_,p,e]);const V=!!S,Z=h.useRef(null),vt=h.useCallback(v=>{var w;v.preventDefault(),v.stopPropagation(),(w=Z.current)==null||w.showModal()},[]),G=h.useCallback(v=>{var w;v.preventDefault(),v.stopPropagation(),(w=Z.current)==null||w.close()},[]),xt=h.useCallback(v=>{G(v),s(!0),_==null||_.installBrowsers({}).then(async()=>{s(!1);const{hasBrowsers:w}=await(_==null?void 0:_.checkBrowsers({}));it(w)})},[G,_]);return r.jsxs("div",{className:"vbox ui-mode",children:[!st&&r.jsxs("dialog",{ref:Z,children:[r.jsxs("div",{className:"title",children:[r.jsx("span",{className:"codicon codicon-lightbulb"}),"Install browsers"]}),r.jsxs("div",{className:"body",children:["Playwright did not find installed browsers.",r.jsx("br",{}),"Would you like to run `playwright install`?",r.jsx("br",{}),r.jsx("button",{className:"button",onClick:xt,children:"Install"}),r.jsx("button",{className:"button secondary",onClick:G,children:"Dismiss"})]})]}),gt&&r.jsxs("div",{className:"disconnected",children:[r.jsx("div",{className:"title",children:"UI Mode disconnected"}),r.jsxs("div",{children:[r.jsx("a",{href:"#",onClick:()=>window.location.href="/",children:"Reload the page"})," to reconnect"]})]}),r.jsxs(Lt,{sidebarSize:250,minSidebarSize:150,orientation:"horizontal",sidebarIsFirst:!0,settingName:"testListSidebar",children:[r.jsxs("div",{className:"vbox",children:[r.jsxs("div",{className:"vbox"+(e?"":" hidden"),children:[r.jsxs(X,{children:[r.jsx("div",{className:"section-title",style:{flex:"none"},children:"Output"}),r.jsx(N,{icon:"circle-slash",title:"Clear output",onClick:()=>W.clear()}),r.jsx("div",{className:"spacer"}),r.jsx(N,{icon:"close",title:"Close",onClick:()=>s(!1)})]}),r.jsx(qt,{source:W})]}),r.jsx("div",{className:"vbox"+(e?" hidden":""),children:r.jsx(se,{item:k,rootDir:(ot=n==null?void 0:n.config)==null?void 0:ot.rootDir})})]}),r.jsxs("div",{className:"vbox ui-mode-sidebar",children:[r.jsxs(X,{noShadow:!0,noMinHeight:!0,children:[r.jsx("img",{src:"playwright-logo.svg",alt:"Playwright logo"}),r.jsx("div",{className:"section-title",children:"Playwright"}),r.jsx(N,{icon:"color-mode",title:"Toggle color mode",onClick:()=>Dt()}),r.jsx(N,{icon:"refresh",title:"Reload",onClick:()=>Y(),disabled:V||y}),r.jsx(N,{icon:"terminal",title:"Toggle output — "+(ut?"⌃`":"Ctrl + `"),toggled:e,onClick:()=>{s(!e)}}),!st&&r.jsx(N,{icon:"lightbulb-autofix",style:{color:"var(--vscode-list-warningForeground)"},title:"Playwright browsers are missing",onClick:vt})]}),r.jsx(Xt,{filterText:o,setFilterText:t,statusFilters:i,setStatusFilters:l,projectFilters:a,setProjectFilters:f,testModel:n,runTests:()=>O("bounce-if-busy",p)}),r.jsxs(X,{noMinHeight:!0,children:[!V&&!d&&r.jsx("div",{className:"section-title",children:"Tests"}),!V&&d&&r.jsx("div",{"data-testid":"status-line",className:"status-line",children:r.jsxs("div",{children:[d.passed,"/",d.total," passed (",d.passed/d.total*100|0,"%)"]})}),V&&d&&r.jsx("div",{"data-testid":"status-line",className:"status-line",children:r.jsxs("div",{children:["Running ",d.passed,"/",S.testIds.size," passed (",d.passed/S.testIds.size*100|0,"%)"]})}),r.jsx(N,{icon:"play",title:"Run all — F5",onClick:()=>O("bounce-if-busy",p),disabled:V||y}),r.jsx(N,{icon:"debug-stop",title:"Stop — "+(ut?"⇧F5":"Shift + F5"),onClick:()=>_==null?void 0:_.stopTests({}),disabled:!V||y}),r.jsx(N,{icon:"eye",title:"Watch all",toggled:A,onClick:()=>{j({value:new Set}),J(!A)}}),r.jsx(N,{icon:"collapse-all",title:"Collapse all",onClick:()=>{pt(q+1)}})]}),r.jsx(te,{filterText:o,testModel:n,testTree:K,testServerConnection:_,runningState:S,runTests:O,onItemSelected:c,watchAll:A,watchedTreeIds:u,setWatchedTreeIds:j,isLoading:y,requestedCollapseAllCount:q,setFilterText:t})]})]})]})};(async()=>{if(Ft(),window.location.protocol!=="file:"){if(window.location.href.includes("isUnderTest=true")&&await new Promise(o=>setTimeout(o,1e3)),!navigator.serviceWorker)throw new Error(`Service workers are not supported.
Make sure to serve the website (${window.location}) via HTTPS or localhost.`);navigator.serviceWorker.register("sw.bundle.js"),navigator.serviceWorker.controller||await new Promise(o=>{navigator.serviceWorker.oncontrollerchange=()=>o()}),setInterval(function(){fetch("ping")},1e4)}At.render(r.jsx(re,{}),document.querySelector("#root"))})();
function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["./assets/xtermModule-Yt6xwiJ_.js","./xtermModule.0lwXJFHT.css"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
